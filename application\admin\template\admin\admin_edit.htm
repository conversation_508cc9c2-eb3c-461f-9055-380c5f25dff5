{include file="public/layout" /}
<body class="rolecss bodystyle">
<div id="toolTipLayer" style="position: absolute; z-index: 9999; display: none; visibility: visible; left: 95px; top: 573px;"></div>
<div id="append_parent"></div>
<div id="ajaxwaitid"></div>
<div class="page">
    {empty name="$iframe"}
    <div class="fixed-bar">
        <div class="item-title">
            <a class="back_xin" href="javascript:history.back();" title="返回"><i class="iconfont e-fanhui"></i></a>
            <div class="subject">
                <h3>个人信息</h3>
                <h5></h5>
            </div>
        </div>
    </div>
    {/empty}
    <form class="form-horizontal" id="postForm" action="{:url('Admin/admin_edit')}" method="post">
        <input type="hidden" name="admin_id" value="{$info.admin_id}">
        <div class="ncap-form-default">
            <dl class="row">
                <dt class="tit">
                    <label for="head_pic">用户头像</label>
                </dt>
                <dd class="opt">
                    <div class="txpic" onClick="GetUploadify(1,'','allimg','head_pic_call_back');">
                        <input type="hidden" name="head_pic" id="head_pic" value="{$info.head_pic}" />
                        <img id="img_head_pic" src="{$info.head_pic|get_head_pic=###}" />
                        <em>更换头像</em>
                    </div>
                </dd>
            </dl>
            <dl class="row">
                <dt class="tit">
                    <label for="username">用&nbsp;&nbsp;户&nbsp;&nbsp;名</label>
                </dt>
                <dd class="opt">
                    {if condition="empty($admin_info.parent_id)"}
                        <input type="text" name="user_name" value="{$info.user_name}" class="input-txt" placeholder="" autocomplete="off">
                        <p class="notic">为了安全起见，请勿设置网络常见的用户名，且不能与笔名一致，以免被暴露在网站前台</p>
                    {else /}
                        {$info.user_name}
                    {/if}
                </dd>
            </dl>
            <dl class="row">
                <dt class="tit">
                    <label for="password">用户密码</label>
                </dt>
                <dd class="opt">
                    <input type="text" name="password" value="" id="password" autocomplete="off" class="input-txt" placeholder="不修改留空">
                    <p class="notic">密码长度至少5位或以上，建议密码长度设置一些，且以0-9a-zA-Z.@_-!等符号组合！</p>
                    <p id="password_tips"></p>
                </dd>
            </dl>
<!--             <dl class="row">
                <dt class="tit">
                    <label for="password">确认密码</label>
                </dt>
                <dd class="opt">
                    <input type="password" name="password2" value="" id="password2" autocomplete="off" class="input-txt">
                    <p class="notic"></p>
                    <p id="password2_tips"></p>
                </dd>
            </dl> -->
            <dl class="row">
                <dt class="tit">
                    <label for="pen_name">笔名</label>
                </dt>
                <dd class="opt">
                    <input type="text" name="pen_name" value="{$info.pen_name}" id="pen_name" class="input-txt" autocomplete="off">
                    <p class="notic">发布文档后显示责任编辑的名字，禁止与用户名设置一致，以免存在安全隐患</p>
                </dd>
            </dl>
            <dl class="row">
                <dt class="tit">
                    <label for="true_name">真实姓名</label>
                </dt>
                <dd class="opt">
                    <input type="text" name="true_name" value="{$info.true_name|default=''}" id="true_name" class="input-txt" autocomplete="off">
                    <p class="notic"></p>
                </dd>
            </dl>
            <dl class="row">
                <dt class="tit">
                    <label for="mobile">手机号码</label>
                </dt>
                <dd class="opt">
                    <input type="text" name="mobile" value="{$info.mobile}" id="mobile" class="input-txt" autocomplete="off">
                    <p class="notic"></p>
                </dd>
            </dl>
            <dl class="row">
                <dt class="tit">
                    <label for="email">Email邮箱</label>
                </dt>
                <dd class="opt">
                    <input type="text" name="email" value="{$info.email}" id="email" class="input-txt" autocomplete="off">
                    <p class="notic"></p>
                </dd>
            </dl>
            <dl class="row">
                <dt class="tit">
                    <label for="desc">工作内容</label>
                </dt>
                <dd class="opt">
                    <textarea rows="5" cols="60" id="desc" name="desc" style="height:60px;">{$info.desc}</textarea>
                    <span class="err"></span>
                    <p class="notic"></p>
                </dd>
            </dl>

            {if condition="!empty($thirdata['data']['security_wechat_open'])"}
                <dl class="row">
                    <dt class="tit">
                        <label for="bind_wechat">绑定微信</label>
                    </dt>
                    <dd class="opt">
                        <span id="span_wechat_nickname">
                            {empty name="$wechatInfo['wx_id']"}未绑定{else /}{$wechatInfo.nickname}{/empty}
                        </span>
                        <a href="javascript:void(0);" id="wechat_bind" data-logintype="{$thirdata['type']}" onclick="wechat_bind(this);" class="ncap-btn ncap-btn-green" {notempty name='$wechatInfo.wx_id'} style="display: none;" {/notempty}>立即绑定</a>
                        <a href="javascript:void(0);" id="wechat_unbind" data-logintype="{$thirdata['type']}" onclick="wechat_unbind(this);" class="ncap-btn ncap-btn-green" {empty name='$wechatInfo.wx_id'} style="display: none;" {/empty}>解除绑定</a>
                        <input type="hidden" id="is_bind_wechat" value="{empty name='$wechatInfo.wx_id'}bind{else /}unbind{/empty}">
                    </dd>
                </dl>
            {elseif condition="!empty($thirdata['data']['switch'])" /}
                <dl class="row">
                    <dt class="tit">
                        <label for="bind_wechat">绑定微信</label>
                    </dt>
                    <dd class="opt">
                        <span id="span_wechat_nickname">
                            {empty name="$wechatInfo['wx_id']"}未绑定{else /}已绑定公众号{/empty}
                        </span>
                        <a href="javascript:void(0);" id="wechat_bind" data-logintype="{$thirdata['type']}" onclick="wechat_bind(this);" class="ncap-btn ncap-btn-green" {notempty name='$wechatInfo.wx_id'} style="display: none;" {/notempty}>立即绑定</a>
                        <a href="javascript:void(0);" id="wechat_unbind" data-logintype="{$thirdata['type']}" onclick="wechat_unbind(this);" class="ncap-btn ncap-btn-green" {empty name='$wechatInfo.wx_id'} style="display: none;" {/empty}>解除绑定</a>
                        <input type="hidden" id="is_bind_wechat" value="{empty name='$wechatInfo.wx_id'}bind{else /}unbind{/empty}">
                    </dd>
                </dl>
            {/if}

            {if condition="$info.admin_id != $Think.session.admin_info.admin_id AND 0 >= $Think.session.admin_info.role_id"}
            <dl class="row"><dt class="tit"><label><b>管理员权限设置</b></label></dt></dl>
            <dl class="row">
                <dt class="tit">
                    <label for="name">管理员角色组</label>
                </dt>
                <dd class="opt">
                    <p><label><input type="radio" name="role_id" value="-1" onclick="changeRole(-1);" {if condition="-1 == $info.role_id"}checked="checked"{/if} />超级管理员</label></p>
                    {foreach name="admin_role_list" item="role"}
                    <p>
                        <label><input type="radio" name="role_id" value="{$role.id}" onclick="changeRole({$role.id});" {eq name="$role_info.id" value="$role.id"} checked="checked"{/eq} />{$role.name}</label>
                        <!-- &nbsp;<a href="javascript:void;" data-url="{:url('AuthRole/edit', array('id'=>$role.id,'iframe'=>1))}" onclick="addRole(this);">[编辑]</a>&nbsp;&nbsp;<a href="javascript:void;" data-url="{:url('AuthRole/del')}" data-id="{$role.id}" onclick="delfun(this);">[删除]</a> -->
                    </p>
                    {/foreach}
                    <p id="custom_role" style="padding-left: 13px; text-decoration:underline;"><label><a href="javascript:void(0);" data-url="{:url('AuthRole/add', array('iframe'=>1))}" onclick="addRole(this);">自定义</a></label></p>
                </dd>
            </dl>
            <dl class="row"><dt class="tit"><label><b>当前权限组预览</b></label></dt></dl>
<!--             <dl class="row">
                <dt class="tit">
                    <label for="name">语言权限</label>
                </dt>
                <dd class="opt">
                    <label><img class="cboximg" src="__SKIN__/images/{if condition="! empty($role_info.language) && in_array('cn', $role_info.language)"}ok{else /}del{/if}.png" /><input type="checkbox" name="language[]" value="cn" {if condition="! empty($role_info.language) && in_array('cn', $role_info.language)"} checked="checked"{/if} class="none" />简体中文</label>
                    <span class="err"></span>
                    <p class="notic"></p>
                </dd>
            </dl> -->
            <dl class="row">
                <dt class="tit">
                    <label for="name">在线升级</label>
                </dt>
                <dd class="opt">
                    <label><img class="cboximg" src="__SKIN__/images/{eq name="$role_info.online_update" value="1"}ok{else /}del{/eq}.png" /><input type="checkbox" name="online_update" value="1" {eq name="$role_info.online_update" value="1"} checked="checked"{/eq} class="none" />允许操作</label>
                    <span class="err"></span>
                    <p class="notic"></p>
                </dd>
            </dl>
            <dl class="row">
                <dt class="tit">
                    <label for="name">功能地图</label>
                </dt>
                <dd class="opt">
                    <label><img class="cboximg" src="__SKIN__/images/{eq name="$role_info.switch_map" value="1"}ok{else /}del{/eq}.png" /><input type="checkbox" name="switch_map" value="1" {eq name="$role_info.switch_map" value="1"} checked="checked"{/eq} class="none" />允许操作</label>
                    <span class="err"></span>
                    <p class="notic"></p>
                </dd>
            </dl>
            <dl class="row">
                <dt class="tit">
                    <label for="name">文档权限</label>
                </dt>
                <dd class="opt">
                    <label><img class="cboximg" src="__SKIN__/images/{eq name="$role_info.only_oneself" value="1"}ok{else /}del{/eq}.png" /><input type="checkbox" name="only_oneself" value="1" {eq name="$role_info.only_oneself" value="1"} checked="checked"{/eq} class="none" />只允许查看自己发布的文档</label>
                    <span class="err"></span>
                    <p class="notic"></p>
                </dd>
            </dl>
            <dl class="row">
                <dt class="tit">
                    <label for="name">文档审核</label>
                </dt>
                <dd class="opt">
                    <label><img class="cboximg" src="__SKIN__/images/{eq name="$role_info.check_oneself" value="1"}ok{else /}del{/eq}.png" /><input type="checkbox" name="check_oneself" value="1" {eq name="$role_info.check_oneself" value="1"} checked="checked"{/eq} class="none" />发布文档自动通过审核</label>
                    <span class="err"></span>
                    <p class="notic"></p>
                </dd>
            </dl>
            <dl class="row">
                <dt class="tit">
                    <label for="name">操作权限</label>
                </dt>
                <dd class="opt">
                    <p><label><img class="cboximg" src="__SKIN__/images/{if condition="!empty($role_info.cud) && count($role_info.cud)>=4"}ok{else}del{/if}.png" /><input type="checkbox" id="select_cud" {if condition="! empty($role_info.cud) && count($role_info.cud)>=4"} checked="checked"{/if} class="none" />完全控制</label></p>
                    <p><label><img class="cboximg" src="__SKIN__/images/{if condition="!empty($role_info.cud) && in_array('add', $role_info.cud)"}ok{else /}del{/if}.png" /><input type="checkbox" name="cud[]" value="add" {if condition="! empty($role_info.cud) && in_array('add', $role_info.cud)"} checked="checked"{/if} class="none" />添加信息</label></p>
                    <p><label><img class="cboximg" src="__SKIN__/images/{if condition="!empty($role_info.cud) && in_array('edit', $role_info.cud)"}ok{else /}del{/if}.png" /><input type="checkbox" name="cud[]" value="edit" {if condition="! empty($role_info.cud) && in_array('edit', $role_info.cud)"} checked="checked"{/if} class="none" />修改信息</label></p>
                    <p><label><img class="cboximg" src="__SKIN__/images/{if condition="!empty($role_info.cud) && in_array('del', $role_info.cud)"}ok{else /}del{/if}.png" /><input type="checkbox" name="cud[]" value="del" {if condition="! empty($role_info.cud) && in_array('del', $role_info.cud)"} checked="checked"{/if} class="none" />删除信息</label></p>
                    <p><label><img class="cboximg" src="__SKIN__/images/{if condition="!empty($role_info.cud) && in_array('changetableval', $role_info.cud)"}ok{else /}del{/if}.png" /><input type="checkbox" name="cud[]" value="changetableval" {if condition="! empty($role_info.cud) && in_array('changetableval', $role_info.cud)"} checked="checked"{/if} class="none" />审核信息</label></p>
                    <span class="err"></span>
                    <p class="notic"></p>
                </dd>
            </dl>
            <dl class="row">
                <dt class="tit">
                    <label>功能权限</label>
                </dt>
                <dd class="opt">
                    <p>
                        <label><img class="cboximg" src="__SKIN__/images/ok.png" /><input type="checkbox" id="select_all_permission" class="none" />全部选择</label>
                    </p>

                    {foreach name="modules" item="vo"}
                      {foreach name="vo.child" item="vo2"}
                        {if condition="1 == $vo2['is_modules'] AND ! empty($auth_rule_list[$vo2.id])"}
                          {if condition="1002 == $vo2['id']"}
                            <div class="admin_poplistdiv">
                                <h2>{$vo2.name}</h2>
                                {if condition="! empty($arctype_p_html)"}
                                <p>
                                    {$arctype_p_html}
                                </p>
                                {$arctype_child_html}
                                {/if}
                            </div>
                          {else /}
                            <div class="admin_poplistdiv">
                                <h2>{$vo2.name}</h2>
                                <p>
                                    {foreach name="auth_rule_list[$vo2.id]" item="rule"}
                                    <label><img class="cboximg" src="__SKIN__/images/{if condition="! empty($role_info.permission.rules) && in_array($rule.id, $role_info.permission.rules)"}ok{else /}del{/if}.png" /><input type="checkbox" class="none" name="permission[rules][]" value="{$rule.id}" {if condition="! empty($role_info.permission.rules) && in_array($rule.id, $role_info.permission.rules)"} checked="checked"{/if} />{$rule.name}</label>
                                    {/foreach}
                                </p>
                            </div>
                          {/if}
                        {/if}
                      {/foreach}
                    {/foreach}

                    {if condition="! empty($plugins)"}
                    <div class="admin_poplistdiv">
                        <h2>插件应用</h2>
                        <ul>
                            {foreach name="plugins" item="plugin"}
                            <li>
                                <label><img class="cboximg" src="__SKIN__/images/{if condition="! empty($role_info.permission.plugins[$plugin['code']]) || !empty($role_info.permission.plugins[$plugin['code']]['child'])"}ok{else /}del{/if}.png" /><input type="checkbox" name="permission[plugins][{$plugin.code}][code]" value="{$plugin.code}" class="none" {if condition="!empty($role_info.permission.plugins[$plugin['code']]) || !empty($role_info.permission.plugins[$plugin['code']]['child'])"} checked="checked"{/if} />{$plugin.name}</label>
                                {php}$config = json_decode($plugin['config'], true);{/php}
                                {if condition="! empty($config['permission'])"}
                                <p style="padding-left:10px;">
                                    <span class="button level1 switch center_docu"></span>
                                    {foreach $config['permission'] as $index => $text}
                                    <label><img class="cboximg" src="__SKIN__/images/{if condition="! empty($role_info.permission.plugins[$plugin['code']]['child']) && in_array($index, $role_info.permission.plugins[$plugin['code']]['child'])"}ok{else /}del{/if}.png" /><input type="checkbox" class="none" name="permission[plugins][{$plugin.code}][child][]" {if condition="! empty($role_info.permission.plugins[$plugin['code']]['child']) && in_array($index, $role_info.permission.plugins[$plugin['code']]['child'])"} checked="checked"{/if} value="{$index}" />{$text}</label>
                                    {/foreach}
                                </p>
                                {/if}
                            </li>
                            {/foreach}
                        </ul>
                    </div>
                    {/if}
                </dd>
            </dl>
            {/if}
            <div class="bot2">
                {:token('__token_admin_edit__')}
                <a href="JavaScript:void(0);" onclick="adsubmit();" class="ncap-btn-big ncap-btn-green" id="submitBtn">确认提交</a>
            </div>
        </div>
    </form>
</div>
<input type="hidden" name="security_ask_old" id="security_ask_old" value="{$global['security_ask']|default=''}">
<textarea name="admin_role_list" id="admin_role_list" class="none">{$admin_role_list|json_encode}</textarea>
<script type="text/javascript">
    $(function(){
        // 默认全部禁用复选框
        $('#postForm input[type="checkbox"]').attr("disabled","disabled");

        /*超级管理员默认全选复选框*/
        if (0 >= {$info.role_id}) {
            $('#postForm input[type="checkbox"]').prop('checked', 'checked');
            $('#postForm img.cboximg').attr('src', '__SKIN__/images/ok.png');
            // $('#postForm input[name=only_oneself]').val(0);
            // $('#postForm input[name=only_oneself]').parent().find('img.cboximg').attr('src', '__SKIN__/images/del.png');
        }
        /*--end*/

        $('.arctype_bg').bind('click', function(){
            var acid = $(this).next().find('input').val(), input = 'arctype_child_' + acid;
            $('.arctype_child').hide();
            if( $(this).attr('class').indexOf('expandable') == -1 ){
                $(this).removeClass('collapsable').addClass('expandable');
            }else{
                $('.arctype_bg').removeClass('collapsable').addClass('expandable');
                $(this).removeClass('expandable').addClass('collapsable');
                $('#'+input).show();
            }
        });
        $('.arctype_cbox').bind('click', function(){
            var acid = $(this).val(), input = 'arctype_child_' + acid;
            var pid = $(this).data('pid');
            var tpid = $(this).data('tpid');
            if($(this).prop('checked')){
                if (0 < $('input[data-pid="'+pid+'"]:checked').length) {
                    $('.arctype_id_'+pid).prop('checked', 'checked');
                    $('.arctype_id_'+pid).parent().find('img.cboximg').attr('src', '__SKIN__/images/ok.png');
                }
                if (0 < $('#arctype_child_'+tpid).find('input[type="checkbox"]:checked').length) {
                    $('.arctype_id_'+tpid).prop('checked', 'checked');
                    $('.arctype_id_'+tpid).parent().find('img.cboximg').attr('src', '__SKIN__/images/ok.png');
                }
                $('#'+input).find('input[type="checkbox"]').prop('checked', 'checked');
                $('#'+input).find('input[type="checkbox"]').parent().find('img.cboximg').attr('src', '__SKIN__/images/ok.png');
            }else{
                if (1 > $('input[data-pid="'+pid+'"]:checked').length) {
                    $('.arctype_id_'+pid).removeAttr('checked');
                    $('.arctype_id_'+pid).parent().find('img.cboximg').attr('src', '__SKIN__/images/del.png');
                }
                if (1 > $('#arctype_child_'+tpid).find('input[type="checkbox"]:checked').length) {
                    $('.arctype_id_'+tpid).removeAttr('checked');
                    $('.arctype_id_'+tpid).parent().find('img.cboximg').attr('src', '__SKIN__/images/del.png');
                }
                $('#'+input).find('input[type="checkbox"]').removeAttr('checked');
                $('#'+input).find('input[type="checkbox"]').parent().find('img.cboximg').attr('src', '__SKIN__/images/del.png');
            }
        });
        $('#select_cud').bind('click', function(){
            if($(this).prop('checked')){
                $('#postForm input[name^="cud"]').prop('checked', 'checked');
                $('#postForm input[name^="cud"]').parent().find('img.cboximg').attr('src', '__SKIN__/images/ok.png');
            }else{
                $('#postForm input[name^="cud"]').removeAttr('checked');
                $('#postForm input[name^="cud"]').parent().find('img.cboximg').attr('src', '__SKIN__/images/del.png');
            }
        });

        $('#select_all_permission').bind('click', function(){
            if($(this).prop('checked')){
                $('#postForm input[name^="permission"]').prop('checked', 'checked');
                $('#postForm input[name^="permission"]').parent().find('img.cboximg').attr('src', '__SKIN__/images/ok.png');
            }else{
                $('#postForm input[name^="permission"]').removeAttr('checked');
                $('#postForm input[name^="permission"]').parent().find('img.cboximg').attr('src', '__SKIN__/images/del.png');
            }
        });
        $('#postForm input[name^="permission"],#postForm input[name^="cud"]').bind('click', function(){
            hasSelectAll();
        });

        hasSelectAll();
    });

    function hasSelectAll(){
        var c = true;
        $('#postForm input[name^="permission"]').each(function(idx, ele){
            if(! $(ele).prop('checked')){
                c = false;
                return;
            }
        });
        if(c){
            $('#select_all_permission').prop('checked', 'checked');
            $('#select_all_permission').parent().find('img.cboximg').attr('src', '__SKIN__/images/ok.png');
        }else{
            $('#select_all_permission').removeAttr('checked');
            $('#select_all_permission').parent().find('img.cboximg').attr('src', '__SKIN__/images/del.png');
        }

        var c = true;
        $('#postForm input[name^="cud"]').each(function(idx, ele){
            if(! $(ele).prop('checked')){
                c = false;
                return;
            }
        });
        if(c){
            $('#select_cud').prop('checked', 'checked');
            $('#select_cud').parent().find('img.cboximg').attr('src', '__SKIN__/images/ok.png');
        }else{
            $('#select_cud').removeAttr('checked');
            $('#select_cud').parent().find('img.cboximg').attr('src', '__SKIN__/images/del.png');
        }
    }

    function changeRole(value){
        if (-1 == value) {
            $('#postForm input[type="checkbox"]').attr("checked","checked").attr('disabled', 'disabled');
            $('#postForm img.cboximg').attr('src', '__SKIN__/images/ok.png');
            // $('#postForm input[name=only_oneself]').val(0);
            // $('#postForm input[name=only_oneself]').parent().find('img.cboximg').attr('src', '__SKIN__/images/del.png');
            return;
        }
        
        $('#postForm input[name!="role_id"]').removeAttr('checked').removeAttr('disabled');
        $('#postForm img.cboximg').attr('src', '__SKIN__/images/del.png');

        // if(value == "0"){
        //     $('#postForm input[name!="role_id"]').prop('checked', 'checked');
        //     $('#postForm input[name="online_update"]').removeAttr('checked');
        //     $('#postForm input[name="switch_map"]').removeAttr('checked');
        //     $('#postForm input[name="only_oneself"]').removeAttr('checked');
        //     return ;
        // }
        var admin_role_list = JSON.parse($('#admin_role_list').val());
        for(var i in admin_role_list){
            var item = admin_role_list[i];
            if(item.id == value){
                if(item.language){
                    item.language.map(function(row){
                        $('#postForm input[name^="language"][value="'+row+'"]').prop('checked', 'checked');
                        $('#postForm input[name^="language"][value="'+row+'"]').parent().find('img.cboximg').attr('src', '__SKIN__/images/ok.png');
                    });
                }

                if(item.online_update){
                    $('#postForm input[name="online_update"]').prop('checked', 'checked');
                    $('#postForm input[name="online_update"]').parent().find('img.cboximg').attr('src', '__SKIN__/images/ok.png');
                };
                if(item.switch_map){
                    $('#postForm input[name="switch_map"]').prop('checked', 'checked');
                    $('#postForm input[name="switch_map"]').parent().find('img.cboximg').attr('src', '__SKIN__/images/ok.png');
                };
                // if(item.editor_visual){
                //     $('#postForm input[name="editor_visual"]').prop('checked', 'checked');
                //     $('#postForm input[name="editor_visual"]').parent().find('img.cboximg').attr('src', '__SKIN__/images/ok.png');
                // };
                if(item.only_oneself){
                    $('#postForm input[name="only_oneself"]').prop('checked', 'checked');
                    $('#postForm input[name="only_oneself"]').parent().find('img.cboximg').attr('src', '__SKIN__/images/ok.png');
                };
                if(item.check_oneself){
                    $('#postForm input[name="check_oneself"]').prop('checked', 'checked');
                    $('#postForm input[name="check_oneself"]').parent().find('img.cboximg').attr('src', '__SKIN__/images/ok.png');
                };
                if(item.cud){
                    item.cud.map(function(row){
                        $('#postForm input[name^="cud"][value="'+row+'"]').prop('checked', 'checked');
                        $('#postForm input[name^="cud"][value="'+row+'"]').parent().find('img.cboximg').attr('src', '__SKIN__/images/ok.png');
                    });
                }
                if(item.permission){
                    for(var p in item.permission){
                        if(p == 'plugins'){
                            if(item.permission[p]){
                                for(var pluginId in item.permission[p]){
                                    $('#postForm input[name="permission['+p+']['+pluginId+'][id]"][value="'+pluginId+'"]').prop('checked', 'checked');
                                    $('#postForm input[name="permission['+p+']['+pluginId+'][code]"][value="'+pluginId+'"]').parent().find('img.cboximg').attr('src', '__SKIN__/images/ok.png');
                                    if(item.permission[p][pluginId].child){
                                        item.permission[p][pluginId].child.map(function(row){
                                            $('#postForm input[name="permission['+p+']['+pluginId+'][child][]"][value="'+row+'"]').prop('checked', 'checked');
                                            $('#postForm input[name="permission['+p+']['+pluginId+'][child][]"][value="'+row+'"]').parent().find('img.cboximg').attr('src', '__SKIN__/images/ok.png');
                                        });
                                    }
                                }
                            }
                        } else if (p == 'arctype') {
                            item.permission[p].map(function(row){
                                $('#postForm .arctype_id_'+row).prop('checked', 'checked');
                                $('#postForm .arctype_id_'+row).parent().find('img.cboximg').attr('src', '__SKIN__/images/ok.png');
                            });
                        }else{
                            item.permission[p].map(function(row){
                                $('#postForm input[name="permission['+p+'][]"][value="'+row+'"]').prop('checked', 'checked');
                                $('#postForm input[name="permission['+p+'][]"][value="'+row+'"]').parent().find('img.cboximg').attr('src', '__SKIN__/images/ok.png');
                            });
                        }
                    }
                }
                
                hasSelectAll();
                $('#postForm input[type="checkbox"]').attr('disabled', 'disabled');
                break;
            }
        }
    }

    function addRole(obj)
    {
        var url = $(obj).data('url');
        // iframe窗
        layer.open({
            type: 2,
            title: '自定义用户组',
            fixed: true, //不固定
            shadeClose: false,
            shade: layer_shade,
            maxmin: false, //开启最大化最小化按钮
            area: ['90%', '90%'],
            content: url
        });
    }

    function custom_role(str, new_role_id, auth_role_list)
    {
        $('#custom_role').before(str);
        $('#admin_role_list').val(auth_role_list);
        changeRole(new_role_id);
    }

    function head_pic_call_back(fileurl_tmp)
    {
      $("#head_pic").val(fileurl_tmp);
      $("#img_head_pic").attr('src', fileurl_tmp);
    }

    $('#password').keyup(function(){
        var password = $(this).val();
        $.ajax({
            url: "{:url('Admin/ajax_checkPasswordLevel')}",
            type: "POST",
            dataType: "JSON",
            data: {password:password, _ajax:1},
            success: function(res){
                $('#password_tips').removeAttr('class');
                if (1 == res.code) {
                    $('#password_tips').addClass('rank r'+res.data.pwdLevel);
                }
            }
        });
    });
/*
    $('#password2').keyup(function(){
        var password = $('#password').val();
        var password2 = $('#password2').val();
        $('#password2_tips').hide();
        if (password != '' || password2 != '') {
            if (password != password2) {
                $('#password2_tips').html('<font color="red">两次密码输入不一致！</font>').show();
            } else {
                $('#password2_tips').html('<font color="green">校验通过！</font>').show();
            }
        }
    });
*/
    var parentObj = parent.layer.getFrameIndex(window.name); //先得到当前iframe层的索引
    // 判断输入框是否为空
    function adsubmit(){
        if ($('input[name=user_name]').length > 0) {
            var user_name = $('input[name=user_name]').val();
            if($.trim(user_name) == '') {
                showErrorMsg('用户名不能为空！');
                $('input[name=user_name]').focus();
                return false;
            } 
        }
        var password = $.trim($('#password').val());
        if (0 < password.length) {
            {$pwdJsCode|default=''}
            var security_verifyfunc = {$global['security_verifyfunc']|json_encode=###};
            if($.inArray('edit_pwd', security_verifyfunc) != -1){
                if (!ajax_isverify_answer()) {
                    autoload_security();
                    return false;
                }
            }
        }

        layer_loading('正在处理');
        setTimeout(function (){
            $.ajax({
                type : 'post',
                url : "{:url('Admin/admin_edit', ['_ajax'=>1,'iframe'=>$iframe])}",
                data : $('#postForm').serialize(),
                dataType : 'json',
                success : function(res){
                    layer.closeAll();
                    if(res.code == 1){
                        var _parent = parent;
                        _parent.layer.msg(res.msg, {shade: layer_shade, time: 1000}, function(){
                            _parent.window.location.reload();
                        });
                    }else{
                        showErrorMsg(res.msg);
                        $('input[name='+res.data.input_name+']').focus();
                    }
                },
                error: function(e){
                    layer.closeAll();
                    layer.alert(e.responseText, {icon: 5, title:false});
                }
            });
        }, 1);
    }

    /**
     * 绑定微信应用
     * @return {[type]} [description]
     */
    var notifyPolling = null;
    function wechat_bind(obj)
    {
        var logintype = $(obj).attr('data-logintype');
        if ('WechatLogin' == logintype) {
            var gourl = window.location.href;
            var url = "{:url('Admin/wechat_bind', ['admin_id'=>$info['admin_id']], true, true)}";
            url += "&gourl="+encodeURIComponent(gourl);
            var iframes = layer.open({
                type: 2,
                title: '微信扫码绑定',
                fixed: true, //不固定
                shadeClose: false,
                shade: layer_shade,
                // maxmin: true, //开启最大化最小化按钮
                area: ['500px', '460px'],
                content: url
            });
        } else if ('EyouGzhLogin' == logintype) {
            layer_loading('正在加载');
            var admin_id = {$info['admin_id']|default=0};
            $.ajax({
                type: 'POST',
                url: "{:url('Admin/mp_getqrcode')}",
                data: {op:'bind', admin_id:admin_id, _ajax:1},
                dataType: "JSON",
                success: function(res){
                    layer.closeAll();
                    if (1 == res.code) {
                        var html_content = '<img src="https://mp.weixin.qq.com/cgi-bin/showqrcode?ticket='+ res.data.ticket +'" style="width: 250px; height: 250px;"><p style="color: red;padding: 8px 0;font-size: 15px;text-align: center;">打开微信扫一扫</p> <script type="text/javascript"> getNotify("'+admin_id+'","'+res.data.uniqid_scene+'"); <\/script>';
                        layer.open({
                            type: 1,
                            title:'微信扫码绑定',
                            id: 'layer_official_account',
                            shadeClose: false,
                            content: html_content,
                            end: function() {
                                clearNotify();
                            }
                        });
                    }else{
                        showErrorAlert(res.msg);
                    }
                },
                error: function(e){
                    layer.closeAll();
                    showErrorAlert(e.responseText);
                }
            });
        }
    }

    /**
     * 轮询是否绑定官方公众号
     * @return {[type]} [description]
     */
    function getNotify(admin_id, uniqid_scene){
        notifyPolling = setTimeout(function(){
            $.ajax({
                type: 'POST',
                url: "{:url('Admin/mp_bingwxgzhopenid')}",
                data: {op:'bind', admin_id:admin_id, uniqid_scene:uniqid_scene, _ajax:1},
                dataType: "JSON",
                success: function(res){
                    if (1 == res.data.code) {
                        layer.closeAll();
                        clearNotify();
                        $('#span_wechat_nickname').html('已绑定公众号');
                        $('#wechat_bind').hide();
                        $('#wechat_unbind').show();
                        layer.msg('绑定成功', {shade: layer_shade, time: 1000});
                    } else if (0 == res.code) {
                        layer.closeAll();
                        clearNotify();
                        layer.alert(res.msg, {icon: 5, title:false, closeBtn:false}, function(){
                            window.location.reload();
                        });
                    } else if (2 == res.data.code) {
                        getNotify(admin_id, uniqid_scene);
                    }
                },
                error: function(e){
                    layer.closeAll();
                    clearNotify();
                    layer.alert('扫码检测异常，重新尝试！', {icon: 5, title:false, closeBtn:false}, function(){
                        window.location.reload();
                    });
                }
            });
        }, 1800);
    }

    function clearNotify(){
        if(notifyPolling > 0){
            clearTimeout(notifyPolling);
            notifyPolling = null;
        }
    }

    /**
     * 解除绑定微信应用
     * @return {[type]} [description]
     */
    function wechat_unbind(obj)
    {
        var logintype = $(obj).attr('data-logintype');
        if ('WechatLogin' == logintype) {
            var admin_id = {$info['admin_id']|default=0};
            layer_loading('正在处理');
            $.ajax({
                type : 'post',
                url : "{:url('Admin/wechat_unbind_handle')}",
                data : {admin_id:admin_id, _ajax:1},
                dataType : 'json',
                success : function(res){
                    layer.closeAll();
                    if(res.code == 1){
                        $('#span_wechat_nickname').html('未绑定');
                        $('#wechat_bind').show();
                        $('#wechat_unbind').hide();
                        layer.msg(res.msg, {shade: layer_shade, time: 1000}, function(){
                            // window.location.reload();
                        });
                    }else{
                        showErrorAlert(res.msg);
                    }
                },
                error: function(e){
                    layer.closeAll();
                    showErrorAlert(e.responseText);
                }
            });
        } else if ('EyouGzhLogin' == logintype) {
            var admin_id = {$info['admin_id']|default=0};
            layer_loading('正在处理');
            $.ajax({
                type: 'POST',
                url: '{:url("Admin/mp_unbindwx")}',
                data: {admin_id:admin_id, _ajax:1},
                dataType: "JSON",
                success: function(res){
                    layer.closeAll();
                    if (1 == res.code){
                        $('#span_wechat_nickname').html('未绑定');
                        $('#wechat_bind').show();
                        $('#wechat_unbind').hide();
                        layer.msg(res.msg, {shade: layer_shade, time: 1000}, function(){
                            // window.location.reload();
                        });
                    }else{
                        showErrorAlert(res.msg);
                    }
                },
                error: function(e){
                    layer.closeAll();
                    showErrorAlert(e.responseText);
                }
            });
        }
    }
</script>

{include file="public/footer" /}