{include file="public/layout" /}

<body class="bodystyle" style="cursor: default; -moz-user-select: inherit;">
<div id="append_parent"></div>
<div id="ajaxwaitid"></div>
<div class="page min-hg-c-10">
    <div class="flexigrid">
        <div class="mDiv">
            <div class="ftitle">
				{if condition="$main_lang == $admin_lang || !empty($global['language_split'])"}
				    {eq name="$Think.const.CONTROLLER_NAME.'@add'|is_check_access" value="1"}
        				 <div class="fbutton">
        				     <a href="javascript:void(0);" data-href="{:url('AdPosition/add')}" onclick="openFullframe(this, '新增广告', '100%', '100%');">
        				         <div class="add">
        				             <span><i class="layui-icon layui-icon-addition"></i>新增广告</span>
        				         </div>
        				     </a>
        				 </div>
				    {/eq}
				 {/if}
            </div>
            <form id="searchForm" class="navbar-form form-inline" action="{:url('AdPosition/index')}" method="get" onsubmit="layer_loading('正在处理');">
                {$searchform.hidden|default=''}
                <div class="sDiv">
                    <div class="sDiv2">
                        <select name="type" class="select" style="margin:0px 5px;">
                            <option value="">--广告类型--</option>
                            <option value="1" {eq name="$Request.param.type" value="1"}selected{/eq}>图片</option>
                            <option value="2" {eq name="$Request.param.type" value="2"}selected{/eq}>多媒体</option>
                            <option value="3" {eq name="$Request.param.type" value="3"}selected{/eq}>HTML代码</option>
                        </select>
                    </div>
                    <div class="sDiv2">
                        <input type="text" size="30" name="keywords" value="{$Request.param.keywords}" class="qsbox" placeholder="名称搜索...">
                        <input type="submit" class="btn" value="搜索">
						<i class="iconfont e-sousuo"></i>
                    </div>
                </div>
            </form>
        </div>

        <div class="hDiv">
            <div class="hDivBox">
                <table cellspacing="0" cellpadding="0" style="width: 100%">
                    <thead>
                        <tr>
                            {if condition="$main_lang == $admin_lang || !empty($global['language_split'])"}
                                <th class="sign w40" axis="col0">
                                    <div class="tc"><input type="checkbox" class="checkAll"></div>
                                </th>
                            {/if}

                            <th abbr="article_title" axis="col3" class="w60">
                                <div class="tc">ID</div>
                            </th>

                            <th abbr="article_title" axis="col3">
                                <div class="tl text-l10" style="width: 100%">广告名称</div>
                            </th>

                            <th abbr="article_title" axis="col3" class="w100">
                                <div class="tc">广告类型</div>
                            </th>

                            <th abbr="article_title" axis="col3" class="w120">
                                <div class="tc">封面图片</div>
                            </th>

                            <th abbr="article_time" axis="col6" class="w60">
                                <div class="tc">启用</div>
                            </th>

                            <th axis="col1" class="w180">
                                <div class="tc">操作</div>
                            </th>
                        </tr>
                    </thead>
                </table>
            </div>
        </div>

        <div class="bDiv" style="height: auto;">
            <div id="flexigrid" cellpadding="0" cellspacing="0" border="0">
                <table style="width: 100%">
                    <tbody>
                    {empty name="list"}
                        <tr>
                            <td class="no-data" align="center" axis="col0" colspan="50">
                                <div class="no_row">
                                    <div class="no_pic"><img src="__SKIN__/images/null-data.png"></div>
                                </div>
                            </td>
                        </tr>
                    {else/}
                        {foreach name="list" item="vo" key="k" }
                            <tr>
                                {if condition="$main_lang == $admin_lang || !empty($global['language_split'])"}
                                <td class="sign">
                                    <div class="w40 tc"> <input type="checkbox" name="ids[]" value="{$vo.id}"> </div>
                                </td>
                                {/if}
                                <td>
                                    <div class="tc w60">
                                    {eq name='$main_lang' value='$admin_lang'}
                                        {$vo.id}
                                    {else /}
                                        {$main_adv_list[$vo['id']]['id']|default=$vo['id']}
                                    {/eq}
                                    </div>
                                </td>

                                <td align="left" style="width:100%;">
                                    <div class="tl text-l10">
                                        {eq name="$Think.const.CONTROLLER_NAME.'@edit'|is_check_access" value="1"}
                                            <a href="javascript:void(0);" data-href="{:url('AdPosition/edit',array('id'=>$vo['id']))}" data-closereload="1" onclick="openFullframe(this, '编辑广告', '100%', '100%');">{$vo.title}</a>
                                        {else /}
                                            {$vo.title}
                                        {/eq}
                                    </div>
                                </td>
                                <td>
                                    <div class="tc w100"> {$vo.type_name} </div>
                                </td>
                                <td>
                                    <div class="tc w120">
                                        <ul class="adpic">
                                            {volist name="$vo.ad" id="vs" length="1" }
                                                <li>
                                                    <img src="{$vs.litpic}"> {eq name="$vo.type" value="1"}<span>{$vo.ad_count}</span>{/eq}
                                                </li>
                                            {/volist}
                                        </ul>
                                    </div>
                                </td>
                                <td>
                                    <div class="tc w60">
                                        {if condition="$vo['status'] eq 1"}
                                            <span class="yes" {eq name="$Think.const.CONTROLLER_NAME.'@edit'|is_check_access" value="1"}onClick="changeTableVal('ad_position','id','{$vo.id}','status',this);"{/eq} ><i class="fa fa-check-circle"></i>是</span>
                                        {else /}
                                            <span class="no" {eq name="$Think.const.CONTROLLER_NAME.'@edit'|is_check_access" value="1"}onClick="changeTableVal('ad_position','id','{$vo.id}','status',this);"{/eq} ><i class="fa fa-ban"></i>否</span>
                                        {/if}
                                    </div>
                                </td>
                                <td class="operation">
                                    <div class="w180 tc">
                                        {eq name="$Think.const.CONTROLLER_NAME.'@edit'|is_check_access" value="1"}
                                            <a href="javascript:void(0);" data-href="{:url('AdPosition/edit',array('id'=>$vo['id']))}" data-closereload="1" class="btn blue" onclick="openFullframe(this, '编辑广告', '100%', '100%');">编辑</a> <i></i>
                                        {/eq}

                                        {if condition="$main_lang == $admin_lang || !empty($global['language_split'])"}
                                            {eq name="$Think.const.CONTROLLER_NAME.'@del'|is_check_access" value="1"}
                                                <a class="btn red"  href="javascript:void(0);" data-url="{:url('AdPosition/del')}" data-id="{$vo.id}" onClick="delfun(this);">删除</a>
                                            {/eq} <i></i>
                                        {/if}
    									
                                        <a class="btn blue" href="javascript:void(0);" onclick="copyToClipBoard({$main_adv_list[$vo['id']]['id']|default=$vo['id']}, 1, {$vo['type']})">标签调用</a>
                                    </div>
                                </td>
                            </tr>
                        {/foreach}
                    {/empty}
                    </tbody>
                </table>
            </div>
            <div class="iDiv" style="display: none;"></div>
        </div>
        {notempty name="list"}
        <div class="tDiv">
            <div class="tDiv2">
                {if condition="$main_lang == $admin_lang || !empty($global['language_split'])"}
                    <div class="fbutton checkboxall"> <input type="checkbox" class="checkAll"> </div>
                    {eq name="$Think.const.CONTROLLER_NAME.'@del'|is_check_access" value="1"}
                        <div class="fbutton">
                            <a onclick="batch_del(this, 'ids');" data-url="{:url('AdPosition/del')}" class="layui-btn layui-btn-primary">批量删除</a>
                        </div>
                    {/eq}
                {/if}
                <!-- 分页 -->
                {include file="public/page" /}
            </div>
            <div style="clear:both"></div>
        </div>
        {/notempty}
    </div>
</div>

<script type="text/javascript">
    $(function() {
        $('input[name*=ids]').click(function() {
            if ($('input[name*=ids]').length == $('input[name*=ids]:checked').length) {
                $('.checkAll').prop('checked', 'checked');
            } else {
                $('.checkAll').prop('checked', false);
            }
        });
        $('input[type=checkbox].checkAll').click(function() {
            $('input[type=checkbox]').prop('checked', this.checked);
        });
    });
    
    $(document).ready(function() {
        // 表格行点击选中切换
        $('#flexigrid > table>tbody >tr').click(function() {
            $(this).toggleClass('trSelected');
        });

        // 点击刷新数据
        $('.fa-refresh').click(function() {
            location.href = location.href;
        });

        $('#searchForm select[name=type]').change(function(){
            $('#searchForm').submit();
        });
    });

    /**
     * 代码调用js
     * @param id  id
     * @param limit 条数
     */
    function copyToClipBoard(id, limit, type) {
        var advstr = '';
        if (1 == type) {
            advstr = "{eyou:adv pid='" + id + "'}\r\n   <img src='{$"+"field.litpic}' alt='{$"+"field.title}' />\r\n{/eyou:adv";
        } else if (2 == type) {
            advstr = "{eyou:adv pid='" + id + "'}\r\n   <video src='{$"+"field.litpic}' controls preload='auto' oncontextmenu='return fase'></video>\r\n{/eyou:adv";
        } else if (3 == type) {
            advstr = "{eyou:adv pid='" + id + "'}\r\n   {$"+"field.intro}\r\n{/eyou:adv";
        }
        var contentdiv = '<div class="dialog_content" style="margin: 0px; padding: 0px;"><dl style="padding:10px 30px;line-height:30px"><dd>标签 adv 调用：</dd>';
        contentdiv += '<textarea rows="4" cols="60" style="width:400px;height:60px;">' + advstr + '}</textarea>';
        contentdiv += '<dd>JavaScript：</dd>';
        contentdiv += '<dd><input type="text" style=" width:400px;" value="<script type=&quot;text/javascript&quot; src=&quot;http://' + '{$Think.server.http_host}' + '__ROOT_DIR__/index.php?m=api&amp;c=Other&amp;a=other_show&amp;pid=' + id + '&amp;row='+limit+'&quot;><\/script>"></dd>';
        contentdiv += '<dd style="border-top: dotted 1px #E7E7E7; color: #F60;">请将标签adv或JavaScript代码复制并粘贴到对应模板文件中！</dd></dl></div>';
        layer.open({
            title: '代码调用',
            type: 1,
            shade: layer_shade,
            skin: 'layui-layer-demo',
            area: ['480px', '280px'], //宽高
            content: contentdiv
        });
    }

</script>

{include file="public/footer" /}