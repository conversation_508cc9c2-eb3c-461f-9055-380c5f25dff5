<?php if (!defined('THINK_PATH')) exit(); /*a:4:{s:30:"./template/pc/view_product.htm";i:1698660034;s:52:"C:\phpstudy_pro\WWW\www.b.com\template\pc\header.htm";i:1752768631;s:52:"C:\phpstudy_pro\WWW\www.b.com\template\pc\footer.htm";i:1752772014;s:51:"C:\phpstudy_pro\WWW\www.b.com\template\pc\right.htm";i:1752743262;}*/ ?>
<!DOCTYPE html>
<html >
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<meta name="viewport" content="width=device-width, initial-scale=1, minimum-scale=1, maximum-scale=1">
<title><?php echo $eyou['field']['seo_title']; ?></title>
<meta name="description" content="<?php echo $eyou['field']['seo_description']; ?>" />
<meta name="keywords" content="<?php echo $eyou['field']['seo_keywords']; ?>" />
<link href="<?php  $tagGlobal = new \think\template\taglib\eyou\TagGlobal; $__VALUE__ = $tagGlobal->getGlobal("web_ico"); echo $__VALUE__; ?>" rel="shortcut icon" type="image/x-icon" />
<?php  $tagStatic = new \think\template\taglib\eyou\TagStatic; $__VALUE__ = $tagStatic->getStatic("skin/css/style.css","","","",""); echo $__VALUE__;  $tagStatic = new \think\template\taglib\eyou\TagStatic; $__VALUE__ = $tagStatic->getStatic("skin/css/guestbook.css","","","",""); echo $__VALUE__; ?>
</head>
<body>
<div class="container"> <header class="web_head index_web_head">
  <div class="head_layer">
    <div class="layout">
      <figure class="logo"><a href="<?php  $tagGlobal = new \think\template\taglib\eyou\TagGlobal; $__VALUE__ = $tagGlobal->getGlobal("web_basehost"); echo $__VALUE__; ?>"><img src="<?php  $tagGlobal = new \think\template\taglib\eyou\TagGlobal; $__VALUE__ = $tagGlobal->getGlobal("web_logo"); echo $__VALUE__; ?>" alt="<?php  $tagGlobal = new \think\template\taglib\eyou\TagGlobal; $__VALUE__ = $tagGlobal->getGlobal("web_name"); echo $__VALUE__; ?>"></a></figure>
      <nav class="nav_wrap">
        <ul class="head_nav">
          <li <?php if(\think\Request::instance()->param('m') == 'Index'): ?> class="active"<?php endif; ?>><a href="http://***********:8046"><em>Home</em></a></li>
          <?php  if(isset($ui_typeid) && !empty($ui_typeid)) : $typeid = $ui_typeid; else: $typeid = ""; endif; if(empty($typeid) && isset($channelartlist["id"]) && !empty($channelartlist["id"])) : $typeid = intval($channelartlist["id"]); endif;  if(isset($ui_row) && !empty($ui_row)) : $row = $ui_row; else: $row = 100; endif; $tagChannel = new \think\template\taglib\eyou\TagChannel; $_result = $tagChannel->getChannel($typeid, "top", "active", ""); if(is_array($_result) || $_result instanceof \think\Collection || $_result instanceof \think\Paginator): $i = 0; $e = 1;$__LIST__ = is_array($_result) ? array_slice($_result,0, $row, true) : $_result->slice(0, $row, true); if( count($__LIST__)==0 ) : echo htmlspecialchars_decode("");else: foreach($__LIST__ as $key=>$field): $field["typename"] = text_msubstr($field["typename"], 0, 100, false); $__LIST__[$key] = $_result[$key] = $field;$i= intval($key) + 1;$mod = ($i % 2 ); ?>
          <li class="<?php echo $field['currentstyle']; ?>"><a href="<?php echo $field['typeurl']; ?>"><em><?php echo $field['typename']; ?></em></a> <?php if(!(empty($field['children']) || (($field['children'] instanceof \think\Collection || $field['children'] instanceof \think\Paginator ) && $field['children']->isEmpty()))): ?>
            <ul>
              <?php  if(isset($ui_typeid) && !empty($ui_typeid)) : $typeid = $ui_typeid; else: $typeid = ""; endif; if(empty($typeid) && isset($channelartlist["id"]) && !empty($channelartlist["id"])) : $typeid = intval($channelartlist["id"]); endif;  if(isset($ui_row) && !empty($ui_row)) : $row = $ui_row; else: $row = 100; endif;if(is_array($field['children']) || $field['children'] instanceof \think\Collection || $field['children'] instanceof \think\Paginator): $i = 0; $e = 1;$__LIST__ = is_array($field['children']) ? array_slice($field['children'],0,100, true) : $field['children']->slice(0,100, true); if( count($__LIST__)==0 ) : echo htmlspecialchars_decode("");else: foreach($__LIST__ as $key=>$field2): $field2["typename"] = text_msubstr($field2["typename"], 0, 100, false); $__LIST__[$key] = $_result[$key] = $field2;$i= intval($key) + 1;$mod = ($i % 2 ); ?>
              <li> <a href="<?php echo $field2['typeurl']; ?>"><em><?php echo $field2['typename']; ?></em></a> <?php if(!(empty($field2['children']) || (($field2['children'] instanceof \think\Collection || $field2['children'] instanceof \think\Paginator ) && $field2['children']->isEmpty()))): ?>
                <ul>
                  <?php  if(isset($ui_typeid) && !empty($ui_typeid)) : $typeid = $ui_typeid; else: $typeid = ""; endif; if(empty($typeid) && isset($channelartlist["id"]) && !empty($channelartlist["id"])) : $typeid = intval($channelartlist["id"]); endif;  if(isset($ui_row) && !empty($ui_row)) : $row = $ui_row; else: $row = 100; endif;if(is_array($field2['children']) || $field2['children'] instanceof \think\Collection || $field2['children'] instanceof \think\Paginator): $i = 0; $e = 1;$__LIST__ = is_array($field2['children']) ? array_slice($field2['children'],0,100, true) : $field2['children']->slice(0,100, true); if( count($__LIST__)==0 ) : echo htmlspecialchars_decode("");else: foreach($__LIST__ as $key=>$field3): $field3["typename"] = text_msubstr($field3["typename"], 0, 100, false); $__LIST__[$key] = $_result[$key] = $field3;$i= intval($key) + 1;$mod = ($i % 2 ); ?>
                  <li><a href="<?php echo $field3['typeurl']; ?>"><em><?php echo $field3['typename']; ?></em></a></li>
                  <?php ++$e; endforeach; endif; else: echo htmlspecialchars_decode("");endif; $field3 = []; ?>
                </ul>
                <?php endif; ?> </li>
              <?php ++$e; endforeach; endif; else: echo htmlspecialchars_decode("");endif; $field2 = []; ?>
            </ul>
            <?php endif; ?> </li>
          <?php ++$e; endforeach; endif; else: echo htmlspecialchars_decode("");endif; $field = []; ?>
        </ul>
        <!--<div class="head_right">
            <div class="change-language ensemble">
              <div class="change-language-title medium-title">
                <div class="language-flag language-flag-en"><a title="English" href="javascript:;"> <b class="country-flag"></b> <span>English</span> </a> </div>
              </div>
              <div class="change-language-cont sub-content"></div>
            </div>
          </div>--> 
      </nav>
    </div>
  </div>
  <section class="head_top">
    <div class="layout">
      <div class="head-search"> <?php  $tagSearchform = new \think\template\taglib\eyou\TagSearchform; $_result = $tagSearchform->getSearchform("","","","","","default"); if(is_array($_result) || $_result instanceof \think\Collection || $_result instanceof \think\Paginator): $i = 0; $e = 1; $__LIST__ = $_result;if( count($__LIST__)==0 ) : echo htmlspecialchars_decode("");else: foreach($__LIST__ as $key=>$field): $i= intval($key) + 1;$mod = ($i % 2 ); ?>
        <form class="search_form" method="get" action="<?php echo $field['action']; ?>" onsubmit="return searchForm();">
          <input class="search-ipt" type="text" name="keywords" id="keywords" placeholder="" />
          <button class="search-btn" type="submit"></button>
          <span id="btn-search"></span> <?php echo $field['hidden']; ?>
        </form>
        <?php ++$e; endforeach;endif; else: echo htmlspecialchars_decode("");endif; $field = []; ?> </div>
    </div>
  </section>
</header>


  <section class="web_main page_main">
    <div class="layout">
      <aside class="aside">
        <div class="aside-wrap">
          <div class="side-widget">
            <div class="side-tit-bar">
              <h2 class="side-tit"><?php echo gettoptype($eyou['field']['typeid'],'typename'); ?></h2>
            </div>
            <ul class="side-cate">
              <?php  if(isset($ui_typeid) && !empty($ui_typeid)) : $typeid = $ui_typeid; else: $typeid = ""; endif; if(empty($typeid) && isset($channelartlist["id"]) && !empty($channelartlist["id"])) : $typeid = intval($channelartlist["id"]); endif;  if(isset($ui_row) && !empty($ui_row)) : $row = $ui_row; else: $row = 100; endif; $tagChannel = new \think\template\taglib\eyou\TagChannel; $_result = $tagChannel->getChannel($typeid, "first", "active", ""); if(is_array($_result) || $_result instanceof \think\Collection || $_result instanceof \think\Paginator): $i = 0; $e = 1;$__LIST__ = is_array($_result) ? array_slice($_result,0, $row, true) : $_result->slice(0, $row, true); if( count($__LIST__)==0 ) : echo htmlspecialchars_decode("");else: foreach($__LIST__ as $key=>$field): $field["typename"] = text_msubstr($field["typename"], 0, 100, false); $__LIST__[$key] = $_result[$key] = $field;$i= intval($key) + 1;$mod = ($i % 2 ); ?>
              <li <?php if($i == '1'): ?>class="nav-current"<?php endif; ?>><a href="<?php echo $field['typeurl']; ?>"><?php echo $field['typename']; ?></a> <?php if(!(empty($field['children']) || (($field['children'] instanceof \think\Collection || $field['children'] instanceof \think\Paginator ) && $field['children']->isEmpty()))): ?>
                <ul class="sub-menu">
                  <?php  if(isset($ui_typeid) && !empty($ui_typeid)) : $typeid = $ui_typeid; else: $typeid = ""; endif; if(empty($typeid) && isset($channelartlist["id"]) && !empty($channelartlist["id"])) : $typeid = intval($channelartlist["id"]); endif;  if(isset($ui_row) && !empty($ui_row)) : $row = $ui_row; else: $row = 100; endif;if(is_array($field['children']) || $field['children'] instanceof \think\Collection || $field['children'] instanceof \think\Paginator): $i = 0; $e = 1;$__LIST__ = is_array($field['children']) ? array_slice($field['children'],0,100, true) : $field['children']->slice(0,100, true); if( count($__LIST__)==0 ) : echo htmlspecialchars_decode("");else: foreach($__LIST__ as $key=>$field2): $field2["typename"] = text_msubstr($field2["typename"], 0, 100, false); $__LIST__[$key] = $_result[$key] = $field2;$i= intval($key) + 1;$mod = ($i % 2 ); ?>
                  <li><a href="<?php echo $field2['typeurl']; ?>"><?php echo $field2['typename']; ?></a></li>
                  <?php ++$e; endforeach; endif; else: echo htmlspecialchars_decode("");endif; $field2 = []; ?>
                </ul>
                <?php endif; ?> </li>
              <?php ++$e; endforeach; endif; else: echo htmlspecialchars_decode("");endif; $field = []; ?>
            </ul>
          </div>
          <div class="side-widget">
            <div class="side-tit-bar">
              <h2 class="side-tit">Featured Products</h2>
            </div>
            <div class="side-product-items">
              <div class="items_content">
                <div class="side_slider">
                  <ul class="swiper-wrapper">
                    <?php  if(isset($ui_typeid) && !empty($ui_typeid)) : $typeid = $ui_typeid; else: $typeid = ""; endif; if(empty($typeid) && isset($channelartlist["id"]) && !empty($channelartlist["id"])) : $typeid = intval($channelartlist["id"]); endif;  if(isset($ui_row) && !empty($ui_row)) : $row = $ui_row; else: $row = 10; endif; $modelid = ""; $param = array(      "typeid"=> $typeid,      "notypeid"=> "",      "flag"=> "",      "noflag"=> "",      "channel"=> $modelid,      "joinaid"=> "",      "keyword"=> "",      "release"=> "off",      "idlist"=> "",      "idrange"=> "",      "aid"=> "", ); $tag = array (
  'titlelen' => '20',
  'infolen' => '100',
  'r' => 'eyou:artlist',
); $tagArclist = new \think\template\taglib\eyou\TagArclist; $_result = $tagArclist->getArclist($param, $row, "", "","desc","",$tag,"0","on","off","","");if(!empty($_result["list"]) && (is_array($_result["list"]) || $_result["list"] instanceof \think\Collection || $_result["list"] instanceof \think\Paginator)): $i = 0; $e = 1; $__LIST__ = is_array($_result["list"]) ? array_slice($_result["list"],0, $row, true) : $_result["list"]->slice(0, $row, true);  $__TAG__ = $_result["tag"];if( count($__LIST__)==0 ) : echo htmlspecialchars_decode("");else: foreach($__LIST__ as $key=>$field): $aid = $field["aid"];$users_id = $field["users_id"];$field["title"] = text_msubstr($field["title"], 0, 20, false);if($field["is_b"] == 1) : $field["title"] = "<strong>".$field["title"]."</strong>";endif;$field["seo_description"] = text_msubstr($field["seo_description"], 0, 100, true);$i= intval($key) + 1;$mod = ($i % 2 ); ?>
                    <li class="swiper-slide gm-sep side_product_item">
                      <figure> <a href="<?php echo $field['arcurl']; ?>" class="item-img"><img src="<?php echo $field['litpic']; ?>" alt="<?php echo $field['title']; ?>"/></a>
                        <figcaption>
                          <h3 class="item_title"><a href="<?php echo $field['arcurl']; ?>"><?php echo $field['title']; ?></a></h3>
                        </figcaption>
                      </figure>
                    </li>
                    <?php ++$e; $aid = 0; $users_id = 0; endforeach; endif; else: echo htmlspecialchars_decode("");endif; $field = []; ?>
                  </ul>
                </div>
                <div class="btn-prev"></div>
                <div class="btn-next"></div>
              </div>
            </div>
          </div>
        </div>
      </aside>
      <section class="main">
        <h2 class="page_title"><?php echo $eyou['field']['title']; ?></h2>
        <div class="product-intro">
          <div class="product-view"> <?php if(is_array($eyou['field']['image_list']) || $eyou['field']['image_list'] instanceof \think\Collection || $eyou['field']['image_list'] instanceof \think\Paginator): $i = 0; $e = 1;$__LIST__ = is_array($eyou['field']['image_list']) ? array_slice($eyou['field']['image_list'],0,1, true) : $eyou['field']['image_list']->slice(0,1, true); if( count($__LIST__)==0 ) : echo htmlspecialchars_decode("");else: foreach($__LIST__ as $key=>$field): $i= intval($key) + 1;$mod = ($i % 2 ); ?>
            <div class="product-image"> <a class="cloud-zoom" id="zoom1" data-zoom="adjustX:0, adjustY:0" href="<?php echo $field['image_url']; ?>" title="<?php echo $field['intro']; ?>"> <img src="<?php echo $field['image_url']; ?>" itemprop="image" title="<?php echo $field['intro']; ?>" alt="<?php echo $field['intro']; ?>" style="width:100%" /></a> </div>
            <?php echo isset($field["ey_1563185380"])?$field["ey_1563185380"]:""; ?><?php echo (1 == $e && isset($field["ey_1563185376"]))?$field["ey_1563185376"]:""; ++$e; endforeach; endif; else: echo htmlspecialchars_decode("");endif; $field = []; ?>
            <div  style="position:relative; width:100%;">
              <div class="image-additional">
                <ul class="swiper-wrapper">
                  <?php if(is_array($eyou['field']['image_list']) || $eyou['field']['image_list'] instanceof \think\Collection || $eyou['field']['image_list'] instanceof \think\Paginator): $i = 0; $e = 1; $__LIST__ = $eyou['field']['image_list'];if( count($__LIST__)==0 ) : echo htmlspecialchars_decode("");else: foreach($__LIST__ as $key=>$field): $i= intval($key) + 1;$mod = ($i % 2 ); ?>
                  <li class="swiper-slide image-item <?php if($i == '1'): ?>current<?php endif; ?>"> <a class="cloud-zoom-gallery item"  href="<?php echo $field['image_url']; ?>" data-zoom="useZoom:zoom1, smallImage:<?php echo $field['image_url']; ?>" title="<?php echo $field['intro']; ?>"><img src="<?php echo $field['image_url']; ?>" title="<?php echo $field['intro']; ?>" alt="<?php echo $field['intro']; ?>" /></a> </li>
                  <?php echo isset($field["ey_1563185380"])?$field["ey_1563185380"]:""; ?><?php echo (1 == $e && isset($field["ey_1563185376"]))?$field["ey_1563185376"]:""; ++$e; endforeach; endif; else: echo htmlspecialchars_decode("");endif; $field = []; ?>
                </ul>
                <div class="swiper-pagination swiper-pagination-white"></div>
              </div>
              <div class="swiper-button-next swiper-button-white"></div>
              <div class="swiper-button-prev swiper-button-white"></div>
            </div>
          </div>
          <div class="product-summary">
            <div class="product-meta">
              <h3>Short Description:</h3>
              <p><?php echo $eyou['field']['seo_description']; ?>...</p>
              <br>
              <ul>
                <?php  if(empty($aid)) : $aid_tmp = ""; endif;  $taid = 0;  if(!empty($aid_tmp)) : $taid = $aid_tmp; elseif(!empty($aid)) : $taid = $aid; endif; $tagAttribute = new \think\template\taglib\eyou\TagAttribute; $_result = $tagAttribute->getAttribute($taid, "newattr", $eyou['field']['attrlist_id']); if(is_array($_result) || $_result instanceof \think\Collection || $_result instanceof \think\Paginator): $i = 0; $e = 1; $__LIST__ = is_array($_result) ? array_slice($_result,0, 100, true) : $_result->slice(0, 100, true); if( count($__LIST__)==0 ) : echo htmlspecialchars_decode("");else: foreach($__LIST__ as $key=>$attr): $i= intval($key) + 1;$mod = ($i % 2 ); ?>
                <li><em><?php echo $attr['name']; ?>: </em> <span class="item-val"><?php echo $attr['value']; ?></span></li>
                <?php ++$e; endforeach;endif; else: echo htmlspecialchars_decode("");endif; $attr = []; ?>
              </ul>
            </div>
            <div class="gm-sep product-btn-wrap"> <a href="javascript:" class="email">Send email to us</a> <a href="<?php echo $eyou['field']['fjxz']; ?>" class="pdf" target="_blank">Download as PDF</a> </div>
            <!--<div class="share-this"><img src="<?php  $tagGlobal = new \think\template\taglib\eyou\TagGlobal; $__VALUE__ = $tagGlobal->getGlobal("web_templets_pc"); echo $__VALUE__; ?>/skin/images/share_this.png" alt=""></div>--> 
          </div>
        </div>
        <div class="tab-content-wrap product-detail">
          <div class="gm-sep tab-title-bar detail-tabs">
            <h2 class="tab-title  title current"><span>Product Detail</span></h2>
            <h2 class="tab-title  title"><span>Product Tags</span></h2>
          </div>
          <div class="tab-panel-wrap">
            <div class="tab-panel disabled">
              <div class="tab-panel-content entry">
                <div class="fl-rich-text"> <?php echo $eyou['field']['content']; ?> </div>
              </div>
            </div>
            <div class="tab-panel disabled entry">
              <div class="tab-panel-content"> <?php echo $eyou['field']['warranty']; ?> </div>
            </div>
          </div>
        </div>
        <div class="inquiry-form-wrap">
          <div class="content-wrap form" style="width: 100%;height: 100%;overflow: hidden;">
            <div class="ch_form_wrap">
              <div class="email_point"> </div>
              <h1 class="title"><em>Leave Your Message</em> </h1>
              <?php  $typeid = "3"; if(empty($typeid) && isset($channelartlist["id"]) && !empty($channelartlist["id"])) : $typeid = intval($channelartlist["id"]); endif;  $tagGuestbookform = new \think\template\taglib\eyou\TagGuestbookform; $_result = $tagGuestbookform->getGuestbookform($typeid, "default", "", 1); if(is_array($_result) || $_result instanceof \think\Collection || $_result instanceof \think\Paginator): $i = 0; $e = 1; $__LIST__ = $_result;if( count($__LIST__)==0 ) : echo htmlspecialchars_decode("");else: foreach($__LIST__ as $key=>$field): $i= intval($key) + 1;$mod = ($i % 2 ); ?>
              <form method="POST" action="<?php echo $field['action']; ?>" <?php echo $field['formhidden']; ?> onsubmit="<?php echo $field['submit']; ?>" class="ch_form">
                <ul>
                  <li class="item item_name">
                    <input type="text" name="<?php echo $field['attr_11']; ?>" id="attr_11" placeholder="* <?php echo $field['itemname_11']; ?>" value="<?php echo $eyou['field']['title']; ?>"/>
                  </li>
                  <li class="item item_name">
                    <input type="text" name="<?php echo $field['attr_8']; ?>" id="attr_8" placeholder="* <?php echo $field['itemname_8']; ?>"/>
                  </li>
                  <li class="item item_email">
                    <input type="text" name="<?php echo $field['attr_9']; ?>" id="attr_9" placeholder="* <?php echo $field['itemname_9']; ?>"/>
                  </li>
                  <li class="item item_phone">
                    <input type="text" name="<?php echo $field['attr_10']; ?>" id="attr_10" placeholder="<?php echo $field['itemname_10']; ?>"/>
                  </li>
                  <li class="item item_message">
                    <textarea name="<?php echo $field['attr_12']; ?>" id="attr_12" placeholder="* <?php echo $field['itemname_12']; ?>"></textarea>
                  </li>
                </ul>
                <div class="item item_submit">
                  <input type="submit" value="Send" name="submit" class="submit_btn gtm_submit_btn" style="margin:0" />
                </div>
                <br />
                <?php echo $field['hidden']; ?>
              </form>
              <?php ++$e; endforeach;endif; else: echo htmlspecialchars_decode("");endif; $field = []; ?> </div>
          </div>
          <div class="ad_prompt">Write your message here and send it to us</div>
        </div>
        <div class="goods-may-like">
          <h2 class="title">Products categories</h2>
          <div class="layer-bd">
            <div class="swiper-slider">
              <ul class="swiper-wrapper">
                <?php  if(isset($ui_typeid) && !empty($ui_typeid)) : $typeid = $ui_typeid; else: $typeid = ""; endif; if(empty($typeid) && isset($channelartlist["id"]) && !empty($channelartlist["id"])) : $typeid = intval($channelartlist["id"]); endif;  if(isset($ui_row) && !empty($ui_row)) : $row = $ui_row; else: $row = 8; endif; $modelid = ""; $param = array(      "typeid"=> $typeid,      "notypeid"=> "",      "flag"=> "",      "noflag"=> "",      "channel"=> $modelid,      "joinaid"=> "",      "keyword"=> "",      "release"=> "off",      "idlist"=> "",      "idrange"=> "",      "aid"=> "", ); $tag = array (
  'titlelen' => '20',
  'infolen' => '100',
  'loop' => '8',
  'r' => 'eyou:artlist',
  'row' => '8',
); $tagArclist = new \think\template\taglib\eyou\TagArclist; $_result = $tagArclist->getArclist($param, $row, "", "","desc","",$tag,"0","on","off","","");if(!empty($_result["list"]) && (is_array($_result["list"]) || $_result["list"] instanceof \think\Collection || $_result["list"] instanceof \think\Paginator)): $i = 0; $e = 1; $__LIST__ = is_array($_result["list"]) ? array_slice($_result["list"],0, $row, true) : $_result["list"]->slice(0, $row, true);  $__TAG__ = $_result["tag"];if( count($__LIST__)==0 ) : echo htmlspecialchars_decode("");else: foreach($__LIST__ as $key=>$field): $aid = $field["aid"];$users_id = $field["users_id"];$field["title"] = text_msubstr($field["title"], 0, 20, false);if($field["is_b"] == 1) : $field["title"] = "<strong>".$field["title"]."</strong>";endif;$field["seo_description"] = text_msubstr($field["seo_description"], 0, 100, true);$i= intval($key) + 1;$mod = ($i % 2 ); ?>
                <li class="swiper-slide wow fadeInUpA product_item" data-wow-delay=".<?php echo $i; ?>s">
                  <figure> <span class="item_img"><img src="<?php echo $field['litpic']; ?>" alt="<?php echo $field['title']; ?>"/><a href="<?php echo $field['arcurl']; ?>"></a></span>
                    <figcaption>
                      <h3 class="item_title"><a href="<?php echo $field['arcurl']; ?>"><?php echo $field['title']; ?></a></h3>
                    </figcaption>
                  </figure>
                </li>
                <?php ++$e; $aid = 0; $users_id = 0; endforeach; endif; else: echo htmlspecialchars_decode("");endif; $field = []; ?>
              </ul>
            </div>
            <div class="swiper-control"> <span class="swiper-button-prev"></span> <span class="swiper-button-next"></span> </div>
          </div>
        </div>
      </section>
    </div>
  </section>
  <footer class="web_footer">
  <section class="foot_service">
    <div class="layout">
      <figure class="foot_logo"><img src="<?php  $tagGlobal = new \think\template\taglib\eyou\TagGlobal; $__VALUE__ = $tagGlobal->getGlobal("web_logo"); echo $__VALUE__; ?>" alt="<?php  $tagGlobal = new \think\template\taglib\eyou\TagGlobal; $__VALUE__ = $tagGlobal->getGlobal("web_attrname_6"); echo $__VALUE__; ?>"></figure>
      <ul class="foot_nav">
        <?php  if(isset($ui_typeid) && !empty($ui_typeid)) : $typeid = $ui_typeid; else: $typeid = ""; endif; if(empty($typeid) && isset($channelartlist["id"]) && !empty($channelartlist["id"])) : $typeid = intval($channelartlist["id"]); endif;  if(isset($ui_row) && !empty($ui_row)) : $row = $ui_row; else: $row = 100; endif; $tagChannel = new \think\template\taglib\eyou\TagChannel; $_result = $tagChannel->getChannel($typeid, "top", "active", ""); if(is_array($_result) || $_result instanceof \think\Collection || $_result instanceof \think\Paginator): $i = 0; $e = 1;$__LIST__ = is_array($_result) ? array_slice($_result,0, $row, true) : $_result->slice(0, $row, true); if( count($__LIST__)==0 ) : echo htmlspecialchars_decode("");else: foreach($__LIST__ as $key=>$field): $field["typename"] = text_msubstr($field["typename"], 0, 100, false); $__LIST__[$key] = $_result[$key] = $field;$i= intval($key) + 1;$mod = ($i % 2 ); ?>
        <li><a href="<?php echo $field['typeurl']; ?>"><?php echo $field['typename']; ?></a></li>
        <?php ++$e; endforeach; endif; else: echo htmlspecialchars_decode("");endif; $field = []; ?>
      </ul>
      <ul class="foot_sns">
        <li><a href=""><img src="<?php  $tagGlobal = new \think\template\taglib\eyou\TagGlobal; $__VALUE__ = $tagGlobal->getGlobal("web_templets_pc"); echo $__VALUE__; ?>/skin/images/sns01.png" alt=""></a></li>
        <li><a href=""><img src="<?php  $tagGlobal = new \think\template\taglib\eyou\TagGlobal; $__VALUE__ = $tagGlobal->getGlobal("web_templets_pc"); echo $__VALUE__; ?>/skin/images/sns02.png" alt=""></a></li>
        <li><a href=""><img src="<?php  $tagGlobal = new \think\template\taglib\eyou\TagGlobal; $__VALUE__ = $tagGlobal->getGlobal("web_templets_pc"); echo $__VALUE__; ?>/skin/images/sns03.png" alt=""></a></li>
        <li><a href=""><img src="<?php  $tagGlobal = new \think\template\taglib\eyou\TagGlobal; $__VALUE__ = $tagGlobal->getGlobal("web_templets_pc"); echo $__VALUE__; ?>/skin/images/sns04.png" alt=""></a></li>
      </ul>
    </div>
  </section>
  <section class="foot_bar">
    <div class="layout">
      <div class="copyright"><?php  $tagGlobal = new \think\template\taglib\eyou\TagGlobal; $__VALUE__ = $tagGlobal->getGlobal("web_copyright"); echo $__VALUE__; ?></div>
    </div>
  </section>
</footer>
<!-- 应用插件标签 start --> 
 <?php  $tagWeapp = new \think\template\taglib\eyou\TagWeapp; $__VALUE__ = $tagWeapp->getWeapp("default"); echo $__VALUE__; ?> 
<!-- 应用插件标签 end --> </div>
<!-- <aside class="scrollsidebar" id="scrollsidebar">
  <section class="side_content">
    <div class="side_list">
      <header class="hd"><img src="<?php  $tagGlobal = new \think\template\taglib\eyou\TagGlobal; $__VALUE__ = $tagGlobal->getGlobal("web_templets_pc"); echo $__VALUE__; ?>/skin/images/title_pic.png" alt=""/></header>
      <div class="cont">
        <li><a class="email" href="javascript:">Send Inquiry</a></li>
        <li><a class="skype" href="skype:cmhello">Skype Chat</a></li>
        <li><a class="inquiry" href="mailto:<?php  $tagGlobal = new \think\template\taglib\eyou\TagGlobal; $__VALUE__ = $tagGlobal->getGlobal("web_attr_2"); echo $__VALUE__; ?>">Send Email</a></li>
      </div>
      <div class="t-code"><img src="<?php  $tagGlobal = new \think\template\taglib\eyou\TagGlobal; $__VALUE__ = $tagGlobal->getGlobal("web_attr_7"); echo $__VALUE__; ?>"></div>
      <div class="side_title">LiveChat<a  class="close_btn"><span>关闭</span></a></div>
    </div>
  </section>
  <div class="show_btn"></div>
</aside> -->
<div class="inquiry-pop-bd">
  <div class="inquiry-pop"><i class="ico-close-pop" onclick="hideMsgPop();"></i>
    <div class="content-wrap form" style="width: 100%;height: 100%;overflow: hidden;">
      <div class="ch_form_wrap">
        <div class="email_point"> </div>
        <h1 class="title"><em>Leave Your Message</em> </h1>
        <?php  $typeid = "2"; if(empty($typeid) && isset($channelartlist["id"]) && !empty($channelartlist["id"])) : $typeid = intval($channelartlist["id"]); endif;  $tagGuestbookform = new \think\template\taglib\eyou\TagGuestbookform; $_result = $tagGuestbookform->getGuestbookform($typeid, "default", "", 1); if(is_array($_result) || $_result instanceof \think\Collection || $_result instanceof \think\Paginator): $i = 0; $e = 1; $__LIST__ = $_result;if( count($__LIST__)==0 ) : echo htmlspecialchars_decode("");else: foreach($__LIST__ as $key=>$field): $i= intval($key) + 1;$mod = ($i % 2 ); ?>
        <form method="POST" action="<?php echo $field['action']; ?>" <?php echo $field['formhidden']; ?> onsubmit="<?php echo $field['submit']; ?>" class="ch_form">
          <ul>
            <li class="item item_name">
              <input type="text" name="<?php echo $field['attr_2']; ?>" id="attr_2" placeholder="* <?php echo $field['itemname_2']; ?>"/>
            </li>
            <li class="item item_email">
              <input type="text" name="<?php echo $field['attr_4']; ?>" id="attr_4" placeholder="* <?php echo $field['itemname_4']; ?>"/>
            </li>
            <li class="item item_phone">
              <input type="text" name="<?php echo $field['attr_3']; ?>" id="attr_3" placeholder="<?php echo $field['itemname_3']; ?>"/>
            </li>
            <li class="item item_message">
              <textarea name="<?php echo $field['attr_5']; ?>" id="attr_5" placeholder="* <?php echo $field['itemname_5']; ?>"></textarea>
            </li>
          </ul>
          <div class="item item_submit">
            <input type="submit" value="Send" name="submit" class="submit_btn gtm_submit_btn" style="margin:0" />
          </div>
          <?php echo $field['hidden']; ?>
        </form>
        <?php ++$e; endforeach;endif; else: echo htmlspecialchars_decode("");endif; $field = []; ?> </div>
    </div>
  </div>
</div>
<div class="web-search"> <b id="btn-search-close" class="btn--search-close"></b>
  <div style="width:100%">
    <div class="head-search"> <?php  $tagSearchform = new \think\template\taglib\eyou\TagSearchform; $_result = $tagSearchform->getSearchform("","","","","","default"); if(is_array($_result) || $_result instanceof \think\Collection || $_result instanceof \think\Paginator): $i = 0; $e = 1; $__LIST__ = $_result;if( count($__LIST__)==0 ) : echo htmlspecialchars_decode("");else: foreach($__LIST__ as $key=>$field): $i= intval($key) + 1;$mod = ($i % 2 ); ?>
      <form method="get" action="<?php echo $field['action']; ?>" onsubmit="return searchForm();">
        <input class="search-ipt" type="text" name="keywords" id="keywords" placeholder="Start Typing..." />
        <input class="search-btn" type="submit"/>
        <span class="search-attr">Hit enter to search or ESC to close</span> <?php echo $field['hidden']; ?>
      </form>
      <?php ++$e; endforeach;endif; else: echo htmlspecialchars_decode("");endif; $field = []; ?> </div>
  </div>
</div>
<?php  $tagStatic = new \think\template\taglib\eyou\TagStatic; $__VALUE__ = $tagStatic->getStatic("skin/js/jquery-3.7.0.min.js","","","",""); echo $__VALUE__;  $tagStatic = new \think\template\taglib\eyou\TagStatic; $__VALUE__ = $tagStatic->getStatic("skin/js/jquery-migrate.min.js","","","",""); echo $__VALUE__;  $tagStatic = new \think\template\taglib\eyou\TagStatic; $__VALUE__ = $tagStatic->getStatic("skin/js/wow.js","","","",""); echo $__VALUE__;  $tagStatic = new \think\template\taglib\eyou\TagStatic; $__VALUE__ = $tagStatic->getStatic("skin/js/swiper4.4.2.js","","","",""); echo $__VALUE__;  $tagStatic = new \think\template\taglib\eyou\TagStatic; $__VALUE__ = $tagStatic->getStatic("skin/js/zoom.js","","","",""); echo $__VALUE__;  $tagStatic = new \think\template\taglib\eyou\TagStatic; $__VALUE__ = $tagStatic->getStatic("skin/js/scrollsidebar.js","","","",""); echo $__VALUE__;  $tagStatic = new \think\template\taglib\eyou\TagStatic; $__VALUE__ = $tagStatic->getStatic("skin/js/common.js","","","",""); echo $__VALUE__; ?> 
<!--<ul class="prisna-wp-translate-seo" id="prisna-translator-seo">
  <li class="language-flag language-flag-en"> <a title="English" href="javascript:;"> <b class="country-flag"></b> <span>English</span> </a> </li>
</ul>--> 

</body>
</html>