{include file="public/layout" /}

<body class="bodysy-w">
<div id="append_parent"></div>
<div id="ajaxwaitid"></div>
<div  id="page_div"  class="page" style="margin-top: 15px;min-width: auto;box-shadow:none;">
    <div class="flexigrid" {eq name='$Request.param.archives' value='1'}style="margin-top: 0px;"{/eq}>
        <div class="mDiv" {eq name='$Request.param.archives' value='1'}style="padding: 5px 0px;"{/eq}>
            <div class="ftitle">
                {eq name="'Guestbook@ajax_excel_export'|is_check_access" value="1"}
                <div class="fbutton">

                    {if condition="$Request.param.typeid > 0"}
                    <a href="javascript:void(0);" onclick="excel_export(this, 'ids');" data-url="{:url('Guestbook/ajax_excel_export')}">
                        <div class="add">
                            <span><i class="fa fa-reply"></i>导出Excel</span>
                        </div>
                    </a>
                    {else /}
                    <a href="javascript:void(0);" onclick="showErrorMsg('请指定所属栏目！');">
                        <div class="add">
                            <span><i class="fa fa-reply"></i>导出Excel</span>
                        </div>
                    </a>
                    {/if}
                </div>
                {/eq}
            </div>
            <form class="navbar-form form-inline" action="{:url('Guestbook/index')}" method="get" onsubmit="layer_loading('正在处理');">
                {$searchform.hidden|default=''}
                <div class="sDiv">
                    <div class="sDiv2">
                        <input type="text" name="add_time_begin" id="add_time_begin" value="{$Request.param.add_time_begin}" class="qsbox" autocomplete="off" placeholder="起始日期">
                    </div>
                    &nbsp;至&nbsp;
                    <div class="sDiv2">
                        <input type="text" name="add_time_end" id="add_time_end" value="{$Request.param.add_time_end}" class="qsbox" autocomplete="off" placeholder="结束日期">
                    </div>
                    <div class="sDiv2">
                        <input type="hidden" name="typeid" id="typeid" value="{$Request.param.typeid|default=''}">
                        <input type="text" size="30" name="keywords" value="{$Request.param.keywords}" class="qsbox" placeholder="模糊搜索...">
                        <input type="submit" class="btn" value="搜索">
                        <i class="iconfont e-sousuo"></i>
                    </div>

                </div>
            </form>
        </div>
        <div class="hDiv">
            <div class="hDivBox">
                <table cellspacing="0" cellpadding="0" style="width: 100%;">
                    <thead>
                    <tr>
                        <th class="sign w40" axis="col0">
                            <div class="tc w40"><input type="checkbox" autocomplete="off" class="checkAll"></div>
                        </th>
                        <th abbr="article_show" axis="col5" class="w50">
                            <div class="tc">ID</div>
                        </th>
                        {volist name="tab_list" id="vo1"}
                            {if condition="$i elt 4" }
                                {if $i == 1}
                                <th abbr="ac_id" axis="col4">
                                    <div class="text-l10">{$vo1.attr_name}</div>
                                </th>
                                {else /}
                                <th abbr="article_show" axis="col5" class="w150">
                                    <div class="tc">{$vo1.attr_name}</div>
                                </th>
                                {/if}
                            {/if}
                        {/volist}

                        {empty name='$tab_list'}
                            <th abbr="ac_id" axis="col4">
                                <div class="text-l10">所属栏目</div>
                            </th>
                        {else /}
                            <th abbr="article_time" axis="col6" class="w100">
                                <div class="tc">所属栏目</div>
                            </th>
                        {/empty}

                        <th abbr="article_time" axis="col6" class="w50">
                            <div class="tc">查阅</div>
                        </th>
                        <th abbr="article_time" axis="col6" class="w160">
                            <div class="tc">新增时间</div>
                        </th>
                        <th axis="col1" class="w120">
                            <div class="tc">操作</div>
                        </th>
                    </tr>
                    </thead>
                </table>
            </div>
        </div>
        <div class="bDiv" style="height: auto;">
            <div id="flexigrid" cellpadding="0" cellspacing="0" border="0">
                <table style="width: 100%;">
                    <tbody>
                    {empty name="list"}
                        <tr>
                            <td class="no-data" align="center" axis="col0" colspan="50">
                                <div class="no_row">
                                    <div class="no_pic"><img src="__SKIN__/images/null-data.png"></div>
                                </div>
                            </td>
                        </tr>
                    {else/}
                        {foreach name="list" item="vo" key="k" }
                        <tr>
                            <td class="sign">
                                <div class="w40 tc"><input type="checkbox" autocomplete="off" name="ids[]" value="{$vo.aid}"></div>
                            </td>
                            <td class="sort">
                                <div class="w50 tc">
                                    {$vo.aid}
                                </div>
                            </td>
                            {for start="0" end="count($tab_list)"}
                                {if condition="$i lt 4" }
                                    {if $i == 0}
                                    <td style="width: 100%">
                                        <div class="text-l10">
                                            {$vo['attr_list'][$i]['attr_value']}
                                        </div>
                                    </td>
                                    {else /}
                                    <td class="">
                                        <div class="w150 tc">
                                            {$vo['attr_list'][$i]['attr_value']}
                                        </div>
                                    </td>
                                    {/if}
                                {/if}
                            {/for}

                            {empty name='$tab_list'}
                                <td style="width: 100%">
                                    <div class="text-l10">
                                        <a href="{:url('Guestbook/index', ['typeid'=>$vo.typeid])}">{$vo.typename|default='<i class="red">数据出错！</i>'}</a>
                                    </div>
                                </td>
                            {else /}
                                <td class="">
                                    <div class="w100 tc">
                                        <a href="{:url('Guestbook/index', ['typeid'=>$vo.typeid])}">{$vo.typename|default='<i class="red">数据出错！</i>'}</a>
                                    </div>
                                </td>
                            {/empty}

                            <td class="">
                                <div class="w50 tc" id="td_is_read_{$vo.aid}">
                                    {eq name='$vo.is_read' value='1'}
                                        已读
                                    {else /}
                                        <font color="red">未读</font>
                                    {/eq}
                                </div>
                            </td>
                            <td class="">
                                <div class="w160 tc">
                                    {$vo.add_time|date='Y-m-d H:i:s',###}
                                </div>
                            </td>
                            <td class="operation">
                                <div class="w120 tc">
                                    <a class="btn blue" href="javascript:void(0);" data-href="{:url('Guestbook/details', ['aid'=>$vo['aid']])}" onclick="openFullframe(this, '留言详情', '800px', '80%');$('#td_is_read_{$vo.aid}').html('已读');">查看</a>
                                    {eq name="$Think.const.CONTROLLER_NAME.'@del'|is_check_access" value="1"}
                                    <i></i>
                                    <a class="btn red" href="javascript:void(0);" data-url="{:url('Guestbook/del')}" data-id="{$vo.aid}" onClick="delfun(this);">删除</a>
                                    {/eq}
                                </div>
                            </td>
                        </tr>
                        {/foreach}
                    {/empty}
                    </tbody>
                </table>
            </div>
            <div class="iDiv" style="display: none;"></div>
        </div>
        {notempty name="list"}
        <div class="tDiv">
            <div class="tDiv2">
                <div class="fbutton checkboxall">
                    <input type="checkbox" autocomplete="off" class="checkAll">
                </div>
                {eq name="$Think.const.CONTROLLER_NAME.'@del'|is_check_access" value="1"}
                <div class="fbutton">
                    <a onclick="batch_del(this, 'ids');" data-url="{:url('Guestbook/del')}">
                        <div class="add">
                            <span>批量删除</span>
                        </div>
                    </a>
                </div>
                {/eq}
                {include file="public/page" /}
            </div>
            <div style="clear:both"></div>
        </div>
        {/notempty}
    </div>
</div>
<script type="text/javascript">
    $(function(){
        $('input[name*=ids]').click(function(){
            if ($('input[name*=ids]').length == $('input[name*=ids]:checked').length) {
                $('.checkAll').prop('checked','checked');
            } else {
                $('.checkAll').prop('checked', false);
            }
        });
        $('input[type=checkbox].checkAll').click(function(){
            $('input[type=checkbox]').prop('checked',this.checked);
        });
    });

    layui.use('laydate', function(){
        var laydate = layui.laydate;
        //执行一个laydate实例
        laydate.render({
            elem: '#add_time_begin' //指定元素
        });
        laydate.render({
            elem: '#add_time_end' //指定元素
        });
    });

    $(document).ready(function(){

        // 表格行点击选中切换
        $('#flexigrid > table>tbody >tr').click(function(){
            $(this).toggleClass('trSelected');
        });

        // 点击刷新数据
        $('.fa-refresh').click(function(){
            location.href = location.href;
        });
    });

    function excel_export(obj,name) {
        var url = $(obj).attr('data-url');
        var aid = [];
        var typeid =  $('input[name=typeid]').val();
        var start_time =  $('input[name=add_time_begin]').val();
        var end_time =  $('input[name=add_time_end]').val();
        $('input[name^='+name+']').each(function(i,o){
            if($(o).is(':checked')){
                aid.push($(o).val());
            }
        });
        url = url+"&aid="+aid+"&typeid="+typeid+"&start_time="+start_time+"&end_time="+end_time;
        window.location.href = url;
    }
</script>

{include file="public/footer" /}