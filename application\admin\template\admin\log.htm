{include file="public/layout" /}
<body class="bodystyle" style="cursor: default; -moz-user-select: inherit;">
<div id="append_parent"></div>
<div id="ajaxwaitid"></div>
<div class="page">
    <div class="fixed-bar">
        <div class="item-title">
            <div class="subject">
                <h3>高级选项 - 操作日志</h3>
                <h5></h5>
            </div>
        </div>
    </div>
    <div class="flexigrid">
        <div class="mDiv">
            <div class="ftitle">
                <h3>操作日志列表</h3>
                <h5>(共{$pager->totalRows}条数据)</h5>
            </div>
            <div title="刷新数据" class="pReload"><i class="fa fa-refresh"></i></div>
        </div>
        <div class="hDiv">
            <div class="hDivBox">
                <table cellspacing="0" cellpadding="0">
                    <thead>
                    <tr>
                        <th align="left" abbr="article_title" axis="col3" class="">
                            <div style="text-align: left; width: 100px;" class="">ID</div>
                        </th>
                        <th align="left" abbr="ac_id" axis="col4" class="">
                            <div style="text-align: left; width: 100px;" class="">管理员</div>
                        </th>
                        <th align="center" abbr="article_show" axis="col5" class="">
                            <div style="text-align: left; width: 300px;" class="">描述</div>
                        </th>
                        <th align="left" abbr="ac_id" axis="col4" class="">
                            <div style="text-align: left; width: 100px;" class="">IP</div>
                        </th>
                        <th align="center" abbr="article_show" axis="col5" class="">
                            <div style="text-align: left; width: 200px;" class="">操作时间</div>
                        </th>
                        <th style="width:100%" axis="col7">
                            <div></div>
                        </th>
                    </tr>
                    </thead>
                </table>
            </div>
        </div>
        <div class="bDiv" style="height: auto;">
            <div id="flexigrid" cellpadding="0" cellspacing="0" border="0">
                <table>
                    <tbody>
                    {empty name="list"}
                        <tr>
                            <td class="no-data" align="center" axis="col0" colspan="50">
                                <div class="no_row">
                                    <div class="no_pic"><img src="__SKIN__/images/null-data.png"></div>
                                </div>
                            </td>
                        </tr>
                    {else/}
                        {foreach name="list" item="vo" key="k" }
                        <tr>
                            <td align="left" class="">
                                <div style="text-align: left; width: 100px;">{$vo.log_id}</div>
                            </td>
                            <td align="left" class="">
                                <div style="text-align: left; width: 100px;">{$vo.user_name}</div>
                            </td>
                            <td align="left" class="">
                                <div style="text-align: left; width: 300px;">{$vo.log_info}</div>
                            </td>
                            <td align="left" class="">
                                <div style="text-align: left; width: 100px;">{$vo.log_ip}</div>
                            </td>
                            <td align="left" class="">
                                <div style="text-align: left; width: 200px;">{$vo.log_time|date="Y-m-d H:i:s",###}</div>
                            </td>
                            <td align="" class="" style="width: 100%;">
                                <div>&nbsp;</div>
                            </td>
                        </tr>
                        {/foreach}
                    {/empty}
                    </tbody>
                </table>
            </div>
            <div class="iDiv" style="display: none;"></div>
        </div>
        <!--分页位置-->
        {$page} </div>
</div>
<script type="text/javascript">
    $(document).ready(function(){
        // 表格行点击选中切换
        $('#flexigrid > table>tbody >tr').click(function(){
            $(this).toggleClass('trSelected');
        });

        // 点击刷新数据
        $('.fa-refresh').click(function(){
            location.href = location.href;
        });

        $('#think_page_trace_open').css('z-index', 99999);

    });
</script>

{include file="public/footer" /}