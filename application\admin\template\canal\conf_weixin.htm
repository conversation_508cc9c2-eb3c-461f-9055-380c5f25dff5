{include file="public/layout" /}
<body class="bodystyle" style="overflow-y: scroll;min-width:auto;">
<div id="toolTipLayer" style="position: absolute; z-index: 9999; display: none; visibility: visible; left: 95px; top: 573px;"></div>
<div id="append_parent"></div>
<div id="ajaxwaitid"></div>
<style type="text/css">
    .ercodepic{
        width: 150px;
        height: 150px;
        vertical-align: middle;
        text-align: center;
        display: table-cell;
    }
</style>
<div class="page" style="min-width: auto;box-shadow:none;">
    {include file="canal/bar" /}
    <!-- 操作说明 -->
    <div id="explanation" class="explanation">
        <div id="checkZoom" class="title"><i class="fa fa-lightbulb-o"></i>
            <h4 title="提示相关设置操作时应注意的要点">提示</h4>
            <span title="收起提示" id="explanationZoom" style="display: block;"></span>
        </div>
        <ul>
            <li>登录微信小程序平台 <a href="https://mp.weixin.qq.com" target="_blank">https://mp.weixin.qq.com</a> ，在“开发管理->开发设置”里获取AppID、AppSecret的值</li>
            <li>登录微信支付商户平台 <a href="https://pay.weixin.qq.com" target="_blank">https://pay.weixin.qq.com</a> ，具体点击<a href="JavaScript:void(0);" onclick="click_to_eyou_1575506523('https://www.eyoucms.com/plus/view.php?aid=8829&origin_eycms=1','微信支付配置教程')" class="red">查看教程</a>操作</li>
            <li>以下配置可通用于开源微信小程序API、或云插件库里的微信小程序插件。</li>
        </ul>
    </div>
    <form class="form-horizontal" id="post_form" action="{:url('Canal/conf_weixin')}" method="post">
        <div class="ncap-form-default">
            <dl class="row"><dt class="tit"><label><b>微信登录</b></label></dt></dl>
            <dl class="row">
                <dt class="tit">
                    <label for="appid"><em id="em_appid" {empty name='$data.appid'}style="display: none;"{/empty}>*</em>AppID</label>
                </dt>
                <dd class="opt">
                    <input type="text" name="appid" id="appid" value="{$data.appid|default=''}" class="input-txt" autocomplete="off">
                    <p class="notic">小程序ID</p>
                </dd>
            </dl>
            <dl class="row">
                <dt class="tit">
                    <label for="appsecret"><em id="em_appsecret" {empty name='$data.appsecret'}style="display: none;"{/empty}>*</em>AppSecret</label>
                </dt>
                <dd class="opt">
                    <input type="text" name="appsecret" id="appsecret" value="{$data.appsecret|default=''}" class="input-txt" autocomplete="off">
                    <p class="notic">小程序密钥</p>
                </dd>
            </dl>
            <dl class="row"><dt class="tit"><label><b>微信支付</b></label></dt></dl>
            <dl class="row">
                <dt class="tit">
                    <label for="mchid">商户号</label>
                </dt>
                <dd class="opt">
                    <input type="text" name="mchid" id="mchid" value="{$data.mchid|default=''}" class="input-txt" autocomplete="off">
                    <p class="notic">商户ID</p>
                </dd>
            </dl>
            <dl class="row">
                <dt class="tit">
                    <label for="apikey">APIv2密钥</label>
                </dt>
                <dd class="opt">
                    <input type="text" name="apikey" id="apikey" value="{$data.apikey|default=''}" class="input-txt" autocomplete="off">
                    <p class="notic">APIv2密钥</p>
                </dd>
            </dl>
            <dl class="row {empty name='$qrcodeurl'}none{/empty}" id="dl_qrcode">
                <dt class="tit">
                    <label for="qrcode">小程序码</label>
                </dt>
                <dd class="opt">
                    <span id="span_qrcode"></span>
                    <div class="ercodepic">
                        <img id="img_qrcode" src="__STATIC__/common/images/loading.gif" width="23" height="23" />
                    </div>
                    <p class="notic"></p>
                </dd>
            </dl>
            <div class="bot">
                <a href="JavaScript:void(0);" onclick="checkForm();" class="ncap-btn-big ncap-btn-green" id="submitBtn">确认提交</a>
            </div>
        </div>
    </form>
</div>
<script type="text/javascript">

    $(function(){
        $('#appid, #appsecret').keyup(function(){
            var appid = $.trim($('#appid').val());
            var appsecret = $.trim($('#appsecret').val());
            if (appid != '' || appsecret != '') {
                $('#em_appid').show();
                $('#em_appsecret').show();
            } else {
                $('#em_appid').hide();
                $('#em_appsecret').hide();
            }
        });

        function ajax_get_weixin_qrcode()
        {
            var appid = $.trim($('#appid').val());
            var appsecret = $.trim($('#appsecret').val());
            if (appid != '' && appsecret != '') {
                $('#dl_qrcode').show();

                var qrcodeurl = "{$qrcodeurl}";
                if (qrcodeurl != '') {
                    $('#img_qrcode').attr('src', qrcodeurl);
                    setTimeout(function(){
                        $('#img_qrcode').attr('width', 150).attr('height', 150);
                    },100);
                }
            }

            $.ajax({
                url: "{:url('Canal/ajax_get_weixin_qrcode', ['_ajax'=>1])}",
                type: 'GET',
                dataType: 'JSON',
                data: {},
                success: function (res) {
                    if (1 == res.code) {
                        $('#span_qrcode').hide();
                        $('#img_qrcode').attr('src', '');
                        $('#img_qrcode').attr('src', res.data.qrcodeurl).attr('width', 150).attr('height', 150).show();
                    } else {
                        $('#span_qrcode').html(res.msg);
                        $('#img_qrcode').hide();
                        return false;
                    }
                },
                error: function (e) {
                    $('#span_qrcode').html(e.responseText);
                    $('#img_qrcode').hide();
                    return false;
                }
            });
        }
        ajax_get_weixin_qrcode();
    });

    // 判断输入框是否为空
    function checkForm(){
        var appid = $.trim($('#appid').val());
        var appsecret = $.trim($('#appsecret').val());
        if (appid != '' || appsecret != '') {
            if(appid == ''){
                showErrorMsg('AppID不能为空！');
                $('#appid').focus();
                return false;
            }
            if(appsecret == ''){
                showErrorMsg('AppSecret不能为空！');
                $('#appsecret').focus();
                return false;
            }
            if($.trim($('#mchid').val()) == '' && $.trim($('#apikey').val()) != ''){
                showErrorMsg('微信支付商户号不能为空！');
                $('#mchid').focus();
                return false;
            }
            if($.trim($('#mchid').val()) != '' && $.trim($('#apikey').val()) == ''){
                showErrorMsg('微信支付API密钥不能为空！');
                $('#apikey').focus();
                return false;
            }
        }
        layer_loading('正在处理');
        $.ajax({
            url: "{:url('Canal/conf_weixin', ['_ajax'=>1])}",
            type: 'POST',
            dataType: 'JSON',
            data: $('#post_form').serialize(),
            success: function (res) {
                layer.closeAll();
                if (1 == res.code) {
                    layer.msg(res.msg, {shade: layer_shade, time: 1000}, function(){
                        window.location.reload();
                    });
                } else {
                    showErrorAlert(res.msg);
                    return false;
                }
            },
            error: function (e) {
                layer.closeAll();
                showErrorAlert(e.responseText);
                return false;
            }
        });
    }

</script>
{include file="public/footer" /}