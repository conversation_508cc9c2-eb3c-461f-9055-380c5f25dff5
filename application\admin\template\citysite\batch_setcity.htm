{include file="public/layout" /}
<body style="background-color: rgb(255, 255, 255); overflow: auto; cursor: default; -moz-user-select: inherit;min-width: auto;">
<div id="append_parent"></div>
<div id="ajaxwaitid"></div>
<div class="page" style="min-width: auto;box-shadow:none; padding-bottom: 0px;">
    <!-- 操作说明 -->
    <div id="explanation" class="explanation">
        <div id="checkZoom" class="title"><i class="fa fa-lightbulb-o"></i>
            <h4 title="提示相关设置操作时应注意的要点">提示</h4>
            <span title="收起提示" id="explanationZoom" style="display: block;"></span>
        </div>
        <ul>
            <li>二级域名：开启之前，建议先做好二级域名解析及绑定，避免访问不了。</li>
            <li>主站文档：分站列表是否要显示主站（所属区域为“全国”）的文档。</li>
        </ul>
    </div>
    <form class="form-horizontal" id="post_form" method="POST" action="{:url('Citysite/batch_setcity')}" onsubmit="check_submit();">
        <div class="ncap-form-default">
            <dl class="row">
                <dt class="tit">
                    <label for="is_open">二级域名</label>
                </dt>
                <dd class="opt" style="width: auto;">
                    <label class="curpoin"><input type="radio" name="is_open" value="-1" checked="checked">原设置</label>&nbsp;&nbsp;
                    <label class="curpoin"><input type="radio" name="is_open" value="1">开启</label>&nbsp;&nbsp;
                    <label class="curpoin"><input type="radio" name="is_open" value="0">关闭</label>
                    <span class="err"></span>
                    <p class="notic"></p>
                </dd>
            </dl>
            <dl class="row">
                <dt class="tit">
                    <label for="showall">主站文档</label>
                </dt>
                <dd class="opt" style="width: auto;">
                    <label class="curpoin"><input type="radio" name="showall" value="-1" checked="checked">原设置</label>&nbsp;&nbsp;
                    <label class="curpoin"><input type="radio" name="showall" value="1">显示</label>&nbsp;&nbsp;
                    <label class="curpoin"><input type="radio" name="showall" value="0">隐藏</label>
                    <span class="err"></span>
                    <p class="notic"></p>
                </dd>
            </dl>
            <dl class="row">
                <dt class="tit">
                    <label for="status">区域状态</label>
                </dt>
                <dd class="opt" style="width: auto;">
                    <label class="curpoin"><input type="radio" name="status" value="-1" checked="checked">原设置</label>&nbsp;&nbsp;
                    <label class="curpoin"><input type="radio" name="status" value="1">启用</label>&nbsp;&nbsp;
                    <label class="curpoin"><input type="radio" name="status" value="0">禁用</label>
                    <p class="notic"></p>
                </dd>
            </dl>
            <dl class="row">
                <dt class="tit">
                    <label for="seoset">SEO信息</label>
                </dt>
                <dd class="opt" style="width: auto;">
                    <label class="curpoin"><input type="radio" name="seoset" value="-1" checked="checked">原设置</label>&nbsp;&nbsp;
                    <label class="curpoin"><input type="radio" name="seoset" value="0">清空</label>&nbsp;&nbsp;
                    <label class="curpoin"><input type="radio" name="seoset" value="1">引用系统默认</label>
                    <span class="err"></span>
                    <p class="notic">系统默认分站SEO在功能配置里统一填写</p>
                </dd>
            </dl>
            <dl class="row" >
                <dt class="tit">操作范围</dt>
                <dd class="opt" style="width: auto;">
                    <label class="label"><input class="check" type="checkbox" name="inherit_province" value="1">&nbsp;{eq name='$level' value='1'}当前区域{else/}一级区域{/eq}(省)</label>
                    &nbsp;&nbsp;&nbsp;
                    <label class="label"><input class="check" type="checkbox" name="inherit_city" value="1">&nbsp;{eq name='$level' value='2'}当前区域{else/}二级区域{/eq}(市/县)</label>
                    &nbsp;&nbsp;&nbsp;
                    <label class="label"><input class="check" type="checkbox" name="inherit_area" value="1">&nbsp;{eq name='$level' value='3'}当前区域{else/}三级区域{/eq}(乡/镇/区)</label>
                </dd>
            </dl>
            <div class="bot" style="padding-bottom:0px;">
                <input type="hidden" name="_ajax" value="1">
                <input type="hidden" name="ids" value="" id="ids">
                <input type="hidden" name="level" value="{$level}">
                <a href="JavaScript:void(0);" onclick="check_submit();" class="ncap-btn-big ncap-btn-green" id="submitBtn">确认提交</a>
            </div>
        </div>
    </form>
</div>
<script type="text/javascript">
    var parentObj = parent.layer.getFrameIndex(window.name); //先得到当前iframe层的索引

    function check_submit()
    {
        var inherit_province_ckd = $('input[name=inherit_province]').is(':checked');
        var inherit_city_ckd = $('input[name=inherit_city]').is(':checked');
        var inherit_area_ckd = $('input[name=inherit_area]').is(':checked');
        if (!inherit_province_ckd && !inherit_city_ckd && !inherit_area_ckd) {
            showErrorAlert('请勾选要操作的区域级别');
            return false;
        }

        layer_loading('正在处理');
        $.ajax({
            url: $('#post_form').attr('action'),
            type: 'POST',
            dataType: 'JSON',
            data: $('#post_form').serialize(),
            success: function(res){
                layer.closeAll();
                if (res.code == 1) {
                    parent.layer.msg(res.msg, {shade: layer_shade, time: 1000}, function(){
                        parent.window.location.reload();
                        parent.layer.close(parentObj);
                    });
                } else {
                    showErrorMsg(res.msg);
                }
                return false;
            },
            error: function(e){
                layer.closeAll();
                showErrorAlert(e.responseText);
                return false;
            }
        });
    }
</script>
{include file="public/footer" /}