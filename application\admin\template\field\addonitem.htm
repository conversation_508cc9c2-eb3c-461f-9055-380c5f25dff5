
{volist name="$addonFieldExtList" id="vo"}
    {if !isset($vo.ifeditable) || $vo.ifeditable}
        {switch name="vo.dtype"}
            {case value="hidden"}
                <!-- 隐藏域 start -->
                <dl class="row" style="display: none;">
                    <dt class="tit">
                        <label>{if condition="isset($vo['ifrequire']) AND !empty($vo['ifrequire'])"}<em>*</em>{/if}{$vo.title|default=''}</label>
                    </dt>
                    <dd class="opt">
                        <input type="hidden" class="input-txt" id="{$vo.fieldArr}_{$vo.name|default=''}" name="{$vo.fieldArr}[{$vo.name|default=''}]" value="{$vo.dfvalue|default=''}">
                    </dd>
                </dl>
                <!-- 隐藏域 start -->
            {/case}

            {case value="region"}
                <!-- 区域选项 start -->
                <dl class="row">
                    <dt class="tit">
                        <label>{if condition="isset($vo['ifrequire']) AND !empty($vo['ifrequire'])"}<em>*</em>{/if}{$vo.title|default=''}</label>
                    </dt>
                    <dd class="opt">
                        {volist name="$vo.dfvalue" id="v2"}
                            <label>
                                <input type="radio" id="{$vo.fieldArr}_{$vo.name|default=''}" name="{$vo.fieldArr}[{$vo.name|default=''}]" value="{$v2['id']}" {if condition="isset($vo['trueValue']) AND in_array($v2['id'], $vo['trueValue'])"}checked="checked"{/if}>{$v2['name']}
                            </label>&nbsp;
                        {/volist}
                        <span class="err"></span>
                        <!-- <p class="notic">{$vo.remark|default=''}</p> -->
                    </dd>
                </dl>
                <!-- 区域选项 end -->
            {/case}

            {case value="text"}
                <!-- 单行文本框 start -->
                <dl class="row">
                    <dt class="tit">
                        <label>{if condition="isset($vo['ifrequire']) AND !empty($vo['ifrequire'])"}<em>*</em>{/if}{$vo.title|default=''}</label>
                    </dt>
                    <dd class="opt">
                        <input type="text" class="input-txt" id="{$vo.fieldArr}_{$vo.name|default=''}" name="{$vo.fieldArr}[{$vo.name|default=''}]" value="{$vo.dfvalue|default=''}">&nbsp;{$vo.dfvalue_unit|default=''}
                        <span class="err"></span>
                        <!-- <p class="notic">{$vo.remark|default=''}</p> -->
                    </dd>
                </dl>
                <!-- 单行文本框 end -->
            {/case}

            {case value="multitext"}
                <!-- 多行文本框 start -->
                <dl class="row">
                    <dt class="tit">
                        <label>{if condition="isset($vo['ifrequire']) AND !empty($vo['ifrequire'])"}<em>*</em>{/if}{$vo.title|default=''}</label>
                    </dt>
                    <dd class="opt">          
                        <textarea rows="5" cols="60" id="{$vo.fieldArr}_{$vo.name|default=''}" name="{$vo.fieldArr}[{$vo.name|default=''}]" style="height:60px;">{$vo.dfvalue|default=''}</textarea>
                        <span class="err"></span>
                        <!-- <p class="notic">{$vo.remark|default=''}</p> -->
                    </dd>
                </dl>
                <!-- 多行文本框 end -->
            {/case}

            {case value="checkbox"}
                <!-- 复选框 start -->
                <dl class="row">
                    <dt class="tit">
                        <label>{if condition="isset($vo['ifrequire']) AND !empty($vo['ifrequire'])"}<em>*</em>{/if}{$vo.title|default=''}</label>
                    </dt>
                    <dd class="opt">
                        {volist name="$vo.dfvalue" id="v2"}
                        <label><input type="checkbox" name="{$vo.fieldArr}[{$vo.name|default=''}][]" value="{$v2}" {if condition="isset($vo['trueValue']) AND in_array($v2, $vo['trueValue'])"}checked="checked"{/if} onclick="func_{$vo.name}_eyempty();">{$v2}</label>&nbsp;
                        {/volist}
                        <input type="hidden" name="{$vo.fieldArr}[{$vo.name}_eyempty]" value="{if condition="!empty($vo['trueValue'])"}1{else /}0{/if}">
                        <span class="err"></span>
                        <!-- <p class="notic">{$vo.remark|default=''}</p> -->
                    </dd>
                </dl>
                <script type="text/javascript">
                    function func_{$vo.name}_eyempty()
                    {
                        var len = $("input[name='{$vo.fieldArr}[{$vo.name}][]']:checked").length;
                        $("input[name='{$vo.fieldArr}[{$vo.name}_eyempty]']").val(len);
                    }
                </script>
                <!-- 复选框 end -->
            {/case}

            {case value="radio"}
                <!-- 单选项 start -->
                <dl class="row">
                    <dt class="tit">
                        <label>{if condition="isset($vo['ifrequire']) AND !empty($vo['ifrequire'])"}<em>*</em>{/if}{$vo.title|default=''}</label>
                    </dt>
                    <dd class="opt">
                        {volist name="$vo.dfvalue" id="v2"}
                        <label><input type="radio" id="{$vo.fieldArr}_{$vo.name|default=''}" name="{$vo.fieldArr}[{$vo.name|default=''}]" value="{$v2}" {if condition="isset($vo['trueValue']) AND in_array($v2, $vo['trueValue'])"}checked="checked"{/if}>{$v2}</label>&nbsp;
                        {/volist}
                        <span class="err"></span>
                        <!-- <p class="notic">{$vo.remark|default=''}</p> -->
                    </dd>
                </dl>
                <!-- 单选项 end -->
            {/case}

            {case value="switch"}
                <!-- 开关 start -->
                <dl class="row">
                    <dt class="tit">
                        <label>{if condition="isset($vo['ifrequire']) AND !empty($vo['ifrequire'])"}<em>*</em>{/if}{$vo.title|default=''}</label>
                    </dt>
                    <dd class="opt">
                        <div class="onoff">
                            <label for="{$vo.fieldArr}_{$vo.name|default=''}1" class="cb-enable {if condition="0 != $vo['dfvalue']"}selected{/if}">是</label>
                            <label for="{$vo.fieldArr}_{$vo.name|default=''}0" class="cb-disable {if condition="0 == $vo['dfvalue']"}selected{/if}">否</label>
                            <input id="{$vo.fieldArr}_{$vo.name|default=''}1" name="{$vo.fieldArr}[{$vo.name|default=''}]" value="1" type="radio" {if condition="0 != $vo['dfvalue']"}checked="checked"{/if}>
                            <input id="{$vo.fieldArr}_{$vo.name|default=''}0" name="{$vo.fieldArr}[{$vo.name|default=''}]" value="0" type="radio" {if condition="0 == $vo['dfvalue']"}checked="checked"{/if}>
                        </div>
                        <span class="err"></span>
                        <!-- <p class="notic">{$vo.remark|default=''}</p> -->
                    </dd>
                </dl>
                <script type="text/javascript">
                    $(function(){
                        //自定义radio样式
                        $(".cb-enable").on('click', function(){
                            var parent = $(this).parents('.onoff');
                            $('.cb-disable',parent).removeClass('selected');
                            $(this).addClass('selected');
                            $('.checkbox',parent).attr('checked', true);
                        });
                        $(".cb-disable").on('click', function(){
                            var parent = $(this).parents('.onoff');
                            $('.cb-enable',parent).removeClass('selected');
                            $(this).addClass('selected');
                            $('.checkbox',parent).attr('checked', false);
                        });
                    });
                </script>
                <!-- 开关 end -->
            {/case}

            {case value="select"}
                <!-- 下拉框 start -->
                <dl class="row">
                    <dt class="tit">
                        <label>{if condition="isset($vo['ifrequire']) AND !empty($vo['ifrequire'])"}<em>*</em>{/if}{$vo.title|default=''}</label>
                    </dt>
                    <dd class="opt"> 
                        <select name="{$vo.fieldArr}[{$vo.name|default=''}]" id="{$vo.fieldArr}_{$vo.name|default=''}">
                            {volist name="$vo.dfvalue" id="v2"}
                            <option value="{$v2}" {if condition="isset($vo['trueValue']) AND in_array($v2, $vo['trueValue'])"}selected{/if}>{$v2}</option>
                            {/volist}
                        </select>
                        <span class="err"></span>
                        <!-- <p class="notic">{$vo.remark|default=''}</p> -->
                    </dd>
                </dl>
                <!-- 下拉框 end -->
            {/case}

            {case value="img"}
                <!-- 单张图 start -->
                <dl class="row">
                    <dt class="tit">
                        <label>{if condition="isset($vo['ifrequire']) AND !empty($vo['ifrequire'])"}<em>*</em>{/if}{$vo.title|default=''}</label>
                    </dt>
                    <dd class="opt">
                        <div class="input-file-show div_{$vo.fieldArr}_{$vo.name|default=''}_eyou_local" {neq name="$vo[$vo['name'].'_eyou_is_remote']" value="0"}style="display: none;"{/neq}>
                            <span class="show">
                                <a id="img_a_{$vo.fieldArr}_{$vo.name|default=''}" target="_blank" class="nyroModal" rel="gal" href="{$vo[$vo['name'].'_eyou_local']|default='javascript:void(0);'}">
                                    <i id="img_i_{$vo.fieldArr}_{$vo.name|default=''}" class="fa fa-picture-o" {notempty name="$vo[$vo['name'].'_eyou_local']"}onmouseover="layer_tips=layer.tips('<img src={$vo[$vo['name'].'_eyou_local']} class=\'layer_tips_img\'>',this,{tips: [1, '#fff']});"{/notempty} onmouseout="layer.close(layer_tips);"></i>
                                </a>
                            </span>
                            <span class="type-file-box">
                                <input type="text" id="{$vo.fieldArr}_{$vo.name|default=''}_eyou_local" name="{$vo.fieldArr}[{$vo.name|default=''}_eyou_local]" value="{$vo[$vo['name'].'_eyou_local']|default=''}" class="type-file-text" autocomplete="off">
                                <input type="button" name="button" id="button1" value="选择上传..." class="type-file-button">
                                <input class="type-file-file" onClick="GetUploadify(1,'','allimg','{$vo.fieldArr}_{$vo.name|default=''}_call_back')" size="30" hidefocus="true" nc_type="change_site_{$vo.fieldArr}_{$vo.name|default=''}"
                                     title="点击前方预览图可查看大图，点击按钮选择文件并提交表单后上传生效">
                            </span>
                        </div>
                        <input type="text" id="{$vo.fieldArr}_{$vo.name|default=''}_eyou_remote" name="{$vo.fieldArr}[{$vo.name|default=''}_eyou_remote]" value="{$vo[$vo['name'].'_eyou_remote']|default=''}" placeholder="http://" class="input-txt" {neq name="$vo[$vo['name'].'_eyou_is_remote']" value="1"}style="display: none;"{/neq}>
                        &nbsp;
                        <label><input type="checkbox" name="{$vo.fieldArr}[{$vo.name|default=''}_eyou_is_remote]" id="{$vo.fieldArr}_{$vo.name|default=''}_eyou_is_remote" value="1" {eq name="$vo[$vo['name'].'_eyou_is_remote']" value="1"}checked="checked"{/eq} onClick="clickRemote(this, '{$vo.fieldArr}_{$vo.name|default=''}_eyou');">远程图片</label>
                        <span class="err"></span>
                        <!-- <p class="notic">{$vo.remark|default=''}</p> -->
                    </dd>
                </dl>
                <script type="text/javascript">
                    function {$vo.fieldArr}_{$vo.name|default=''}_call_back(fileurl_tmp)
                    {
                      $("#{$vo.fieldArr}_{$vo.name|default=''}_eyou_local").val(fileurl_tmp);
                      $("#img_a_{$vo.fieldArr}_{$vo.name|default=''}").attr('href', fileurl_tmp);
                      $("#img_i_{$vo.fieldArr}_{$vo.name|default=''}").attr('onmouseover', "layer_tips=layer.tips('<img src="+fileurl_tmp+" class=\\'layer_tips_img\\'>',this,{tips: [1, '#fff']});");
                    }
                </script>
                <!-- 单张图 end -->
            {/case}

            {case value="file"}
            <!-- 单个文件 start -->
            <dl class="row">
                <dt class="tit">
                    <label>{if condition="isset($vo['ifrequire']) AND !empty($vo['ifrequire'])"}<em>*</em>{/if}{$vo.title|default=''}</label>
                </dt>
                <dd class="opt">
                    <div class="input-file-show div_{$vo.fieldArr}_{$vo.name|default=''}_eyou_local" {neq name="$vo[$vo['name'].'_eyou_is_remote']" value="0"}style="display: none;"{/neq}>
                        <span class="type-file-box">
                            <input type="text" id="{$vo.fieldArr}_{$vo.name|default=''}_eyou_local" name="{$vo.fieldArr}[{$vo.name|default=''}_eyou_local]" value="{$vo[$vo['name'].'_eyou_local']|default=''}" class="type-file-text" autocomplete="off">
                            <input type="button" name="button" id="button1" value="选择上传..." class="type-file-button">
                            <input class="type-file-file" type="file" onchange="upload_file_1585641738(this)" size="30" hidefocus="true" nc_type="change_site_{$vo.fieldArr}_{$vo.name|default=''}">
                        </span>
                    </div>
                    <input type="text" id="{$vo.fieldArr}_{$vo.name|default=''}_eyou_remote" name="{$vo.fieldArr}[{$vo.name|default=''}_eyou_remote]" value="{$vo[$vo['name'].'_eyou_remote']|default=''}" placeholder="http://" class="input-txt" {neq name="$vo[$vo['name'].'_eyou_is_remote']" value="1"}style="display: none;"{/neq}>
                    &nbsp;
                    <label><input type="checkbox" name="{$vo.fieldArr}[{$vo.name|default=''}_eyou_is_remote]" id="{$vo.fieldArr}_{$vo.name|default=''}_eyou_is_remote" value="1" {eq name="$vo[$vo['name'].'_eyou_is_remote']" value="1"}checked="checked"{/eq} onClick="clickRemote(this, '{$vo.fieldArr}_{$vo.name|default=''}_eyou');">远程文件</label>
                    <span class="err"></span>
                    <!-- <p class="notic">{$vo.remark|default=''}</p> -->
                </dd>
            </dl>
            <script type="text/javascript">
                function upload_file_1585641738(e){
                    var file = $(e)[0].files[0]
                    var formData = new FormData();
                    formData.append('file',file);
                    $.ajax({
                        type: 'post',
                        url: "{:url('Ueditor/DownloadUploadFileAjax')}",
                        data: formData,
                        contentType: false,
                        processData: false,
                        dataType: 'json',
                        success: function (res) {
                            if (res.code==0){
                                layer.msg(res.msg)
                            }else {
                                $("#{$vo.fieldArr}_{$vo.name|default=''}_eyou_local").val(res.file_url);
                            }
                        }
                    })
                }
            </script>
            <!-- 单个文件 end -->
            {/case}

            {case value="imgs"}
                <!-- 多张图 start -->
                <dl class="row" id="dl_{$vo.fieldArr}_{$vo.name|default=''}">
                    <dt class="tit">
                        <label>{if condition="isset($vo['ifrequire']) AND !empty($vo['ifrequire'])"}<em>*</em>{/if}{$vo.title|default=''}</label>
                    </dt>
                    <dd class="opt">
                        <div class="tab-pane pics" id="tab_{$vo.fieldArr}_{$vo.name|default=''}">
                          <!-- <a href="javascript:void(0);" onClick="GetUploadify(100,'','allimg','{$vo.fieldArr}_{$vo.name|default=''}_call_back');" class="imgupload">
                                <i class="fa fa-photo"></i>上传图片
                            </a> -->
                            <table class="table table-bordered">
                                <tbody>
                                <tr>
                                    <td >
                                        <div class="sort-list-{$vo.fieldArr}_{$vo.name|default=''}">
                                            {volist name="$vo[$vo['name'].'_eyou_imgupload_list']" id="v2" key="k2"}
                                            <div class="images_upload images_upload_html" style="display:inline-block;">
                                                <div class="images_upload_box">
                                                    <input type="hidden" name="{$vo.fieldArr}[{$vo.name|default=''}][]" value="{$v2['image_url']}">
                                                    <a href="{$v2['image_url']}" onclick="" class="upimg" target="_blank" title="拖动修改排序">
                                                        <img data-original="{$v2['image_url']}">
                                                    </a>
                                                    <a href="javascript:void(0)" onclick="{$vo.fieldArr}_{$vo.name|default=''}_ClearPicArr2(this,'{$v2['image_url']}')" class="delect" title="删除"></a>
                                                </div>
                                                <textarea rows="5" cols="60" name="{$vo.fieldArr}[{$vo.name|default=''}_eyou_intro][]" placeholder="图片注释">{$v2.intro}</textarea>
                                            </div>
                                            {/volist}
                                            <div class="images_upload"></div>
                                        </div>
                                        <a href="javascript:void(0);" onClick="GetUploadify(100,'','allimg','{$vo.fieldArr}_{$vo.name|default=''}_call_back');"  class="img-upload mb15" title="点击上传">
                                             <div class="y-line"></div>
                                             <div class="x-line"></div>
                                         </a>
                                    </td>
                                </tr>
                                </tbody>
                            </table>
                        </div>
                        <!-- 上传图片显示的样板 start -->
                        <div class="{$vo.fieldArr}_{$vo.name|default=''}_upload_tpl none">
                            <div class="images_upload images_upload_html" style="display:inline-block;">
                                <div class="images_upload_box">
                                    <input type="hidden" name="{$vo.fieldArr}[{$vo.name|default=''}][]" value="" />
                                    <a href="javascript:void(0);" onClick="" class="upimg" title="拖动修改排序">
                                        <img src="__STATIC__/admin/images/add-button.jpg" />
                                    </a>
                                    <a href="javascript:void(0)" class="delect" title="删除">&nbsp;&nbsp;</a>
                                </div>
                                <textarea rows="5" cols="60" name="{$vo.fieldArr}[{$vo.name|default=''}_eyou_intro][]" placeholder="图片注释"></textarea>
                            </div>
                        </div>
                        <!-- 上传图片显示的样板 end -->
                    </dd>
                </dl>
                <script type="text/javascript">
                    // 上传多图回调函数
                    function {$vo.fieldArr}_{$vo.name|default=''}_call_back(paths){
                        
                        var  last_div = $(".{$vo.fieldArr}_{$vo.name|default=''}_upload_tpl").html();
                        for (var i=0;i<paths.length ;i++ )
                        {
                            if ($(".sort-list-{$vo.fieldArr}_{$vo.name|default=''} .images_upload_html").length > 0) {
                                $(".sort-list-{$vo.fieldArr}_{$vo.name|default=''} .images_upload_html:last").after(last_div);  // 插入一个 新图片
                            } else {
                                $(".sort-list-{$vo.fieldArr}_{$vo.name|default=''} .images_upload:last").before(last_div);  // 插入一个 新图片
                            }
                            $(".sort-list-{$vo.fieldArr}_{$vo.name|default=''} .images_upload_html:last").find('a:eq(0)').attr('href',paths[i]).attr('onclick','').attr('target', "_blank");// 修改他的链接地址
                            $(".sort-list-{$vo.fieldArr}_{$vo.name|default=''} .images_upload_html:last").find('img').attr('src',paths[i]);// 修改他的图片路径
                            $(".sort-list-{$vo.fieldArr}_{$vo.name|default=''} .images_upload_html:last").find('a:eq(1)').attr('onclick',"{$vo.fieldArr}_{$vo.name|default=''}_ClearPicArr2(this,'"+paths[i]+"')").text('');
                            $(".sort-list-{$vo.fieldArr}_{$vo.name|default=''} .images_upload_html:last").find('input').val(paths[i]); // 设置隐藏域 要提交的值
                        }             
                    }
                    /*
                     * 上传之后删除组图input     
                     * @access   public
                     * @val      string  删除的图片input
                     */
                    function {$vo.fieldArr}_{$vo.name|default=''}_ClearPicArr2(obj,path)
                    {
                        // 删除数据库记录
                        $.ajax({
                            type:'GET',
                            url:"{:url('Field/del_arctypeimgs', ['_ajax'=>1])}",
                            data:{filename:path,fieldid:"{$vo.id}",typeid:"{$aid|default='0'}"},
                            success:function(){
                                $(obj).parent().parent().remove(); // 删除完服务器的, 再删除 html上的图片
                                $.ajax({
                                    type:'GET',
                                    url:"{:url('Uploadimgnew/delupload', ['_ajax'=>1])}",
                                    data:{action:"del", filename:path},
                                    success:function(){}
                                });
                            }
                        });    
                    }

                    /** 以下 产品相册的拖动排序相关 js*/

                    $( ".sort-list-{$vo.fieldArr}_{$vo.name|default=''}" ).sortable({
                        start: function( event, ui) {
                        
                        }
                        ,stop: function( event, ui ) {

                        }
                    });
                    //因为他们要拖动，所以尽量设置他们的文字不能选择。 
                    // $( ".sort-list-{$vo.fieldArr}_{$vo.name|default=''}" ).disableSelection();
                </script>
                <!-- 多张图 end -->
            {/case}

            {case value="int"}
                <!-- 整数类型 start -->
                <dl class="row">
                    <dt class="tit">
                        <label>{if condition="isset($vo['ifrequire']) AND !empty($vo['ifrequire'])"}<em>*</em>{/if}{$vo.title|default=''}</label>
                    </dt>
                    <dd class="opt"> 
                        <input type="text" value="{$vo.dfvalue|default=''}" name="{$vo.fieldArr}[{$vo.name|default=''}]" id="{$vo.fieldArr}_{$vo.name|default=''}" placeholder="只允许纯数字" class="input-txt" onkeyup="this.value=this.value.replace(/[^0-9]/g,'');" onbeforepaste="clipboardData.setData('text',clipboardData.getData('text').replace(/[^0-9]/g,''));">&nbsp;{$vo.dfvalue_unit|default=''}
                        <span class="err"></span>
                        <!-- <p class="notic">{$vo.remark|default=''}</p> -->
                    </dd>
                </dl>
                <!-- 整数类型 end -->
            {/case}

            {case value="float"}
                <!-- 小数类型 start -->
                <dl class="row">
                    <dt class="tit">
                        <label>{if condition="isset($vo['ifrequire']) AND !empty($vo['ifrequire'])"}<em>*</em>{/if}{$vo.title|default=''}</label>
                    </dt>
                    <dd class="opt"> 
                        <input type="text" value="{$vo.dfvalue|default=''}" name="{$vo.fieldArr}[{$vo.name|default=''}]" id="{$vo.fieldArr}_{$vo.name|default=''}" placeholder="允许带有小数点的数值" class="input-txt" onkeyup="this.value=this.value.replace(/[^0-9\.]/g,'');" onbeforepaste="clipboardData.setData('text',clipboardData.getData('text').replace(/[^0-9\.]/g,''));">&nbsp;{$vo.dfvalue_unit|default=''}
                        <span class="err"></span>
                        <!-- <p class="notic">{$vo.remark|default=''}</p> -->
                    </dd>
                </dl>
                <!-- 小数类型 end -->
            {/case}

            {case value="decimal"}
                <!-- 金额类型 start -->
                <dl class="row">
                    <dt class="tit">
                        <label>{if condition="isset($vo['ifrequire']) AND !empty($vo['ifrequire'])"}<em>*</em>{/if}{$vo.title|default=''}</label>
                    </dt>
                    <dd class="opt"> 
                        <input type="text" value="{$vo.dfvalue|default=''}" name="{$vo.fieldArr}[{$vo.name|default=''}]" id="{$vo.fieldArr}_{$vo.name|default=''}" placeholder="允许带有小数点的金额" class="input-txt" onkeyup="this.value=this.value.replace(/[^0-9\.]/g,'');" onbeforepaste="clipboardData.setData('text',clipboardData.getData('text').replace(/[^0-9\.]/g,''));">&nbsp;{$vo.dfvalue_unit|default=''}
                        <span class="err"></span>
                        <!-- <p class="notic">{$vo.remark|default=''}</p> -->
                    </dd>
                </dl>
                <!-- 金额类型 end -->
            {/case}

            {case value="datetime"}
                <!-- 日期和时间 start -->
                <dl class="row">
                    <dt class="tit">
                        <label>{if condition="isset($vo['ifrequire']) AND !empty($vo['ifrequire'])"}<em>*</em>{/if}{$vo.title|default=''}</label>
                    </dt>
                    <dd class="opt"> 
                        <input type="text" class="input-txt" name="{$vo.fieldArr}[{$vo.name|default=''}]" id="{$vo.fieldArr}_{$vo.name|default=''}" value="{$vo['dfvalue']}" autocomplete="off">        
                        <span class="add-on input-group-addon">
                            <i class="glyphicon glyphicon-calendar fa fa-calendar"></i>
                        </span> 
                        <span class="err"></span>
                        <!-- <p class="notic">{$vo.remark|default=''}</p> -->
                    </dd>
                </dl>
                <script type="text/javascript">
                    layui.use('laydate', function() {
                        var laydate = layui.laydate;

                        //常规用法
                        laydate.render({
                            elem: "#{$vo.fieldArr}_{$vo.name|default=''}"
                            ,type: 'datetime'
                        });
                    })
                </script>
                <!-- 日期和时间 end -->
            {/case}

            {case value="htmltext"}
                <!-- HTML文本 start -->
                {eq name="$editor.editor_select" value="1"}
                <dl class="row">
                    <dt class="tit">
                        <label>{if condition="isset($vo['ifrequire']) AND !empty($vo['ifrequire'])"}<em>*</em>{/if}{$vo.title|default=''}</label>
                    </dt>
                    <dd class="opt">
                        {if condition="isset($vo['first']) AND 1 == $vo['first']"}
                        <label class="curpoin"><input type="checkbox" name="editor_remote_img_local" id="editor_remote_img_local" data-val="{$vo.editor.editor_remote_img_local}" value="1" {eq name="$vo.editor.editor_remote_img_local" value="1"} checked {/eq}>远程图片本地化</label>&nbsp;<!-- <a href="javascript:void(0);" onclick="editor_handle_210607(1);">[手动]</a>&nbsp; -->
                        <label class="curpoin"><input type="checkbox" name="editor_img_clear_link" id="editor_img_clear_link" data-val="{$vo.editor.editor_img_clear_link}" value="1" {eq name="$vo.editor.editor_img_clear_link" value="1"} checked {/eq}>清除非本站链接</label>&nbsp;<!-- <a href="javascript:void(0);" onclick="editor_handle_210607(2);">[手动]</a>&nbsp; -->
                        {/if}
                        <textarea class="span12 ckeditor" id="{$vo.fieldArr}_{$vo.name|default=''}" data-func="{$vo.fieldArr}_{$vo.name|default=''}" name="{$vo.fieldArr}[{$vo.name|default=''}]" title="">{$vo.dfvalue|default=''}</textarea>
                        <span class="err"></span>
                        <!-- <p class="notic">{$vo.remark|default=''}</p> -->
                    </dd>
                </dl>

                {if condition="isset($vo['first']) AND 1 == $vo['first']"}
                <script type="text/javascript">
                    // 打开多图选择及上传界面
                    function OpenImagesList(e, obj) {
                        var func = '';
                        if (e.path && e.path[10] && e.path[10].id && undefined != e.path[10].id) {
                            func = e.path[10].id;
                        } else {
                            func = $(obj).parent().parent().parent().parent().parent().parent().parent().parent().parent().attr('id');
                        }
                        if (func.indexOf('addonField_') == -1) {
                            func = 'addonField_content';
                        }
                        var OpenUrl = "{:url('Uploadimgnew/upload', ['num'=>30, 'path'=>'allimg', 'is_water'=>1])}";
                        OpenUrl += "&func=images_call_" + func;
                        top.layer.open({
                            type: 2,
                            title: '图片上传',
                            shade: layer_shade,
                            maxmin: false,
                            shadeClose: false,
                            area: ['1000px', '625px'],
                            content: OpenUrl
                        });
                    }
                </script>
                {/if}

                <script type="text/javascript">
                    var ue_{$vo.fieldArr}_{$vo.name|default=''} = UE.getEditor('{$vo.fieldArr}_{$vo.name|default=''}',{
                        serverUrl :"{:url('Ueditor/index',array('savepath'=>'allimg'))}",
                        zIndex: 999,
                        initialFrameWidth: "100%", //初化宽度
                        initialFrameHeight: 450, //初化高度
                        focus: false, //初始化时，是否让编辑器获得焦点true或false
                        maximumWords: 99999,
                        removeFormatAttributes: 'class,style,lang,width,height,align,hspace,valign',//允许的最大字符数 'fullscreen',
                        pasteplain:false, //是否默认为纯文本粘贴。false为不使用纯文本粘贴，true为使用纯文本粘贴
                        autoHeightEnabled: false,
                        toolbars: ueditor_toolbars
                    });

                    //必须在提交前渲染编辑器；
                    function {$vo.fieldArr}_{$vo.name|default=''}() {
                        //判断编辑模式状态:0表示【源代码】HTML视图；1是【设计】视图,即可见即所得；-1表示不可用
                        if(UE.getEditor("{$vo.fieldArr}_{$vo.name|default=''}").queryCommandState('source') != 0) {
                            UE.getEditor("{$vo.fieldArr}_{$vo.name|default=''}").execCommand('source'); //切换到【设计】视图
                        }
                    }

                    // 加载图片到编辑器指定位置
                    function images_call_{$vo.fieldArr}_{$vo.name|default=''}(fileurl_tmp) {
                        var addBody = '';
                        $.each(fileurl_tmp, function(index, items) {
                            addBody += '<p><img src="'+items+'"></p>';
                        });
                        UE.getEditor("{$vo.fieldArr}_{$vo.name|default=''}").execCommand('inserthtml', addBody);
                    }

                    // 自动远程图片本地化/自动清除非本站链接
                    function ajax_auto_editor_{$vo.fieldArr}_{$vo.name|default=''}(local,link) {
                        var body = UE.getEditor("{$vo.fieldArr}_{$vo.name|default=''}").getContent();
                        $.ajax({
                            type: 'POST',
                            url: "{:url('Archives/ajax_auto_editor')}",
                            data: {local:local,link:link,body:body,_ajax:1},
                            dataType: "JSON",
                            async:false,
                            success: function(res){
                                if (res.code == 1) {
                                    UE.getEditor("{$vo.fieldArr}_{$vo.name|default=''}").setContent(res.data.body);
                                }
                            }
                        });
                    }
                </script>
                {else/}
                <dl class="row">
                    <dt class="tit">
                        <label>{if condition="isset($vo['ifrequire']) AND !empty($vo['ifrequire'])"}<em>*</em>{/if}{$vo.title|default=''}</label>
                    </dt>
                    <dd class="opt">
                        {if condition="isset($vo['first']) AND 1 == $vo['first']"}
                        <label class="curpoin"><input type="checkbox" name="editor_remote_img_local" id="editor_remote_img_local" data-val="{$vo.editor.editor_remote_img_local}" value="1" {eq name="$vo.editor.editor_remote_img_local" value="1"} checked {/eq} >远程图片本地化</label>&nbsp;<!-- <a href="javascript:void(0);" onclick="editor_handle_210607(1);">[手动]</a>&nbsp; -->
                        <label class="curpoin"><input type="checkbox" name="editor_img_clear_link" id="editor_img_clear_link" data-val="{$vo.editor.editor_img_clear_link}" value="1" {eq name="$vo.editor.editor_img_clear_link" value="1"} checked {/eq} >清除非本站链接</label>&nbsp;<!-- <a href="javascript:void(0);" onclick="editor_handle_210607(2);">[手动]</a>&nbsp; -->
                        {/if}
                        <textarea id="{$vo.fieldArr}_{$vo.name|default=''}" name="{$vo.fieldArr}[{$vo.name|default=''}]">{$vo.dfvalue|default=''}</textarea>
                        <span class="err"></span>
                        <!-- <p class="notic">{$vo.remark|default=''}</p> -->
                    </dd>
                </dl>
                <script type="text/javascript">
                    // 加载编辑器，若存在编辑器则先进行销毁
                    try {
                        var elemtid = "{$vo.fieldArr}_{$vo.name|default=''}";
                        var editor = CKEDITOR.instances[elemtid];
                        if (editor) editor.destroy(true);
                    } catch(e) {}

                    CKEDITOR.replace(elemtid, {
                        filebrowserBrowseUrl: "{:url('Ueditor/index', ['savepath'=>'allimg', 'action'=>'uploadimage'])}",
                        filebrowserImageBrowseUrl: "{:url('Ueditor/index', ['savepath'=>'allimg', 'action'=>'uploadimage'])}",
                        filebrowserUploadUrl: "{:url('Ueditor/index', ['savepath'=>'allimg', 'action'=>'uploadimage'])}",
                        filebrowserImageUploadUrl: "{:url('Ueditor/index', ['savepath'=>'allimg', 'action'=>'uploadimage'])}",
                    });

                    // 自动远程图片本地化/自动清除非本站链接
                    function ajax_auto_editor_{$vo.fieldArr}_{$vo.name|default=''}(local,link) {
                        var body = CKEDITOR.instances.{$vo.fieldArr}_{$vo.name|default=''}.getData();
                        $.ajax({
                            type: 'POST',
                            url: "{:url('Archives/ajax_auto_editor')}",
                            data: {local:local,link:link,body:body,_ajax:1},
                            dataType: "JSON",
                            async:false,
                            success: function(res){
                                if (res.code == 1) {
                                    CKEDITOR.instances.{$vo.fieldArr}_{$vo.name|default=''}.setData(res.data.body);
                                }
                            }
                        });
                    }

                </script>
                {/eq}
                <!-- HTML文本 end -->
            {/case}
        {/switch}
    {/if}
{/volist}

<script type="text/javascript">
    $(function(){
        try {
            $("img").lazyload({
                effect: "fadeIn",
                placeholder : "__PUBLIC__/static/common/images/loading.gif",
                threshold: 200
            });
        }catch(e){}
        
        auto_notic_tips();
        /**
        * 自动小提示
        */
        function auto_notic_tips()
        {
            var html = '<a class="ui_tips" href="javascript:void(0);" onmouseover="layer_tips = layer.tips($(this).parent().find(\'p.notic\').html(), this, {time:100000});" onmouseout="layer.close(layer_tips);">提示</a>';
            $.each($('dd.opt > p.notic'), function(index, item){
                var a_length = $(item).prev("a.ui_tips").length;
                if ($(item).html() != '' && 0 == a_length) {
                    $(item).before(html);
                }
            });
        }

        // 第一个自定义字段显示感汉号提示
        var ganhaohtml = '<a class="ui_tips-ganhan" href="javascript:void(0);" onmouseover="layer_tips = layer.tips($(this).parent().find(\'p.notic-ganhan-ico\').html(), this, {time:100000});" onmouseout="layer.close(layer_tips);"></a><p class="notic-ganhan-ico none">属于自定义字段。在“更多功能>栏目字段”里管理</p>';
        $($('#FieldAddonextitem').find('dl > dd').find('p.notic').get(0)).parent().append(ganhaohtml);
    });
</script>