<?php if (!defined('THINK_PATH')) exit(); /*a:4:{s:30:"./template/pc/lists_single.htm";i:1698980534;s:52:"C:\phpstudy_pro\WWW\www.b.com\template\pc\header.htm";i:1752768631;s:52:"C:\phpstudy_pro\WWW\www.b.com\template\pc\footer.htm";i:1752772014;s:51:"C:\phpstudy_pro\WWW\www.b.com\template\pc\right.htm";i:1752743262;}*/ ?>
<!DOCTYPE html>
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<meta name="viewport" content="width=device-width, initial-scale=1, minimum-scale=1, maximum-scale=1">
<title><?php echo $eyou['field']['seo_title']; ?></title>
<meta name="description" content="<?php echo $eyou['field']['seo_description']; ?>" />
<meta name="keywords" content="<?php echo $eyou['field']['seo_keywords']; ?>" />
<link href="<?php  $tagGlobal = new \think\template\taglib\eyou\TagGlobal; $__VALUE__ = $tagGlobal->getGlobal("web_ico"); echo $__VALUE__; ?>" rel="shortcut icon" type="image/x-icon" />
<?php  $tagStatic = new \think\template\taglib\eyou\TagStatic; $__VALUE__ = $tagStatic->getStatic("skin/css/style.css","","","",""); echo $__VALUE__;  $tagStatic = new \think\template\taglib\eyou\TagStatic; $__VALUE__ = $tagStatic->getStatic("skin/css/guestbook.css","","","",""); echo $__VALUE__;  $tagStatic = new \think\template\taglib\eyou\TagStatic; $__VALUE__ = $tagStatic->getStatic("skin/css/about.css","","","",""); echo $__VALUE__; ?>
</head>
<body>
<div class="container"> <header class="web_head index_web_head">
  <div class="head_layer">
    <div class="layout">
      <figure class="logo"><a href="<?php  $tagGlobal = new \think\template\taglib\eyou\TagGlobal; $__VALUE__ = $tagGlobal->getGlobal("web_basehost"); echo $__VALUE__; ?>"><img src="<?php  $tagGlobal = new \think\template\taglib\eyou\TagGlobal; $__VALUE__ = $tagGlobal->getGlobal("web_logo"); echo $__VALUE__; ?>" alt="<?php  $tagGlobal = new \think\template\taglib\eyou\TagGlobal; $__VALUE__ = $tagGlobal->getGlobal("web_name"); echo $__VALUE__; ?>"></a></figure>
      <nav class="nav_wrap">
        <ul class="head_nav">
          <li <?php if(\think\Request::instance()->param('m') == 'Index'): ?> class="active"<?php endif; ?>><a href="http://***********:8046"><em>Home</em></a></li>
          <?php  if(isset($ui_typeid) && !empty($ui_typeid)) : $typeid = $ui_typeid; else: $typeid = ""; endif; if(empty($typeid) && isset($channelartlist["id"]) && !empty($channelartlist["id"])) : $typeid = intval($channelartlist["id"]); endif;  if(isset($ui_row) && !empty($ui_row)) : $row = $ui_row; else: $row = 100; endif; $tagChannel = new \think\template\taglib\eyou\TagChannel; $_result = $tagChannel->getChannel($typeid, "top", "active", ""); if(is_array($_result) || $_result instanceof \think\Collection || $_result instanceof \think\Paginator): $i = 0; $e = 1;$__LIST__ = is_array($_result) ? array_slice($_result,0, $row, true) : $_result->slice(0, $row, true); if( count($__LIST__)==0 ) : echo htmlspecialchars_decode("");else: foreach($__LIST__ as $key=>$field): $field["typename"] = text_msubstr($field["typename"], 0, 100, false); $__LIST__[$key] = $_result[$key] = $field;$i= intval($key) + 1;$mod = ($i % 2 ); ?>
          <li class="<?php echo $field['currentstyle']; ?>"><a href="<?php echo $field['typeurl']; ?>"><em><?php echo $field['typename']; ?></em></a> <?php if(!(empty($field['children']) || (($field['children'] instanceof \think\Collection || $field['children'] instanceof \think\Paginator ) && $field['children']->isEmpty()))): ?>
            <ul>
              <?php  if(isset($ui_typeid) && !empty($ui_typeid)) : $typeid = $ui_typeid; else: $typeid = ""; endif; if(empty($typeid) && isset($channelartlist["id"]) && !empty($channelartlist["id"])) : $typeid = intval($channelartlist["id"]); endif;  if(isset($ui_row) && !empty($ui_row)) : $row = $ui_row; else: $row = 100; endif;if(is_array($field['children']) || $field['children'] instanceof \think\Collection || $field['children'] instanceof \think\Paginator): $i = 0; $e = 1;$__LIST__ = is_array($field['children']) ? array_slice($field['children'],0,100, true) : $field['children']->slice(0,100, true); if( count($__LIST__)==0 ) : echo htmlspecialchars_decode("");else: foreach($__LIST__ as $key=>$field2): $field2["typename"] = text_msubstr($field2["typename"], 0, 100, false); $__LIST__[$key] = $_result[$key] = $field2;$i= intval($key) + 1;$mod = ($i % 2 ); ?>
              <li> <a href="<?php echo $field2['typeurl']; ?>"><em><?php echo $field2['typename']; ?></em></a> <?php if(!(empty($field2['children']) || (($field2['children'] instanceof \think\Collection || $field2['children'] instanceof \think\Paginator ) && $field2['children']->isEmpty()))): ?>
                <ul>
                  <?php  if(isset($ui_typeid) && !empty($ui_typeid)) : $typeid = $ui_typeid; else: $typeid = ""; endif; if(empty($typeid) && isset($channelartlist["id"]) && !empty($channelartlist["id"])) : $typeid = intval($channelartlist["id"]); endif;  if(isset($ui_row) && !empty($ui_row)) : $row = $ui_row; else: $row = 100; endif;if(is_array($field2['children']) || $field2['children'] instanceof \think\Collection || $field2['children'] instanceof \think\Paginator): $i = 0; $e = 1;$__LIST__ = is_array($field2['children']) ? array_slice($field2['children'],0,100, true) : $field2['children']->slice(0,100, true); if( count($__LIST__)==0 ) : echo htmlspecialchars_decode("");else: foreach($__LIST__ as $key=>$field3): $field3["typename"] = text_msubstr($field3["typename"], 0, 100, false); $__LIST__[$key] = $_result[$key] = $field3;$i= intval($key) + 1;$mod = ($i % 2 ); ?>
                  <li><a href="<?php echo $field3['typeurl']; ?>"><em><?php echo $field3['typename']; ?></em></a></li>
                  <?php ++$e; endforeach; endif; else: echo htmlspecialchars_decode("");endif; $field3 = []; ?>
                </ul>
                <?php endif; ?> </li>
              <?php ++$e; endforeach; endif; else: echo htmlspecialchars_decode("");endif; $field2 = []; ?>
            </ul>
            <?php endif; ?> </li>
          <?php ++$e; endforeach; endif; else: echo htmlspecialchars_decode("");endif; $field = []; ?>
        </ul>
        <!--<div class="head_right">
            <div class="change-language ensemble">
              <div class="change-language-title medium-title">
                <div class="language-flag language-flag-en"><a title="English" href="javascript:;"> <b class="country-flag"></b> <span>English</span> </a> </div>
              </div>
              <div class="change-language-cont sub-content"></div>
            </div>
          </div>--> 
      </nav>
    </div>
  </div>
  <section class="head_top">
    <div class="layout">
      <div class="head-search"> <?php  $tagSearchform = new \think\template\taglib\eyou\TagSearchform; $_result = $tagSearchform->getSearchform("","","","","","default"); if(is_array($_result) || $_result instanceof \think\Collection || $_result instanceof \think\Paginator): $i = 0; $e = 1; $__LIST__ = $_result;if( count($__LIST__)==0 ) : echo htmlspecialchars_decode("");else: foreach($__LIST__ as $key=>$field): $i= intval($key) + 1;$mod = ($i % 2 ); ?>
        <form class="search_form" method="get" action="<?php echo $field['action']; ?>" onsubmit="return searchForm();">
          <input class="search-ipt" type="text" name="keywords" id="keywords" placeholder="" />
          <button class="search-btn" type="submit"></button>
          <span id="btn-search"></span> <?php echo $field['hidden']; ?>
        </form>
        <?php ++$e; endforeach;endif; else: echo htmlspecialchars_decode("");endif; $field = []; ?> </div>
    </div>
  </section>
</header>


  <div class="wrap inner article">
    <div class="ey-position clean s1">
      <div class="title"><?php echo $eyou['field']['typename']; ?></div>
      <div class="position"> <?php  $typeid = ""; if(empty($typeid) && isset($channelartlist["id"]) && !empty($channelartlist["id"])) : $typeid = intval($channelartlist["id"]); endif;  $tagPosition = new \think\template\taglib\eyou\TagPosition; $__VALUE__ = $tagPosition->getPosition($typeid, "", ""); echo $__VALUE__; ?></div>
    </div>
    <div class="ey-smdy s1">
      <div id="ey_procon" class="contents">
        <div class="about_con">
          <div class="about_item w">
            <div class="title"><?php echo $eyou['field']['typename']; ?></div>
            <div class="con"> <?php echo $eyou['field']['content']; ?> </div>
          </div>
          <div class="clear"></div>
        </div>
      </div>
      <div class="ey_smly">
        <div class="ey_lytitle">
          <div class="title">Message</div>
          <span></span>
          <div class="txt">If you have any suggestions or question for us.Please contact us.</div>
        </div>
        <?php  $typeid = "2"; if(empty($typeid) && isset($channelartlist["id"]) && !empty($channelartlist["id"])) : $typeid = intval($channelartlist["id"]); endif;  $tagGuestbookform = new \think\template\taglib\eyou\TagGuestbookform; $_result = $tagGuestbookform->getGuestbookform($typeid, "default", "", 1); if(is_array($_result) || $_result instanceof \think\Collection || $_result instanceof \think\Paginator): $i = 0; $e = 1; $__LIST__ = $_result;if( count($__LIST__)==0 ) : echo htmlspecialchars_decode("");else: foreach($__LIST__ as $key=>$field): $i= intval($key) + 1;$mod = ($i % 2 ); ?>
        <form method="POST" action="<?php echo $field['action']; ?>" <?php echo $field['formhidden']; ?> onsubmit="<?php echo $field['submit']; ?>">
          <div class="rows input_rows">
            <input name="<?php echo $field['attr_5']; ?>" id='attr_5' type="text" class="input" size="50" maxlength="50" placeholder="<?php echo $field['itemname_5']; ?>"/>
            <font class="fc_red">*</font></div>
          <div class="rows input_rows">
            <input name="<?php echo $field['attr_3']; ?>" id='attr_3' type="text" class="input" size="30" maxlength="20" placeholder="<?php echo $field['itemname_3']; ?>"  />
            <font class="fc_red">*</font> </div>
          <div class="rows input_rows">
            <input name="<?php echo $field['attr_4']; ?>" id='attr_4' type="text" class="input" size="30" maxlength="100" placeholder="<?php echo $field['itemname_4']; ?>"/>
            <font class="fc_red">*</font></div>
          <div class="rows input_rows">
            <input name="<?php echo $field['attr_2']; ?>" id='attr_2'  type="text" class="input" size="30" maxlength="100" placeholder="<?php echo $field['itemname_2']; ?>"/>
            <font class="fc_red">*</font></div>
          <div class="rows textarea_rows">
            <textarea name="<?php echo $field['attr_6']; ?>" id='attr_6' class="form_area contents" placeholder="<?php echo $field['itemname_6']; ?>"></textarea>
            <font class="fc_red">*</font></div>
          <div class="clear"></div>
          <div class="rows">
            <input name="submit" type="submit" class="form_button" value="Submit"/>
          </div>
          <?php echo $field['hidden']; ?>
        </form>
        <?php ++$e; endforeach;endif; else: echo htmlspecialchars_decode("");endif; $field = []; ?> </div>
    </div>
  </div>
  <footer class="web_footer">
  <section class="foot_service">
    <div class="layout">
      <figure class="foot_logo"><img src="<?php  $tagGlobal = new \think\template\taglib\eyou\TagGlobal; $__VALUE__ = $tagGlobal->getGlobal("web_logo"); echo $__VALUE__; ?>" alt="<?php  $tagGlobal = new \think\template\taglib\eyou\TagGlobal; $__VALUE__ = $tagGlobal->getGlobal("web_attrname_6"); echo $__VALUE__; ?>"></figure>
      <ul class="foot_nav">
        <?php  if(isset($ui_typeid) && !empty($ui_typeid)) : $typeid = $ui_typeid; else: $typeid = ""; endif; if(empty($typeid) && isset($channelartlist["id"]) && !empty($channelartlist["id"])) : $typeid = intval($channelartlist["id"]); endif;  if(isset($ui_row) && !empty($ui_row)) : $row = $ui_row; else: $row = 100; endif; $tagChannel = new \think\template\taglib\eyou\TagChannel; $_result = $tagChannel->getChannel($typeid, "top", "active", ""); if(is_array($_result) || $_result instanceof \think\Collection || $_result instanceof \think\Paginator): $i = 0; $e = 1;$__LIST__ = is_array($_result) ? array_slice($_result,0, $row, true) : $_result->slice(0, $row, true); if( count($__LIST__)==0 ) : echo htmlspecialchars_decode("");else: foreach($__LIST__ as $key=>$field): $field["typename"] = text_msubstr($field["typename"], 0, 100, false); $__LIST__[$key] = $_result[$key] = $field;$i= intval($key) + 1;$mod = ($i % 2 ); ?>
        <li><a href="<?php echo $field['typeurl']; ?>"><?php echo $field['typename']; ?></a></li>
        <?php ++$e; endforeach; endif; else: echo htmlspecialchars_decode("");endif; $field = []; ?>
      </ul>
      <ul class="foot_sns">
        <li><a href=""><img src="<?php  $tagGlobal = new \think\template\taglib\eyou\TagGlobal; $__VALUE__ = $tagGlobal->getGlobal("web_templets_pc"); echo $__VALUE__; ?>/skin/images/sns01.png" alt=""></a></li>
        <li><a href=""><img src="<?php  $tagGlobal = new \think\template\taglib\eyou\TagGlobal; $__VALUE__ = $tagGlobal->getGlobal("web_templets_pc"); echo $__VALUE__; ?>/skin/images/sns02.png" alt=""></a></li>
        <li><a href=""><img src="<?php  $tagGlobal = new \think\template\taglib\eyou\TagGlobal; $__VALUE__ = $tagGlobal->getGlobal("web_templets_pc"); echo $__VALUE__; ?>/skin/images/sns03.png" alt=""></a></li>
        <li><a href=""><img src="<?php  $tagGlobal = new \think\template\taglib\eyou\TagGlobal; $__VALUE__ = $tagGlobal->getGlobal("web_templets_pc"); echo $__VALUE__; ?>/skin/images/sns04.png" alt=""></a></li>
      </ul>
    </div>
  </section>
  <section class="foot_bar">
    <div class="layout">
      <div class="copyright"><?php  $tagGlobal = new \think\template\taglib\eyou\TagGlobal; $__VALUE__ = $tagGlobal->getGlobal("web_copyright"); echo $__VALUE__; ?></div>
    </div>
  </section>
</footer>
<!-- 应用插件标签 start --> 
 <?php  $tagWeapp = new \think\template\taglib\eyou\TagWeapp; $__VALUE__ = $tagWeapp->getWeapp("default"); echo $__VALUE__; ?> 
<!-- 应用插件标签 end --> </div>
<!-- <aside class="scrollsidebar" id="scrollsidebar">
  <section class="side_content">
    <div class="side_list">
      <header class="hd"><img src="<?php  $tagGlobal = new \think\template\taglib\eyou\TagGlobal; $__VALUE__ = $tagGlobal->getGlobal("web_templets_pc"); echo $__VALUE__; ?>/skin/images/title_pic.png" alt=""/></header>
      <div class="cont">
        <li><a class="email" href="javascript:">Send Inquiry</a></li>
        <li><a class="skype" href="skype:cmhello">Skype Chat</a></li>
        <li><a class="inquiry" href="mailto:<?php  $tagGlobal = new \think\template\taglib\eyou\TagGlobal; $__VALUE__ = $tagGlobal->getGlobal("web_attr_2"); echo $__VALUE__; ?>">Send Email</a></li>
      </div>
      <div class="t-code"><img src="<?php  $tagGlobal = new \think\template\taglib\eyou\TagGlobal; $__VALUE__ = $tagGlobal->getGlobal("web_attr_7"); echo $__VALUE__; ?>"></div>
      <div class="side_title">LiveChat<a  class="close_btn"><span>关闭</span></a></div>
    </div>
  </section>
  <div class="show_btn"></div>
</aside> -->
<div class="inquiry-pop-bd">
  <div class="inquiry-pop"><i class="ico-close-pop" onclick="hideMsgPop();"></i>
    <div class="content-wrap form" style="width: 100%;height: 100%;overflow: hidden;">
      <div class="ch_form_wrap">
        <div class="email_point"> </div>
        <h1 class="title"><em>Leave Your Message</em> </h1>
        <?php  $typeid = "2"; if(empty($typeid) && isset($channelartlist["id"]) && !empty($channelartlist["id"])) : $typeid = intval($channelartlist["id"]); endif;  $tagGuestbookform = new \think\template\taglib\eyou\TagGuestbookform; $_result = $tagGuestbookform->getGuestbookform($typeid, "default", "", 1); if(is_array($_result) || $_result instanceof \think\Collection || $_result instanceof \think\Paginator): $i = 0; $e = 1; $__LIST__ = $_result;if( count($__LIST__)==0 ) : echo htmlspecialchars_decode("");else: foreach($__LIST__ as $key=>$field): $i= intval($key) + 1;$mod = ($i % 2 ); ?>
        <form method="POST" action="<?php echo $field['action']; ?>" <?php echo $field['formhidden']; ?> onsubmit="<?php echo $field['submit']; ?>" class="ch_form">
          <ul>
            <li class="item item_name">
              <input type="text" name="<?php echo $field['attr_2']; ?>" id="attr_2" placeholder="* <?php echo $field['itemname_2']; ?>"/>
            </li>
            <li class="item item_email">
              <input type="text" name="<?php echo $field['attr_4']; ?>" id="attr_4" placeholder="* <?php echo $field['itemname_4']; ?>"/>
            </li>
            <li class="item item_phone">
              <input type="text" name="<?php echo $field['attr_3']; ?>" id="attr_3" placeholder="<?php echo $field['itemname_3']; ?>"/>
            </li>
            <li class="item item_message">
              <textarea name="<?php echo $field['attr_5']; ?>" id="attr_5" placeholder="* <?php echo $field['itemname_5']; ?>"></textarea>
            </li>
          </ul>
          <div class="item item_submit">
            <input type="submit" value="Send" name="submit" class="submit_btn gtm_submit_btn" style="margin:0" />
          </div>
          <?php echo $field['hidden']; ?>
        </form>
        <?php ++$e; endforeach;endif; else: echo htmlspecialchars_decode("");endif; $field = []; ?> </div>
    </div>
  </div>
</div>
<div class="web-search"> <b id="btn-search-close" class="btn--search-close"></b>
  <div style="width:100%">
    <div class="head-search"> <?php  $tagSearchform = new \think\template\taglib\eyou\TagSearchform; $_result = $tagSearchform->getSearchform("","","","","","default"); if(is_array($_result) || $_result instanceof \think\Collection || $_result instanceof \think\Paginator): $i = 0; $e = 1; $__LIST__ = $_result;if( count($__LIST__)==0 ) : echo htmlspecialchars_decode("");else: foreach($__LIST__ as $key=>$field): $i= intval($key) + 1;$mod = ($i % 2 ); ?>
      <form method="get" action="<?php echo $field['action']; ?>" onsubmit="return searchForm();">
        <input class="search-ipt" type="text" name="keywords" id="keywords" placeholder="Start Typing..." />
        <input class="search-btn" type="submit"/>
        <span class="search-attr">Hit enter to search or ESC to close</span> <?php echo $field['hidden']; ?>
      </form>
      <?php ++$e; endforeach;endif; else: echo htmlspecialchars_decode("");endif; $field = []; ?> </div>
  </div>
</div>
<?php  $tagStatic = new \think\template\taglib\eyou\TagStatic; $__VALUE__ = $tagStatic->getStatic("skin/js/jquery-3.7.0.min.js","","","",""); echo $__VALUE__;  $tagStatic = new \think\template\taglib\eyou\TagStatic; $__VALUE__ = $tagStatic->getStatic("skin/js/jquery-migrate.min.js","","","",""); echo $__VALUE__;  $tagStatic = new \think\template\taglib\eyou\TagStatic; $__VALUE__ = $tagStatic->getStatic("skin/js/wow.js","","","",""); echo $__VALUE__;  $tagStatic = new \think\template\taglib\eyou\TagStatic; $__VALUE__ = $tagStatic->getStatic("skin/js/swiper4.4.2.js","","","",""); echo $__VALUE__;  $tagStatic = new \think\template\taglib\eyou\TagStatic; $__VALUE__ = $tagStatic->getStatic("skin/js/zoom.js","","","",""); echo $__VALUE__;  $tagStatic = new \think\template\taglib\eyou\TagStatic; $__VALUE__ = $tagStatic->getStatic("skin/js/scrollsidebar.js","","","",""); echo $__VALUE__;  $tagStatic = new \think\template\taglib\eyou\TagStatic; $__VALUE__ = $tagStatic->getStatic("skin/js/common.js","","","",""); echo $__VALUE__; ?> 
<!--<ul class="prisna-wp-translate-seo" id="prisna-translator-seo">
  <li class="language-flag language-flag-en"> <a title="English" href="javascript:;"> <b class="country-flag"></b> <span>English</span> </a> </li>
</ul>--> 

</body>
</html>