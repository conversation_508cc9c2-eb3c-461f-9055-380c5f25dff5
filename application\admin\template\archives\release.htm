{include file="public/layout" /}

<body class="bodystyle">
<div id="toolTipLayer" style="position: absolute; z-index: 9999; display: none; visibility: visible; left: 95px; top: 573px;"></div>
<div id="append_parent"></div>
<div id="ajaxwaitid"></div>
<div class="page ksedit">
    <div class="fixed-bar">
        <div class="item-title">
            <div class="subject">
                <h3><i class="fa fa-edit"></i>快捷发布</h3>
                <h5></h5>
            </div>
        </div>
    </div>
    <form class="form-horizontal" id="post_form" action="{:url('Archives/release')}" method="post">
        <div class="ncap-form-default">
            <dl class="row">
                <dt class="tit">
                    <label for="title"><em>*</em>发布至</label>
                </dt>
                <dd class="opt">
                    <select name="typeid" id="typeid" style="width: 300px;" size="25">
                        {$select_html}
                    </select>
                    <span class="err"></span>
                    <p class="notic"></p>
                </dd>
            </dl>
        </div>
    </form>
</div>
<script type="text/javascript">
    $(function () {
        $('#typeid').find('option').click(function(){
            check_submit();
        });
    });

    // 判断输入框是否为空
    function check_submit(){
        if (!$('#typeid').val()) {
            layer.alert('请选择栏目…！', {icon:5, title:false}, function(index){
                $('#typeid').val('');
                layer.close(index);
            });
            return false;
        }
        var current_channel = $('#typeid').find('option:selected').attr('data-current_channel');
        current_channel = parseInt(current_channel);
        var js_allow_channel_arr = {$js_allow_channel_arr};
        if ($.inArray(current_channel, js_allow_channel_arr) == -1) {
            layer.alert('该栏目模型不允许发布文档！', {icon:5, title:false}, function(index){
                $('#typeid').val('');
                layer.close(index);
            });
            return false;
        }
        layer_loading('正在处理');
        $('#post_form').submit();
    }
</script>

{include file="public/footer" /}