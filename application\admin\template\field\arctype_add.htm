{include file="public/layout" /}

<body class="bodystyle">
<div id="toolTipLayer" style="position: absolute; z-index: 9999; display: none; visibility: visible; left: 95px; top: 573px;"></div>
<div id="append_parent"></div>
<div id="ajaxwaitid"></div>
<div class="page">
    <form class="form-horizontal" id="post_form" action="{:url('Field/arctype_add')}" method="post">
        <!-- 常规选项 -->
        <div class="ncap-form-default tab_div_1">
            <dl class="row">
                <dt class="tit">
                    <label for="title"><em>*</em>字段名称</label>
                </dt>
                <dd class="opt">
                    <input type="text" value="" name="title" id="title" class="input-txt">
                    <p class="notic"></p>
                </dd>
            </dl>
            <dl class="row">
                <dt class="tit">
                    <label for="name"><em>*</em>字段标识</label>
                </dt>
                <dd class="opt">
                    <input type="text" value="" name="name" id="name" placeholder="支持字母、数字和下划线，不能和别的标识值相同" class="input-txt" onkeyup="this.value=this.value.replace(/[^0-9a-zA-Z_]/g,'');" onbeforepaste="clipboardData.setData('text',clipboardData.getData('text').replace(/[^0-9a-zA-Z_]/g,''));">
                    <p class="notic">保持唯一性，不可与主表、附加表重复</p>
                </dd>
            </dl>
            <dl class="row">
                <dt class="tit">
                    <label for="dtype"><em>*</em>字段类型</label>
                </dt>
                <dd class="opt">
                    {volist name='$fieldtype_list' id='vo'}
                        <div style="width: 150px; float: left;">
                            <label><input type="radio" name="dtype" value="{$vo.name}" data-ifoption="{$vo.ifoption|default=0}" data-text="{$vo.title}" {eq name='$i' value='1'} checked="checked" {/eq}>{$vo.title}</label>&nbsp;
                        </div>
                        {if condition="$i % 4 == 0"}<br/>{/if}
                    {/volist}
                    <span class="err"></span>
                    <p class="notic"></p>
                </dd>
            </dl>
            <dl class="row" id="dl_dfvalue">
                <dt class="tit">
                    <label id="label_dfvalue">默认值</label>
                </dt>
                <dd class="opt">
                    <textarea rows="5" cols="60" id="dfvalue" name="dfvalue" placeholder="如果定义字段类型为下拉框、单选项、多选项时，此处填写被选择的项目(用“,”分开，如“男,女,人妖”)。" style="height:60px;"></textarea>
                    <span class="err"></span>
                    <p class="notic">1、如果定义字段类型为下拉框、单选项、多选项时，此处填写被选择的项目(用“,”分开，如“男,女,人妖”)，不支持反斜杠 \ ，可以用斜杠 / 代替。<br/>2、特殊符号会被过滤掉，比如：&、=、?等<br/>3、定义字段类型为多选项时，默认值最多为64项，超出则截取前64项</p>
                </dd>
            </dl>
            <dl class="row">
                <dt class="tit">
                    <label>提示文字</label>
                </dt>
                <dd class="opt">          
                    <textarea rows="5" cols="60" id="remark" name="remark" placeholder="问号提示文字" style="height:60px;"></textarea>
                    <span class="err"></span>
                    <p class="notic">问号提示文字</p>
                </dd>
            </dl>
<!--             <dl class="row">
                <dt class="tit">
                    <label for="sort_order">排序</label>
                </dt>
                <dd class="opt">
                    <input type="text" value="100" name="sort_order" id="sort_order" class="input-txt">
                    <p class="notic">越小越靠前</p>
                </dd>
            </dl> -->
            <dl class="row">
                <dt class="tit">
                    <label for="title" id="select_title">指定栏目</label>
                </dt>
                <dd class="opt">
                    <select name="typeids[]" id="typeid" style="width: 300px;" size="15" multiple="true">
                        <option value="0" {if condition="empty($typeids) || in_array(0, $typeids)"}selected="true"{/if}>
                        —指定所有栏目—
                        </option>
                        {$select_html}
                    </select>
                    <span class="err"></span>
                    <p class="red">(按 Ctrl 可以进行多选)</p>
                </dd>
            </dl>
        </div>
        <!-- 常规选项 -->
        <div class="ncap-form-default">
            <div class="bot">
                <input type="hidden" name="channel_id" id="channel_id" value="{$channel_id|default='-99'}">
                <a href="JavaScript:void(0);" onclick="check_submit();" class="ncap-btn-big ncap-btn-green" id="submitBtn">确认提交</a>
            </div>
        </div> 
    </form>
</div>
<script type="text/javascript">
    $(function(){
        dtype_change($('input[name=dtype]:checked'));
        $('input[name=dtype]').click(function(){
            dtype_change(this);
        });

        function dtype_change(obj){
            var dtype = $(obj).val();
            var ifoption = $(obj).data('ifoption');
            if (0 <= $.inArray(dtype, ['datetime','switch','img','imgs','files'])) {
                $('#dl_dfvalue').hide();
            } else {
                if (1 == ifoption) {
                    $('#label_dfvalue').html('<em>*</em>默认值');
                } else {
                    $('#label_dfvalue').html('默认值');
                }
                $('#dl_dfvalue').show();
            }
        }
    });

    var parentObj = parent.layer.getFrameIndex(window.name); //先得到当前iframe层的索引
    function check_submit(){
        if($('input[name="title"]').val() == ''){
            showErrorMsg('字段标题不能为空！');
            $('input[name=title]').focus();
            return false;
        }
        var name = $('input[name="name"]').val();
        var ret1 = /^[_]+$/;
        var ret2 = /^[\w]+$/;
        var ret3 = /^[0-9]+$/;
        if (ret1.test(name) || !ret2.test(name)) {
            showErrorMsg('字段名称格式不正确！');
            $('input[name=name]').focus();
            return false;
        } else if (ret3.test(name)) {
            showErrorMsg('字段名称不能纯数字！');
            $('input[name=name]').focus();
            return false;
        }
        if($('input[name=dtype]:checked').val() == ''){
            showErrorMsg('请选择字段类型！');
            $('input[name=dtype]').focus();
            return false;
        } else {
            var ifoption = $('input[name=dtype]:checked').data('ifoption');
            if (1 == ifoption) {
                if ($.trim($('#dfvalue').val()) == '') {
                    showErrorMsg('默认值不能为空！');
                    $('#dfvalue').focus();
                    return false;
                }
            }
            
            if (0 <= $.inArray($('input[name=dtype]:checked').val(), ['radio','checkbox','select'])) {
                var dfvalue = $.trim($('#dfvalue').val());
                if(dfvalue.indexOf('\\') != -1){
               　　  showErrorMsg('默认值不支持反斜杠 \\ ，可用斜杠 / 代替');
                    $('#dfvalue').focus();
                    return false;
            　　}
                data = dfvalue.split(',');
                for(var i = 0;i < data.length ;i++) {
                    for(var j = i+1;j < data.length;j++) {
                        if ($.trim(data[i]) == $.trim(data [j])){
                            showErrorMsg('默认值不能含有相同的值！');
                            $('#dfvalue').focus();
                            return false;
                        }
                    }
                }
                if (0 <= $.inArray($('input[name=dtype]:checked').val(), ['checkbox'])) {
                    if (64 < data.length) {
                        showErrorMsg('默认值最多填写64项！');
                        $('#dfvalue').focus();
                        return false;
                    }
                }
            }
        }

        layer_loading('正在处理');
        $.ajax({
            type : 'post',
            url : "{:url('Field/arctype_add', ['_ajax'=>1])}",
            data : $('#post_form').serialize(),
            dataType : 'json',
            success : function(res){
                layer.closeAll();
                if(res.code == 1){
                    var _parent = parent;
                    _parent.layer.close(parentObj);
                    _parent.layer.msg(res.msg, {shade: layer_shade, time: 1000}, function(){
                        _parent.window.location.reload();
                    });
                }else{
                    showErrorMsg(res.msg);
                }
            },
            error: function(e){
                layer.closeAll();
                layer.alert(e.responseText, {icon: 5, title:false});
            }
        });
    }
</script>

{include file="public/footer" /}