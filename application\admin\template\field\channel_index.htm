{include file="public/layout" /}

<body class="bodystyle" style="cursor: default; -moz-user-select: inherit;">
<div id="append_parent"></div>
<div id="ajaxwaitid"></div>
<div class="page min-hg-c-10">
    {notempty name="$channel_id"}
    <div class="fixed-bar">
        <div class="item-title">
            <a class="back_xin" href="{:url('Channeltype/index')}"  title="返回"><i class="iconfont e-fanhui"></i></a>
            <div class="subject">
                <h3>{$channeltype_row[$channel_id]['ntitle']|default='无'}字段</h3>
                <h5></h5>
            </div>
            <ul class="tab-base nc-row">
                <li><a href="{:url('Channeltype/edit', ['id'=>$channel_id])}" {eq name='$Request.action' value='edit'}class="current"{/eq}><span>模型编辑</span></a></li>
                <li><a href="{:url('Field/channel_index', ['channel_id'=>$channel_id])}" {eq name='$Request.action' value='channel_index'}class="current"{/eq}><span>字段管理</span></a></li>
            </ul>
        </div>
    </div>
    {else/}
    <!-- 全部模型字段管理 -->
    {include file="channeltype/bar" /}
    {/notempty}
    <div class="flexigrid">
        <div class="mDiv pt0">
            <div class="ftitle">
				{eq name="$Think.const.CONTROLLER_NAME.'@channel_add'|is_check_access" value="1"}
				<div class="fbutton">
                    <a href="javascript:void(0);" {notempty name="$channel_id"} data-href="{:url('Field/channel_add', array('channel_id'=>$channel_id))}" {else/} data-href="{:url('Field/channel_add', array('type'=>'all'))}" {/notempty} onclick="openFullframe(this, '新增字段', '100%', '100%');">
				        <div class="add">
				            <span><i class="layui-icon layui-icon-addition"></i>新增字段</span>
				        </div>
				    </a>
				</div>
				{/eq}
            </div>
            <form id="searchForm" class="navbar-form form-inline" action="{:url('Field/channel_index')}" method="get" onsubmit="layer_loading('正在处理');">
                {$searchform.hidden|default=''}
                <div class="sDiv">
                    <div class="sDiv2">  
                        <select id="select_channel" class="select" style="margin:0px 5px;">
                            <option value="">--全部模型--</option>
                            {volist name="$channeltype_row" id="vo"}
                                <option value="{$vo.id}" {eq name="$channel_id" value="$vo.id"}selected{/eq}>{$vo.title}</option>
                            {/volist}
                        </select>
                    </div>
                    <div class="sDiv2">
                        <input type="hidden" name="searchopt" value="1">
                        <input type="hidden" name="channel_id" id="channel_id" value="{$channel_id|default='1'}">
                        <input type="text" size="30" name="keywords" value="{$Request.param.keywords}" class="qsbox" placeholder="标题/名称搜索...">
                        <input type="submit" class="btn" value="搜索">
                        <i class="iconfont e-sousuo"></i>
                    </div>
                </div>
            </form>
        </div>
        <div class="hDiv">
            <div class="hDivBox">
                <table cellspacing="0" cellpadding="0" style="width: 100%">
                    <thead>
                    <tr>
                        <th abbr="article_title" axis="col3" class="w50">
                            <div class="tc">ID</div>
                        </th>
                        <th abbr="article_title" axis="col3" class="">
                            <div class="text-l10">字段标题</div>
                        </th>
                        <th abbr="article_time" axis="col6" class="w160">
                            <div class="tc">所属模型</div>
                        </th>
                        <th abbr="article_time" axis="col6" class="w160">
                            <div class="tc">字段名称</div>
                        </th>
                        <th abbr="article_time" axis="col6" class="w120">
                            <div class="tc">字段类型</div>
                        </th>
                        <!-- <th abbr="article_time" axis="col6" class="w70">
                            <div class="tc">字段分类</div>
                        </th> -->
                        <th abbr="article_time" axis="col6" class="w100">
                            <div class="tc">创建时间</div>
                        </th>
                        <th axis="col1" class="w220">
                            <div class="tc">操作</div>
                        </th>
                        <th abbr="article_time" axis="col6" class="w60">
                            <div class="tc">排序</div>
                        </th>
                    </tr>
                    </thead>
                </table>
            </div>
        </div>
        <div class="bDiv" style="height: auto;">
            <div id="flexigrid" cellpadding="0" cellspacing="0" border="0">
                <table style="width: 100%;">
                    <tbody>
                    {empty name="list"}
                        <tr>
                            <td class="no-data" align="center" axis="col0" colspan="50">
                                <div class="no_row">
                                    <div class="no_pic"><img src="__SKIN__/images/null-data.png"></div>
                                </div>
                            </td>
                        </tr>
                    {else/}
                        {foreach name="list" item="vo" key="k" }
                        <tr>
                            <td class="sort">
                                <div class="tc w50">
                                    {if condition="$pager->nowPage > 1"}
                                        {$pager->listRows * ($pager->nowPage-1) + $k+1}
                                    {else /}
                                        {$k+1}
                                    {/if}
                                </div>
                            </td>
                            <td style="width: 100%">
                                <div class="text-l10">
                                {if condition="($vo['ifsystem'] eq 1) OR (1 eq $vo['ifmain'])"}
                                    {$vo.title}
                                {else /}
                                    {eq name="$Think.const.CONTROLLER_NAME.'@channel_edit'|is_check_access" value="1"}
                                    <a href="javascript:void(0);" {notempty name="$channel_id"} data-href="{:url('Field/channel_edit',array('channel_id'=>$vo['channel_id'],'id'=>$vo['id']))}" {else/} data-href="{:url('Field/channel_edit',array('channel_id'=>$vo['channel_id'],'id'=>$vo['id'],'type'=>'all'))}" {/notempty} onclick="openFullframe(this, '编辑字段', '100%', '100%');">{$vo.title}</a>
                                    {else /}
                                    {$vo.title}
                                    {/eq}
                                {/if}
                                </div>
                            </td>
                            <td class="">
                                <div class="w160 tc">
                                    {$channeltype_list[$vo.channel_id]['title']}
                                </div>
                            </td>
                            <td class="">
                                <div class="w160 tc">
                                    {$vo.name}
                                </div>
                            </td>
                            <td class="">
                                <div class="w120 tc">
                                    {$fieldtypeList[$vo['dtype']]['title']|default='数据错误'}
                                </div>
                            </td>
                            <!-- <td>
                                <div class="tc w70">
                                    {if condition="($vo['ifsystem'] eq 1) OR (1 eq $vo['ifmain'])"}
                                        系统
                                    {else /}
                                        自定义
                                    {/if}
                                </div>
                            </td> -->
                            <td>
                                <div class="w100 tc">
                                    {$vo.add_time|date='Y-m-d',###}
                                </div>
                            </td>
                            <td  class="operation">
                                <div class="w220 tc">
                                {eq name="'Field@ajax_channel_show'|is_check_access" value="1"}
                                {if condition="$vo['ifcontrol'] eq 1"}
                                <a class="btn grey"  href="javascript:void(0);" data-id="{$vo.id}" data-ifeditable="{eq name='$vo.ifeditable' value='1'}0{else /}1{/eq}">{if condition="$vo['ifeditable'] eq 1"}隐藏{else /}显示{/if}</a>
                                {else /}
                                <a class="btn blue"  href="javascript:void(0);" data-url="{:url('Field/ajax_channel_show')}" data-id="{$vo.id}" data-ifeditable="{eq name='$vo.ifeditable' value='1'}0{else /}1{/eq}" onClick="handleShow(this);">{if condition="$vo['ifeditable'] eq 1"}隐藏{else /}显示{/if}</a>
                                {/if}
                                {/eq}
								<i></i>
                                {if condition="($vo['ifsystem'] eq 1) OR (1 eq $vo['ifmain'])"}
                                    {eq name="$Think.const.CONTROLLER_NAME.'@channel_edit'|is_check_access" value="1"}
                                    <a href="javascript:void(0);" class="btn grey">编辑</a>
                                    {/eq}
									<i></i>
                                    {eq name="$Think.const.CONTROLLER_NAME.'@channel_del'|is_check_access" value="1"}
                                    <a class="btn grey"  href="javascript:void(0);"  data-id="{$vo.id}">删除</a>
                                    {/eq}
                                {else /}
                                    {eq name="$Think.const.CONTROLLER_NAME.'@channel_edit'|is_check_access" value="1"}
                                    <a href="javascript:void(0);" {notempty name="$channel_id"} data-href="{:url('Field/channel_edit',array('channel_id'=>$vo['channel_id'],'id'=>$vo['id']))}" {else/} data-href="{:url('Field/channel_edit',array('channel_id'=>$vo['channel_id'],'id'=>$vo['id'],'type'=>'all'))}" {/notempty} class="btn blue" onclick="openFullframe(this, '编辑字段', '100%', '100%');">编辑</a>
                                    {/eq}
									<i></i>
                                    {eq name="$Think.const.CONTROLLER_NAME.'@channel_del'|is_check_access" value="1"}
                                    <a class="btn red"  href="javascript:void(0);" data-url="{:url('Field/channel_del', array('channel_id'=>$vo['channel_id']))}" data-id="{$vo.id}" onClick="delfun(this);">删除</a>
                                    {/eq}
                                {/if}
								<i></i>
                                <a class="btn blue" href="javascript:void(0);" data-name="{$vo.name}" data-channel_id="{$vo['channel_id']|default='0'}" data-dtype="{$vo.dtype}" data-ifmain="{$vo.ifmain}" data-is_screening="{$vo.is_screening}" onclick="copyToClipBoard(this)">标签调用</a>
                                </div>
                            </td>
                            <td class="sort">
                                <div class="w60 tc">
                                {if condition="empty($vo['ifmain'])"}
                                    {eq name="$Think.const.CONTROLLER_NAME.'@channel_edit'|is_check_access" value="1"}
                                    <input class="tc" onchange="changeTableVal('channelfield','id','{$vo.id}','sort_order',this);"  size="4"  value="{$vo.sort_order}" />
                                    {else /}
                                    {$vo.sort_order}
                                    {/eq}
                                {else /}
                                    ———
                                {/if}
                                </div>
                            </td>
                            
                        </tr>
                        {/foreach}
                    {/empty}
                    </tbody>
                </table>
            </div>
            <div class="iDiv" style="display: none;"></div>
        </div>
        <div class="tDiv">
            <div class="tDiv2">
				{include file="public/page" /}
            <div style="clear:both"></div>
        </div>
    </div>
</div>
<script type="text/javascript">
    $(document).ready(function(){

        // 表格行点击选中切换
        $('#flexigrid > table>tbody >tr').click(function(){
            $(this).toggleClass('trSelected');
        });

        // 点击刷新数据
        $('.fa-refresh').click(function(){
            location.href = location.href;
        });

        /*模型下拉框跳转*/
        $('#select_channel').change(function(){
            var channel_id = $(this).val();
            var url = "{:url('Field/channel_index')}";
            if (url.indexOf('?') > -1) {
                url += '&';
            } else {
                url += '?';
            }
            url += 'channel_id='+channel_id;
            layer_loading('正在处理');
            window.location.href = url;
        });
        /*--end*/
    });

    function delfun(obj){
      var title = $(obj).attr('data-typename');
      layer.confirm('确认删除？', {
          shade: layer_shade,
          area: ['480px', '190px'],
          move: false,
          title: '提示',
          btnAlign:'r',
          closeBtn: 3,
          btn: ['确定', '取消'] ,//按钮
          success: function () {
                $(".layui-layer-content").css('text-align', 'left');
            }
        }, function(){
          layer_loading('正在处理');
        // 确定
          $.ajax({
            type : 'post',
            url : $(obj).attr('data-url'),
            data : {del_id:$(obj).attr('data-id'), _ajax:1},
            dataType : 'json',
            success : function(data){
              layer.closeAll();
              if(data.code == 1){
                layer.msg(data.msg, {icon: 1});
                window.location.reload();
                // $('tr[data-id="'+$(obj).attr('data-id')+'"]').remove();
              }else{
                layer.alert(data.msg, {icon: 2, title:false});
              }
            }
          })
        }, function(index){
          layer.close(index);
        }
      );
      return false;
    }  

    /**
     * 标签调用js
     * @param string  fieldname 字段名称
     * @param intval  channel_id 模型ID
     */
    function copyToClipBoard(obj) {
        var fieldname = $(obj).attr('data-name');
        var channel_id = $(obj).attr('data-channel_id');
        var dtype = $(obj).attr('data-dtype');
        var ifmain = $(obj).attr('data-ifmain');
        var is_screening = $(obj).attr('data-is_screening');
        var height = '280px';
        var contentdiv = '<div class="dialog_content" style="margin: 0px; padding: 0px;"><dl style="padding:10px 30px;line-height:30px;">';
        if (6 == channel_id) {
            switch(dtype)
            {
                case 'imgs':
                    {
                        height = '400px';
                        var viewstr = "{eyou:diyfield type='"+dtype+"' id='"+fieldname+"' name='$"+"eyou.field."+fieldname+"'}\r\n    <img src='{$"+fieldname+".image_url}' />\r\n    注释：{$"+fieldname+".intro}\r\n{/eyou:diyfield";
                        contentdiv += '<dd>列表/内容页：</dd>';
                        contentdiv += '<dd><textarea rows="4" cols="60" style="width:470px;height:80px;">' + viewstr + '}</textarea></dd>';
                        var liststr = "{eyou:type typeid='栏目ID' id='eyoav' addfields='"+fieldname+"'}\r\n    {eyou:diyfield type='"+dtype+"' id='"+fieldname+"' name='$"+"eyoav."+fieldname+"'}\r\n        <img src='{$"+fieldname+".image_url}' />\r\n        注释：{$"+fieldname+".intro}\r\n    {/eyou:diyfield"+"}\r\n{/eyou:type";
                        contentdiv += '<dd>标签 arclist / list 内调用 <font color="red">(更多简洁调法请参考标签 arclist /list)</font>：</dd>';
                        contentdiv += '<dd><textarea rows="4" cols="60" style="width:470px;height:120px;">' + liststr + '}</textarea></dd>';
                    }
                    break;

                case 'checkbox':
                    {
                        height = '400px';
                        var viewstr = "{eyou:diyfield type='"+dtype+"' id='"+fieldname+"' name='$"+"eyou.field."+fieldname+"'}\r\n    {$"+fieldname+".value}\r\n{/eyou:diyfield";
                        contentdiv += '<dd>列表/内容页：</dd>';
                        contentdiv += '<dd><textarea rows="4" cols="60" style="width:470px;height:60px;">' + viewstr + '}</textarea></dd>';
                        var liststr = "{eyou:type typeid='栏目ID' id='eyoav' addfields='"+fieldname+"'}\r\n    {eyou:diyfield type='"+dtype+"' id='"+fieldname+"' name='$"+"eyoav."+fieldname+"'}\r\n        {$"+fieldname+".value}\r\n    {/eyou:diyfield"+"}\r\n{/eyou:type";
                        contentdiv += '<dd>标签 arclist / list 内调用 <font color="red">(更多简洁调法请参考标签 arclist /list)</font>：</dd>';
                        contentdiv += '<dd><textarea rows="4" cols="60" style="width:470px;height:120px;">' + liststr + '}</textarea></dd>';
                    }
                    break;

                case 'datetime':
                    {
                        contentdiv += '<dd>列表/内容页：</dd>';
                        contentdiv += '<dd><input type="text" style=" width:470px;margin-bottom:10px;" value="{$'+'eyou.field.'+fieldname+'|MyDate=\'Y-m-d H:i:s\',###}"></dd>';
                        var liststr = "{eyou:type typeid='栏目ID' addfields='"+fieldname+"'}\r\n    {$"+"field."+fieldname+"|MyDate=\'Y-m-d H:i:s\',###}\r\n{/eyou:type";
                        contentdiv += '<dd>标签 type 属性 addfields 调用：</dd>';
                        contentdiv += '<dd><textarea rows="4" cols="60" style="width:470px;height:60px;">' + liststr + '}</textarea></dd>';
                    }
                    break;

                default:
                    {
                        contentdiv += '<dd>列表/内容页：</dd>';
                        contentdiv += '<dd><input type="text" style=" width:470px;margin-bottom:10px;" value="{$'+'eyou.field.'+fieldname+'}"></dd>';
                        var liststr = "{eyou:type typeid='栏目ID' addfields='"+fieldname+"'}\r\n    {$"+"field."+fieldname+"}\r\n{/eyou:type";
                        contentdiv += '<dd>标签 type 属性 addfields 调用：</dd>';
                        contentdiv += '<dd><textarea rows="4" cols="60" style="width:470px;height:60px;">' + liststr + '}</textarea></dd>';
                    }
                    break;
            }

        } else {
            switch(dtype)
            {
                case 'imgs':
                    {
                        height = '400px';
                        var viewstr = "{eyou:diyfield type='"+dtype+"' id='"+fieldname+"' name='$"+"eyou.field."+fieldname+"'}\r\n    <img src='{$"+fieldname+".image_url}' />\r\n    注释：{$"+fieldname+".intro}\r\n{/eyou:diyfield";
                        contentdiv += '<dd>内容页：</dd>';
                        contentdiv += '<dd><textarea rows="4" cols="60" style="width:470px;height:80px;">' + viewstr + '}</textarea></dd>';
                        var liststr = "{eyou:arcview aid='$"+"field.aid' id='eyoav' addfields='"+fieldname+"'}\r\n    {eyou:diyfield type='"+dtype+"' id='"+fieldname+"' name='$"+"eyoav."+fieldname+"'}\r\n        <img src='{$"+fieldname+".image_url}' />\r\n        注释：{$"+fieldname+".intro}\r\n    {/eyou:diyfield"+"}\r\n{/eyou:arcview";
                        contentdiv += '<dd>标签 arclist / list 内调用 <font color="red">(更多简洁调法请参考标签 arclist /list)</font>：</dd>';
                        contentdiv += '<dd><textarea rows="4" cols="60" style="width:470px;height:120px;">' + liststr + '}</textarea></dd>';
                    }
                    break;

                case 'checkbox':
                    {
                        height = '430px';
                        var viewstr = "{eyou:diyfield type='"+dtype+"' id='"+fieldname+"' name='$"+"eyou.field."+fieldname+"'}\r\n    {$"+fieldname+".value}\r\n{/eyou:diyfield";
                        contentdiv += '<dd>内容页：</dd>';
                        contentdiv += '<dd><textarea rows="4" cols="60" style="width:470px;height:60px;">' + viewstr + '}</textarea></dd>';
                        var liststr = "{eyou:arcview aid='$"+"field.aid' id='eyoav' addfields='"+fieldname+"'}\r\n    {eyou:diyfield type='"+dtype+"' id='"+fieldname+"' name='$"+"eyoav."+fieldname+"'}\r\n        {$"+fieldname+".value}\r\n    {/eyou:diyfield"+"}\r\n{/eyou:arcview";
                        contentdiv += '<dd>标签 arclist / list 内调用 <font color="red">(更多简洁调法请参考标签 arclist /list)</font>：</dd>';
                        contentdiv += '<dd><textarea rows="4" cols="60" style="width:470px;height:120px;">' + liststr + '}</textarea></dd>';
                        if (1 == is_screening) {
                            contentdiv += '<dd style="color: red;">自定义字段应用于条件筛选，请参考标签用法：<a id="call_tags_help" href="JavaScript:void(0);" onclick="click_to_eyou_1575506523('+"'https://www.eyoucms.com/plus/view.php?aid=7881&origin_eycms=1','筛选标签调用'"+')">查看标签调用</a></dd>';
                        }
                    }
                    break;

                case 'datetime':
                    {
                        contentdiv += '<dd>内容页：</dd>';
                        contentdiv += '<dd><input type="text" style=" width:470px;margin-bottom:10px;" value="{$'+'eyou.field.'+fieldname+'|MyDate=\'Y-m-d H:i:s\',###}"></dd>';
                        var liststr = "{eyou:arcview aid='$"+"field.aid' id='eyoav' addfields='"+fieldname+"'}\r\n    {$"+"eyoav."+fieldname+"|MyDate=\'Y-m-d H:i:s\',###}\r\n{/eyou:arcview";
                        contentdiv += '<dd>标签 arclist / list 内调用 <font color="red">(更多简洁调法请参考标签 arclist /list)</font>：</dd>';
                        contentdiv += '<dd><textarea rows="4" cols="60" style="width:470px;height:60px;">' + liststr + '}</textarea></dd>';
                    }
                    break;

                case 'htmltext':
                    {
                        contentdiv += '<dd>内容页：</dd>';
                        contentdiv += '<dd><input type="text" style=" width:470px;margin-bottom:10px;" value="{$'+'eyou.field.'+fieldname+'}"></dd>';
                        if (1 == ifmain) {
                            contentdiv += '<dd>标签 arclist / list 内调用：</dd>';
                            contentdiv += '<dd><input type="text" style=" width:470px;margin-bottom:10px;" value="{$'+'field.'+fieldname+'}"></dd>';
                        } else {
                            var liststr = "eyou:field aid='$"+"field.aid' addfields='"+fieldname+"|html_msubstr=###,0,150,true' /";
                            contentdiv += '<dd>标签 arclist / list 内调用：</dd>';
                            contentdiv += '<dd><input type="text" style=" width:470px;margin-bottom:10px;" value="{'+liststr+'}"></dd>';
                        }
                        if (1 == is_screening) {
                            contentdiv += '<dd style="color: red;">自定义字段应用于条件筛选，请参考标签用法：<a id="call_tags_help" href="https://www.eyoucms.com/plus/view.php?aid=7881&origin_eycms=1" target="_blank">查看标签调用</a></dd>';
                        }
                    }
                    break;

                default:
                    {
                        contentdiv += '<dd>内容页：</dd>';
                        contentdiv += '<dd><input type="text" style=" width:470px;margin-bottom:10px;" value="{$'+'eyou.field.'+fieldname+'}"></dd>';
                        if (1 == ifmain) {
                            contentdiv += '<dd>标签 arclist / list 内调用：</dd>';
                            contentdiv += '<dd><input type="text" style=" width:470px;margin-bottom:10px;" value="{$'+'field.'+fieldname+'}"></dd>';
                        } else {
                            var liststr = "eyou:field aid='$"+"field.aid' addfields='"+fieldname+"' /";
                            contentdiv += '<dd>标签 arclist / list 内调用：</dd>';
                            contentdiv += '<dd><input type="text" style=" width:470px;margin-bottom:10px;" value="{'+liststr+'}"></dd>';
                        }
                        if (1 == is_screening) {
                            contentdiv += '<dd style="color: red;">自定义字段应用于条件筛选，请参考标签用法：<a id="call_tags_help" href="https://www.eyoucms.com/plus/view.php?aid=7881&origin_eycms=1" target="_blank">查看标签调用</a></dd>';
                        }
                    }
                    break;
            }
        }
        contentdiv += '<dd style="border-top: dotted 1px #E7E7E7; color: #F60;">请将相应标签复制并粘贴到对应模板文件中！</dd></dl></div>'
        layer.open({
            shade: layer_shade,
            title: '标签调用',
            type: 1,
            skin: 'layui-layer-demo',
            area: ['550px', height], //宽高
            content: contentdiv
        });
    }

    function handleShow(obj){
        $.ajax({
            type : 'post',
            url : $(obj).attr('data-url'),
            data : {id:$(obj).attr('data-id'),ifeditable:$(obj).attr('data-ifeditable'), _ajax:1},
            dataType : 'json',
            success : function(data){
                layer.closeAll();
                if(data.code == 1){
                    layer.msg(data.msg, {icon: 1});
                    window.location.reload();
                }else{
                    layer.alert(data.msg, {icon: 2, title:false});
                }
            }
        });
    }
</script>

{include file="public/footer" /}