{include file="public/layout" /}

<body class="bodystyle" id="bodystyle"  style="cursor: default; -moz-user-select: inherit;">
<div id="append_parent"></div>
<div id="ajaxwaitid"></div>
<div class="page min-hg-c-10">
    <div class="flexigrid" style="margin-top: 0px;min-height: 600px;">
		<div class="fixed-bar">
		    <div class="item-title">
		        <a class="back_xin" href="{notempty name='$menu'}{:url('Index/switch_map')}{else /}{:url('Archives/index')}{/notempty}" title="返回"><i class="iconfont e-fanhui"></i></a>
		        <div class="subject">
		            <h3>待审核文档</h3>
		            <h5></h5>
		        </div>
		    </div>
		</div>
        <div class="mDiv pt0">
            <div class="ftitle_nav">
                <div class="fbutton">
                    <a href="{:url('Archives/index_draft')}">
                        <div class="{empty name='$draft_type'}cur{/empty}" title="全部">
                            全部<span>{$allCount|default=0}</span>
                        </div>
                    </a>
                </div>
                <div class="fbutton">
                    <a  href="{:url('Archives/index_draft', ['draft_type'=>'user'])}">
                        <div class="{eq name='$draft_type' value='user'}cur{/eq}" title="会员投稿">
                            前台投稿<span>{$userCount|default=0}</span>
                        </div>
                    </a>
                </div>
                <div class="fbutton">
                    <a href="{:url('Archives/index_draft', ['draft_type'=>'admin'])}">
                        <div class="{eq name='$draft_type' value='admin'}cur{/eq}" title="后台发布">
                            后台发布<span>{$adminCount|default=0}</span>
                        </div>
                    </a>
                </div>
            </div>
            <form id="searchForm" class="navbar-form form-inline" action="{:url('Archives/index_draft')}" method="get" onSubmit="layer_loading('正在处理');">
                {$searchform.hidden|default=''}
                <div class="sDiv">
                    <div class="sDiv2">
                        <input type="text" size="30" name="keywords" value="{$Request.param.keywords}" class="qsbox" placeholder="标题搜索...">
                        <input type="hidden" name="menu" value="{$menu|default=0}">
                        <input type="submit" class="btn" value="搜索">
						<i class="iconfont e-sousuo"></i>
                    </div>
                </div>
            </form>
        </div>
        <div class="hDiv">
            <div class="hDivBox">
                <table cellspacing="0" cellpadding="0" style="width: 100%">
                    <thead>
                    <tr>
                        <th class="sign w40" axis="col0">
                            <div class="tc"><input type="checkbox" class="checkAll" autocomplete="off"></div>
                        </th>
                        <th abbr="article_title" axis="col3" class="w70">
                            <div class="tc">ID</div>
                        </th>
                        <th abbr="article_title" axis="col3" class="">
                            <div style="text-align: left;" class="text-l10">标题</div>
                        </th>
                        <th abbr="article_time" axis="col6" class="w110">
                            <div class="tl text-l10">发布人</div>
                        </th>
                        <th abbr="article_time" axis="col6" class="w120">
                            <div class="tc">栏目</div>
                        </th>
                        <th abbr="article_time" axis="col6" class="w60">
                            <div class="tc sort_style"><a href="{:getArchivesSortUrl('arcrank')}">审核&nbsp;<i {eq name='$Request.param.orderby' value='arcrank'}{eq name='$Request.param.orderway' value='asc'}class="asc"{else /}class="desc"{/eq}{/eq}></i></a></div>
                        </th>
                        <th abbr="article_time" axis="col6" class="w60">
                            <div class="tc">点击</div>
                        </th>
                        {if !empty($arctype_info) && $arctype_info['current_channel'] == 4}
                        <th abbr="download_time" axis="col6" class="w60">
                            <div class="tc">下载</div>
                        </th>
                        {/if}
                        <th abbr="article_time" axis="col6" class="w100">
                            <div class="tc">发布时间</div>
                        </th>
                        <th axis="col1" class="w160">
                            <div class="tc">操作</div>
                        </th>
                        <th abbr="article_time" axis="col6" class="w60">
                            <div class="tc sort_style"><a href="{:getArchivesSortUrl('sort_order')}">排序&nbsp;<i {eq name='$Request.param.orderby' value='sort_order'}{eq name='$Request.param.orderway' value='asc'}class="asc"{else /}class="desc"{/eq}{/eq}></i></a></div>
                        </th>
                    </tr>
                    </thead>
                </table>
            </div>
        </div>
        <div class="bDiv" style="height: auto;">
            <div id="flexigrid" cellpadding="0" cellspacing="0" border="0">
                <table style="width: 100%;">
                    <tbody>
                    {empty name="list"}
                        <tr>
                            <td class="no-data" align="center" axis="col0" colspan="50">
                                <div class="no_row">
                                    <div class="no_pic"><img src="__SKIN__/images/null-data.png"></div>
                                </div>
                            </td>
                        </tr>
                    {else/}
                        {foreach name="list" item="vo" key="k" }
                        <tr>
                            <td class="sign">
                                <div id="aid_{$vo.aid}" data-typeid="{$vo.typeid}" class="tc w40"><input type="checkbox" name="ids[]" value="{$vo.aid}" autocomplete="off"></div>
                            </td>
                           
                            <td class="sort">
                                <div class="tc w70">
                                    {$vo.aid}
                                </div>
                            </td>
                            <td class="" style="width: 100%;">
                                <div class="tl pdl10" >
                                    {notempty name="$vo.is_litpic"}
                                        <i class="fa fa-picture-o color_z" onmouseover="layer_tips=layer.tips('<img src={$vo.litpic} class=\'layer_tips_img\'>',this,{tips: [3, '#fff'],skin:'layer-yourskin-mt0'});" onmouseout="layer.close(layer_tips);"></i>
                                    {/notempty}
                                    {eq name="'Archives@edit'|is_check_access" value="1"}
                                        {if condition="empty($channelRow[$vo['channel']]['ifsystem'])"}
                                            <a href="{:url('Custom/edit',array('id'=>$vo['aid'],'typeid'=>$Request.param.typeid, 'channel'=>$vo.channel, 'referurl'=>$pageurl))}" style="{eq name="vo.is_b" value="1"} font-weight: bold;{/eq}">{$vo.title}</a>
                                        {else /}
                                            <a href="{:url($channelRow[$vo['channel']]['ctl_name'].'/edit',array('id'=>$vo['aid'],'typeid'=>$Request.param.typeid, 'referurl'=>$pageurl))}" style="{eq name="vo.is_b" value="1"} font-weight: bold;{/eq}">{$vo.title}</a>
                                        {/if}
                                    {else /}
                                    {$vo.title}
                                    {/eq}
                                    {assign name='showArcFlagData' value='$vo|showArchivesFlagStr'}
                                    {volist name='$showArcFlagData' id="vo1"}
                                        {eq name='$i' value='1'}<span style="color: red;">[{/eq}
                                        <i style="font-size: 12px;">{$vo1['small_name']}</i>
                                        {eq name='$i' value='$showArcFlagData|count'}]</span>{/eq}
                                    {/volist}
                                </div>
                            </td>
                            <td>
                                <div class="tl text-l10 w110 ellipsis">
                                    {$vo.username|default='匿名'}
                                </div>
                            </td>
                            <td class="">
                                <div class="w120 tc ellipsis"><a href="{:url('Archives/index_draft', array('typeid'=>$vo['typeid']))}" title="{$vo.typename}">{$vo.typename|default='<i class="red">数据出错！</i>'}</a></div>
                            </td>
                            <td>
                                <div class="tc w60">
                                    {if condition="$vo['arcrank'] <= -1"}
                                        <span class="no" {eq name="$Think.const.CONTROLLER_NAME.'@edit'|is_check_access" value="1"} data-typeid="{$vo.typeid}" data-seo_pseudo="{$seo_pseudo|default='1'}" onClick="changeTableVal('archives','aid','{$vo.aid}','arcrank',this);" {/eq} ><i class="fa fa-ban"></i>否</span>
                                    {else /}
                                        <span class="yes" {eq name="$Think.const.CONTROLLER_NAME.'@edit'|is_check_access" value="1"} data-typeid="{$vo.typeid}" data-seo_pseudo="{$seo_pseudo|default='1'}" onClick="changeTableVal('archives','aid','{$vo.aid}','arcrank',this);" {/eq} ><i class="fa fa-check-circle"></i>是</span>
                                    {/if}
                                </div>
                            </td>
                            <td>
                                <div class="tc w60">
                                    {$vo.click}
                                </div>
                            </td>
                            {if !empty($arctype_info) && $arctype_info['current_channel'] == 4}
                            <td>
                                <div class="tc w60">
                                    {$vo.downcount}
                                </div>
                            </td>
                            {/if}
                            <td>
                                <div class="w100 tc">
                                    {$vo.add_time|date='Y-m-d',###}
                                </div>
                            </td>
                            <td class="operation">
                                <div class="w160 tc">
                                    {eq name="'Archives@edit'|is_check_access" value="1"}
                                        {if condition="empty($channelRow[$vo['channel']]['ifsystem'])"}
                                            <a href="{:url('Custom/edit',array('id'=>$vo['aid'],'typeid'=>$Request.param.typeid, 'channel'=>$vo.channel, 'referurl'=>$pageurl))}" class="btn blue">编辑</a>
                                        {else /}
                                            <a href="{:url($channelRow[$vo['channel']]['ctl_name'].'/edit',array('id'=>$vo['aid'],'typeid'=>$Request.param.typeid, 'referurl'=>$pageurl))}" class="btn blue">编辑</a>
                                        {/if}
                                        <i></i>
                                    {/eq}
                                    {eq name="'Archives@del'|is_check_access" value="1"}
                                        <a class="btn red"  href="javascript:void(0);" data-url="{:url('Archives/del')}" data-id="{$vo.aid}" {eq name="$global['web_recycle_switch']" value='1'} data-deltype="del" {else /} data-deltype="pseudo" {/eq} onClick="delfun(this);">删除</a>
                                        <i></i>
                                    {/eq}
                                    <a href="{$vo.arcurl}" target="_blank" class="btn blue">浏览</a>
                                </div>
                            </td>
                             <td class="sort">
                                <div class="w60 tc">
                                    {eq name="'Archives@edit'|is_check_access" value="1"}
                                    <input type="text" onChange="changeTableVal('archives','aid','{$vo.aid}','sort_order',this);"  size="4"  value="{$vo.sort_order}" />
                                    {else /}
                                    {$vo.sort_order}
                                    {/eq}
                                </div>
                            </td>
                            
                        </tr>
                        {/foreach}
                    {/empty}
                    </tbody>
                </table>
            </div>
            <div class="iDiv" style="display: none;"></div>
        </div>
        {notempty name="list"}
        <div class="footer-oper">
            <span class="ml15">
                <input type="checkbox" class="checkAll" autocomplete="off">
            </span>
            <div class="nav-dropup">
                <button class="layui-btn layui-btn-primary dropdown-bt">批量操作<i class="layui-icon layui-icon-up"></i></button>
                <div class="dropdown-menus" style="display:none; {if condition='0 < $pager->totalRows && ($pager->totalRows < 4 || $pager->listRows < 4)'}top:28px;bottom:unset;border-bottom:1px solid rgba(0,0,0,.15);border-top:none;min-height: 170px;{/if}">
                    {eq name="'Archives@batch_attr'|is_check_access" value="1"}
                    <a href="javascript:void(0);" onclick="batch_attr(this, 'ids', '批量新增属性');" data-url="{:url('Archives/batch_attr', ['opt'=>'add'])}">新增属性</a>
                    <a href="javascript:void(0);" onclick="batch_attr(this, 'ids', '批量删除属性');" data-url="{:url('Archives/batch_attr', ['opt'=>'del'])}">删除属性</a>
                    <hr class="layui-bg-gray">
                    {/eq}
                    {eq name="'Archives@batch_copy'|is_check_access" value="1"}
                    <a href="javascript:void(0);" onclick="func_batch_copy(this, 'ids');" data-url="{:url('Archives/batch_copy', array('typeid'=>$Request.param.typeid))}">复制文档</a>
                    {/eq}
                    {eq name="'Archives@move'|is_check_access" value="1"}
                    <a href="javascript:void(0);" onclick="func_move(this, 'ids');" data-url="{:url('Archives/move', array('typeid'=>$Request.param.typeid))}">移动文档</a>
                    {/eq}
                    {eq name="'Archives@del'|is_check_access" value="1"}
                    <a href="javascript:void(0);" onclick="batch_del(this, 'ids');" data-url="{:url('Archives/del')}" {eq name="$global['web_recycle_switch']" value='1'} data-deltype="del" {else /} data-deltype="pseudo" {/eq}>删除文档</a>
                    {/eq}
                </div>
            </div>
            {eq name="'Archives@check'|is_check_access" value="1"}
            <a href="javascript:void(0);" onclick="batch_check(this, 'ids');" data-type="check"  data-url="{:url('Archives/check')}" class="layui-btn layui-btn-primary">审核文档</a>
            {/eq}
            {eq name="'RecycleBin@archives_index'|is_check_access" value="1"}
            {neq name="$global['web_recycle_switch']" value='1'} <a href="{:url('RecycleBin/archives_index')}" class="layui-btn layui-btn-primary" title="回收站">回收站</a> {/neq}
            {/eq}
            {include file="public/page" /}
        </div>
        {/notempty}
    </div>
</div>
<script type="text/javascript">
    $(function(){
        $('input[name*=ids]').click(function(){
            if ($('input[name*=ids]').length == $('input[name*=ids]:checked').length) {
                $('.checkAll').prop('checked','checked');
            } else {
                $('.checkAll').prop('checked', false);
            }
        });
        $('input[type=checkbox].checkAll').click(function(){
            $('input[type=checkbox]').prop('checked',this.checked);
        });
    });
    
    $(document).ready(function(){

        // 表格行点击选中切换
        $('#flexigrid > table>tbody >tr').click(function(){
            $(this).toggleClass('trSelected');
        });

        // 点击刷新数据
        $('.fa-refresh').click(function(){
            location.href = location.href;
        });

        // 选择栏目检索出相应文档列表
        $('#typeid').change(function(){
          $('#searchForm').submit();
        });

        // 批量操作
        $(".dropdown-bt").click(function(){
            $(".dropdown-menus").slideToggle(200);
            event.stopPropagation();
        })
        $(document).click(function(){
            $(".dropdown-menus").slideUp(200);
            event.stopPropagation();
        })
    });

    var aids = '';
    function func_move(obj, name)
    {
        var a = [];
        var k = 0;
        aids = '';
        $('input[name^='+name+']').each(function(i,o){
            if($(o).is(':checked')){
                a.push($(o).val());
                if (k > 0) {
                    aids += ',';
                }
                aids += $(o).val();
                k++;
            }
        })
        if(a.length == 0){
            layer.alert('请至少选择一项', {
                shade: layer_shade,
                area: ['480px', '190px'],
                move: false,
                title: '提示',
                btnAlign:'r',
                closeBtn: 3,
                success: function () {
                      $(".layui-layer-content").css('text-align', 'left');
                  }
            });
            return;
        }

        var url = $(obj).attr('data-url');
        //iframe窗
        layer.open({
            type: 2,
            title: '移动文档',
            fixed: true, //不固定
            shadeClose: false,
            shade: layer_shade,
            maxmin: false, //开启最大化最小化按钮
            area: ['480px', '360px'],
            content: url
        });
    }

    /**
     * 获取修改之前的内容
     */
    function get_aids()
    {
        return aids;
    }
    var typeid_arr = [];   //已经生成过静态的typeid
    var build_finish = false;  //是否已经全部生成静态
    //检查是否已经生成静态，刷新页面
    function build_finish_polling(){
        if(build_finish === true){
            layer.closeAll();
            layer.msg('操作成功', {icon: 1});
            window.location.reload();
        }
    }
    /**
     * 批量审核
     */
    function batch_check(obj, name) {

        var url = $(obj).attr('data-url');
        var data_type = $(obj).attr('data-type');//uncheck

        var a = [];
        $('input[name^='+name+']').each(function(i,o){
            if($(o).is(':checked')){
                a.push($(o).val());
            }
        })
        if(a.length == 0){
            layer.alert('请至少选择一项', {
                shade: layer_shade,
                area: ['480px', '190px'],
                move: false,
                title: '提示',
                btnAlign:'r',
                closeBtn: 3,
                success: function () {
                      $(".layui-layer-content").css('text-align', 'left');
                  }
            });
            return;
        }

        layer_loading('正在处理');
        var seo_pseudo = {$seo_pseudo|default=1};
        $.ajax({
            type: "POST",
            url: url,
            data: {ids:a, _ajax:1},
            dataType: 'json',
            success: function (data) {
                if(data.code == 1){
                    var last = false;
                    if (data_type === 'check') {
                        if (2 == seo_pseudo) {
                            for(var i=0;i<a.length;i++){
                                var id_value = a[i];
                                var typeid =$("#aid_"+id_value).data('typeid');
                                if ((i+1) == a.length){
                                    last = true;
                                }
                                up_html(id_value,typeid,last);
                            }
                            window.setTimeout(build_finish_polling, 3000);
                        } else {
                            layer.closeAll();
                            layer.msg('操作成功', {icon: 1});
                            window.location.reload();
                        }
                    }else{
                        layer.closeAll();
                        layer.msg(data.msg, {icon: 1});
                        window.location.reload();
                    }
                }else{
                    layer.closeAll();
                    layer.alert(data.msg, {icon: 2, title:false});
                }
            },
            error:function(e){
                layer.closeAll();
                layer.alert(e.responseText, {icon: 2, title:false});
            }
        });
    }

    //生成静态页面
    function up_html(id_value,typeid,last){
        $.ajax({
            url:__root_dir__+"/index.php?m=home&c=Buildhtml&a=upHtml&lang="+__lang__+"&id="+id_value+"&t_id="+typeid+"&type=view&ctl_name=Archives&_ajax=1",
            type:'GET',
            dataType:'json',
            data:{},
            success:function(res1){
//                layer.closeAll();
//                layer.msg('生成完成', {icon: 1, time: 1500});
                if (typeid_arr.indexOf(typeid) === -1){
                    typeid_arr.push(typeid);
                    $.ajax({
                        url:__root_dir__+"/index.php?m=home&c=Buildhtml&a=upHtml&lang="+__lang__+"&id="+id_value+"&t_id="+typeid+"&type=lists&ctl_name=Archives&_ajax=1",
                        type:'GET',
                        dataType:'json',
                        data:{},
                        success:function(res2){
                            if (last === true){
                                build_finish = true;
                            }
//                        layer.closeAll();
//                        layer.msg('生成完成', {icon: 1, time: 1500});
                        },
                        error: function(e){
                            layer.closeAll();
                            layer.alert('生成当前栏目HTML失败，请手工生成栏目静态！', {icon: 5, title: false});
                        }
                    });
                }else{
                    if (last === true){
                        build_finish = true;
                    }
                }
            },
            error: function(e){
                layer.closeAll();
                layer.alert('生成HTML失败，请手工生成静态HTML！', {icon: 5, title: false});
            }
        });
    }

</script>

{include file="public/footer" /}