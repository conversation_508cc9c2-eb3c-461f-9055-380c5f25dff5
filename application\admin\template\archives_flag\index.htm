{include file="public/layout" /}
<body class="bodystyle" style="cursor: default; -moz-user-select: inherit; min-width:400px;">
<div id="append_parent"></div>
<div id="ajaxwaitid"></div>
<div class="page min-hg-c-10">
    <div class="fixed-bar">
        <div class="item-title">
            {eq name='$Request.param.source' value='archives'}
            <a class="back_xin" href="javascript:history.back();"  title="返回"><i class="iconfont e-fanhui"></i></a>
            {else /}
            <a class="back_xin" href="{:url('Index/switch_map')}" title="返回"><i class="iconfont e-fanhui"></i></a>
            {/eq}
            <div class="subject">
                <h3>文档属性列表</h3>
                <h5></h5>
            </div>
        </div>
    </div>
    <div class="flexigrid">
        <div class="hDiv">
            <div class="hDivBox">
                <table cellspacing="0" cellpadding="0" style="width: 100%">
                    <thead>
                    <tr>
                        
                        <th abbr="article_title" axis="col3" class="">
                            <div class="text-l10">属性名称</div>
                        </th>
						<th abbr="article_title" axis="col3" class="w80">
                            <div class="tc">属性值</div>
                        </th>
                        <th abbr="article_time" axis="col6" class="w80">
                            <div class="tc">显示</div>
                        </th>
                        <th abbr="article_time" axis="col6" class="w80">
                            <div class="tc">排序</div>
                        </th>
                    </tr>
                    </thead>
                </table>
            </div>
        </div>
        <div class="bDiv" style="height: auto;">
            <div id="flexigrid">
                <table cellspacing="0" cellpadding="0" style="width: 100%">
                    <tbody>
                        {empty name="list"}
                            <tr>
                                <td class="no-data" align="center" axis="col0" colspan="50">
                                    <div class="no_row">
                                        <div class="no_pic"><img src="__SKIN__/images/null-data.png"></div>
                                    </div>
                                </td>
                            </tr>
                        {else/}
                            {foreach name="list" item="vo" key="k" }
                            <tr>
                                
                                <td class="" style="width: 100%;">
                                    <div class="tl text-l10" >
                                        {eq name="'Archives@add'|is_check_access" value="1"}
                                            <input type="text" name="flag_name" value="{$vo.flag_name}" onchange="changeTableVal('archives_flag', 'id', '{$vo.id}', 'flag_name', this);" style="width: 200px;" title="自定义属性名称" placeholder="自定义属性名称">
                                        {else /}
                                            {$vo.flag_name}
                                        {/eq}
                                    </div>
                                </td>
								<td class="">
                                    <div class="w80 tc">
                                        {$vo.flag_attr}
                                    </div>
                                </td>
                                <td class="">
                                    <div class="w80 tc">
                                        {eq name="$vo.status" value='1'}
                                            <span class="yes" style="cursor: pointer;" {eq name="'Archives@add'|is_check_access" value="1"} data-id="{$vo.id}" onclick="changeTableVal('archives_flag','id','{$vo.id}','status',this);" {/eq} ><i class="fa fa-check-circle"></i>是</span>
                                        {else /}
                                            <span class="no" style="cursor: pointer;" {eq name="'Archives@add'|is_check_access" value="1"} data-id="{$vo.id}" onclick="changeTableVal('archives_flag','id','{$vo.id}','status',this);" {/eq} ><i class="fa fa-ban"></i>否</span>
                                        {/eq}
                                    </div>
                                </td>
                                <td class="sort">
                                    <div class="w80 tc">
                                        <input type="text" onkeyup="this.value=this.value.replace(/[^\d]/g,'');" onpaste="this.value=this.value.replace(/[^\d]/g,'')" onchange="changeTableVal('archives_flag','id','{$vo.id}','sort_order',this);"  size="4"  value="{$vo.sort_order}" />
                                    </div>
                                </td>
                            </tr>
                            {/foreach}
                        {/empty}
                        </tbody>
                    </table>
                </div>
            </form>
        </div>
    </div>
</div>
<script>
    $(document).ready(function(){
        // 表格行点击选中切换
        $('#flexigrid > table>tbody >tr').click(function(){
            $(this).toggleClass('trSelected');
        });
    });

</script>

{include file="public/footer" /}