{include file="public/layout" /}
<body class="bodystyle" style="overflow-y: scroll;min-width:auto;">
<div id="toolTipLayer" style="position: absolute; z-index: 9999; display: none; visibility: visible; left: 95px; top: 573px;"></div>
<div id="append_parent"></div>
<div id="ajaxwaitid"></div>
<style type="text/css">
    .ercodepic{
        width: 150px;
        height: 150px;
        vertical-align: middle;
        text-align: center;
        display: table-cell;
    }
</style>
<div class="page" style="min-width: auto;box-shadow:none;">
    {include file="canal/bar" /}
    <!-- 操作说明 -->
    <div id="explanation" class="explanation">
        <div id="checkZoom" class="title"><i class="fa fa-lightbulb-o"></i>
            <h4 title="提示相关设置操作时应注意的要点">提示</h4>
            <span title="收起提示" id="explanationZoom" style="display: block;"></span>
        </div>
        <ul>
            <li>登录智能小程序平台 <a href="https://smartprogram.baidu.com" target="_blank">https://smartprogram.baidu.com</a></li>
            <li>在“管理->基础设置->开发设置”里获取AppID、AppKey、AppSecret的值</li>
            <li>以下配置可通用于开源百度小程序API、或云插件库里的百度小程序插件。</li>
        </ul>
    </div>
    <form class="form-horizontal" id="post_form" action="{:url('Canal/conf_baidu')}" method="post">
        <div class="ncap-form-default">
            <dl class="row"><dt class="tit"><label><b>百度登录</b></label></dt></dl>
            <dl class="row">
                <dt class="tit">
                    <label for="appid"><em id="em_appid" {empty name='$data.appid'}style="display: none;"{/empty}>*</em>App ID</label>
                </dt>
                <dd class="opt">
                    <input type="text" name="appid" id="appid" value="{$data.appid|default=''}" class="input-txt" autocomplete="off">
                    <p class="notic">智能小程序ID</p>
                </dd>
            </dl>
            <dl class="row">
                <dt class="tit">
                    <label for="appkey"><em id="em_appkey" {empty name='$data.appkey'}style="display: none;"{/empty}>*</em>App Key</label>
                </dt>
                <dd class="opt">
                    <input type="text" name="appkey" id="appkey" value="{$data.appkey|default=''}" class="input-txt" autocomplete="off">
                    <p class="notic"></p>
                </dd>
            </dl>
            <dl class="row">
                <dt class="tit">
                    <label for="appsecret"><em id="em_appsecret" {empty name='$data.appsecret'}style="display: none;"{/empty}>*</em>App Secret</label>
                </dt>
                <dd class="opt">
                    <input type="text" name="appsecret" id="appsecret" value="{$data.appsecret|default=''}" class="input-txt" autocomplete="off">
                    <p class="notic">智能小程序密匙</p>
                </dd>
            </dl>
            <dl class="row {empty name='$qrcodeurl'}none{/empty}" id="dl_qrcode">
                <dt class="tit">
                    <label for="qrcode">小程序码</label>
                </dt>
                <dd class="opt">
                    <span id="span_qrcode"></span>
                    <div class="ercodepic">
                        <img id="img_qrcode" src="__STATIC__/common/images/loading.gif" width="23" height="23" />
                    </div>
                    <p class="notic"></p>
                </dd>
            </dl>
            <div class="bot">
                <a href="JavaScript:void(0);" onclick="checkForm();" class="ncap-btn-big ncap-btn-green" id="submitBtn">确认提交</a>
            </div>
        </div>
    </form>
</div>
<script type="text/javascript">

    $(function(){
        $('#appid, #appkey, #appsecret').keyup(function(){
            var appid = $.trim($('#appid').val());
            var appkey = $.trim($('#appkey').val());
            var appsecret = $.trim($('#appsecret').val());
            if (appid != '' || appkey != '' || appsecret != '') {
                $('#em_appid').show();
                $('#em_appkey').show();
                $('#em_appsecret').show();
            } else {
                $('#em_appid').hide();
                $('#em_appkey').hide();
                $('#em_appsecret').hide();
            }
        });

        function ajax_get_baidu_qrcode()
        {
            var appkey = $.trim($('#appkey').val());
            var appsecret = $.trim($('#appsecret').val());
            if (appkey != '' && appsecret != '') {
                $('#dl_qrcode').show();

                var qrcodeurl = "{$qrcodeurl}";
                if (qrcodeurl != '') {
                    $('#img_qrcode').attr('src', qrcodeurl);
                    setTimeout(function(){
                        $('#img_qrcode').attr('width', 150).attr('height', 150);
                    },100);
                }
            }

            $.ajax({
                url: "{:url('Canal/ajax_get_baidu_qrcode', ['_ajax'=>1])}",
                type: 'GET',
                dataType: 'JSON',
                data: {},
                success: function (res) {
                    if (1 == res.code) {
                        $('#span_qrcode').hide();
                        $('#img_qrcode').attr('src', '');
                        $('#img_qrcode').attr('src', res.data.qrcodeurl).attr('width', 150).attr('height', 150).show();
                    } else {
                        $('#span_qrcode').html(res.msg);
                        $('#img_qrcode').hide();
                        return false;
                    }
                },
                error: function (e) {
                    $('#span_qrcode').html(e.responseText);
                    $('#img_qrcode').hide();
                    return false;
                }
            });
        }
        ajax_get_baidu_qrcode();
    });

    // 判断输入框是否为空
    function checkForm(){
        var appid = $.trim($('#appid').val());
        var appkey = $.trim($('#appkey').val());
        var appsecret = $.trim($('#appsecret').val());
        if (appid != '' || appkey != '' || appsecret != '') {
            if(appid == ''){
                showErrorMsg('App ID不能为空！');
                $('input[name=appid]').focus();
                return false;
            }
            if(appkey == ''){
                showErrorMsg('App Key不能为空！');
                $('input[name=appkey]').focus();
                return false;
            }
            if(appsecret == ''){
                showErrorMsg('App Secret不能为空！');
                $('input[name=appsecret]').focus();
                return false;
            }
        }
        layer_loading('正在处理');
        $.ajax({
            url: "{:url('Canal/conf_baidu', ['_ajax'=>1])}",
            type: 'POST',
            dataType: 'JSON',
            data: $('#post_form').serialize(),
            success: function (res) {
                layer.closeAll();
                if (1 == res.code) {
                    layer.msg(res.msg, {shade: layer_shade, time: 1000}, function(){
                        window.location.reload();
                    });
                } else {
                    showErrorAlert(res.msg);
                    return false;
                }
            },
            error: function (e) {
                layer.closeAll();
                showErrorAlert(e.responseText);
                return false;
            }
        });
    }

</script>
{include file="public/footer" /}