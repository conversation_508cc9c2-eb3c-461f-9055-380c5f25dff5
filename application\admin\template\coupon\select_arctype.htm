{include file="public/layout" /}
<body class="bodystyle"
    style="background-color: rgb(255, 255, 255); overflow: auto; cursor: default; -moz-user-select: inherit;min-width: auto;padding: 5px;">
    <div class="page arctype" onselectstart="return false;">
        <div class="flexigrid">
            <div class="hDiv">
                <div class="hDivBox">
                    <table cellpadding="0" cellspacing="0" style="width: 100%">
                        <thead>
                            <tr>
                                <th axis="col3" class="">
                                    <div class="sundefined text-l10">
                                        {if condition="!empty($tree['cookied_treeclicked'])"}
                                        <img src="__SKIN__/images/tv-collapsable-last.gif" id="all_treeclicked" title="关闭所有子栏目" style="float: none;" data-status="open" onClick="treeClicked(this,'all',0);">
                                        {else /}
                                        <img src="__SKIN__/images/tv-expandable.gif" id="all_treeclicked" title="展开所有子栏目" style="float: none;" data-status="close" onClick="treeClicked(this,'all',0);">
                                        {/if}
                                        分类名称
                                    </div>
                                </th>
                            </tr>
                        </thead>
                    </table>
                </div>
            </div>

            <div id="flexigrid" class="bDiv" style="height: auto;">
            {empty name="arctype_list"}
                <div id="" cellpadding="0" cellspacing="0" border="0">
                    <table>
                        <tbody>
                            <tr>
                                <td class="no-data" align="center" axis="col0" colspan="50">
                                    <div class="no_row">
                                        <div class="no_pic"><img src="__SKIN__/images/null-data.png"></div>
                                    </div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                <div class="iDiv" style="display: none;"></div>
            {else /}
                <table class="flex-table autoht" cellpadding="0" cellspacing="0" border="0" id="arctype_table" style="width: 100%">
                    <tbody id="treet1">
                        {foreach name="arctype_list" item="vo" key="k" }
                        <tr nctype="0" {if condition="!in_array($vo.parent_id,$tree.cookied_treeclicked) && 0 < $vo.parent_id "} style="display:none;"{/if} class="parent_id_{$vo['parent_id']}" data-level="{$vo['level']}" data-id="{$vo['id']}">
                            <td class="typename" style="width: 100%">
                                <div>
                                    {gt name='vo.level' value='0'}
                                    {php}
                                    if (1 == $vo['level']) {
                                    echo '<span class="w40x"></span>';
                                    } else if ($vo['level'] >= 2) {
                                    echo '<span class="w40x w40xc'.$vo['level'].'" style="margin-right:'.(($vo['level'] - 1) * 25).'px;"></span>';
                                    }
                                    {/php}
                                    {/gt}
                                    {gt name="vo.has_children" value="0"}
                                    <img src="{in name='$vo.id' value='$tree.cookied_treeclicked'}__SKIN__/images/tv-collapsable-last.gif{else /}__SKIN__/images/tv-expandable.gif{/in}" style="float: none;" fieldid="2" status="open" nc_type="flex" onclick="treeClicked(this, {$vo['id']}, 0);" class="has_children childrenImg" data-level="{$vo['level']}" data-id="{$vo['id']}">
                                    {else /}
                                    <img src="__SKIN__/images/tv-collapsable-last.gif" style="float: none;" fieldid="2" status="open" nc_type="flex" class="childrenImg">
                                    {/gt}
                                    <label><input type="checkbox" value="{$vo.id}" id="{$vo.id}" data-level="{$vo.level}" data-typename="{$vo.typename}" onclick="selectTypeid(this);" class="typeids" {if condition="in_array($vo.id, $typeids_arr)"}checked="checked"{/if}>&nbsp;{$vo.typename}</label>
                                </div>
                            </td>
                        </tr>
                        {/foreach}
                    </tbody>
                </table>
            {/empty}
            </div>
        </div>
        <p style="padding: 10px 0 0 ;">所选栏目不包含子栏目，如需包含子栏目，需要一并勾选</p>
    </div>
    <input type="hidden" id="post_typeids" value="{$typeids}">
    <script type="text/javascript">
        function selectTypeid(obj) {
            var postStypeidArr = [];
            var postStypenameArr = [];
            $('.typeids:not(:disabled)').each(function() {
                var _this = $(this);
                // 选中的分类ID加入数组
                if (true === _this.prop('checked')) {
                    postStypeidArr.push(_this.val());
                    postStypenameArr.push(_this.data('typename'));
                }
            });
            // 选中的分类ID数组转字符串
            var postStypeidStr = postStypeidArr.join(',');
            $('#post_typeids').val(postStypeidStr);
        }
    </script>
    <script type="text/javascript">
        $(function() {
            setCookies('coupontypeids-treeClicked-Arr', JSON.stringify({$tree.cookied_treeclicked_arr}) );
        });

        // 点击展开 收缩节点
        function treeClicked(obj, id, reload) {
            if (id == 'all') {
                if (1 == reload) {
                    var status = getCookie('coupontypeids-treeClicked');
                    if (!status) status = $(obj).attr('data-status');
                } else {
                    var status = $(obj).attr('data-status');
                }
                if (status == 'close') {
                    setCookies('coupontypeids-treeClicked-Arr', JSON.stringify({$tree.parent_ids}));
                    setCookies('coupontypeids-treeClicked_All', 1);//1为全部展开,0-为非全部展开
                    $('tr[class^=parent_id_]').show();
                    $('.childrenImg').attr('src', '__SKIN__/images/tv-collapsable-last.gif');
                    $(obj).attr('data-status', 'open').attr('title','关闭所有子分类').attr('src','__SKIN__/images/tv-collapsable-last.gif');
                } else {
                    setCookies('coupontypeids-treeClicked-Arr', '');
                    setCookies('coupontypeids-treeClicked_All', 0);
                    $('tr[data-level=0]').find('img.has_children').attr('src', '__SKIN__/images/tv-collapsable-last.gif').trigger('click');
                    $('tr[class^=parent_id_]').removeClass('trSelected');
                    $(obj).attr('data-status', 'close').attr('title','展开所有子分类').attr('src','__SKIN__/images/tv-expandable.gif');
                }
                setCookies('coupontypeids-treeClicked', status);
                return false;
            }

            var src = $(obj).attr('src');
            if (src == '__SKIN__/images/tv-expandable.gif') {
                var arr = [];
                var str = getCookie('coupontypeids-treeClicked-Arr');
                if ('' == str || null == str || 'null' == str) {
                    arr.push(id);
                } else {
                    arr = JSON.parse(str);
                    if (!arr.includes(id))arr.push(id);
                }
                arr = JSON.stringify(arr);
                setCookies('coupontypeids-treeClicked-Arr', arr);
                $(".parent_id_"+id).show();
                $(obj).attr('src','__SKIN__/images/tv-collapsable-last.gif');
                var status = 'close';
            } else {
                var key = -1;
                var arr = [];
                var level = '';
                var str = getCookie('coupontypeids-treeClicked-Arr');
                if ('' != str) {
                    arr = JSON.parse(str);
                    key = $.inArray(id,arr);
                    if (-1 < key) arr.splice(key,1);
                    level = $(obj).attr('data-level');
                    $(obj).parent().parent().parent().nextAll().each(function() {
                        if ($(this).attr('data-level') > level) {
                            key = $.inArray(parseInt($(this).attr('data-id')), arr);
                            if (-1 < key) arr.splice(key,1);
                        } else {
                            return false;
                        }
                    });
                    arr = JSON.stringify(arr);
                    setCookies('coupontypeids-treeClicked-Arr', arr);
                    setCookies('coupontypeids-treeClicked_All', 0);
                }

                var status = 'open';
                $(obj).attr('src', '__SKIN__/images/tv-expandable.gif');

                // 如果是点击减号, 遍历循环他下面的所有都关闭
                var fnd = false;
                var cur_tr = obj.parentNode.parentNode.parentNode;
                var tbl = document.getElementById("arctype_table");
                for (var i = 0; i < tbl.rows.length; i++) {
                    var row = tbl.rows[i];
                    if (row == cur_tr) {
                        fnd = true;
                    } else {
                        if (fnd == true) {
                            var cur_level = $(cur_tr).data('level');
                            var level = parseInt($(row).data('level'));
                            if (level > cur_level) {
                                $(row).hide();
                                $(row).find('img.has_children').attr('src','__SKIN__/images/tv-expandable.gif');
                            } else {
                                fnd = false;
                                break;
                            }
                        }
                    }
                }
            }
            setCookies('coupontypeids-treeClicked', status);
        }
    </script>
    {include file="public/footer" /}

