{include file="public/layout" /}
<body style="background-color: rgb(255, 255, 255); overflow: auto; cursor: default; -moz-user-select: inherit;min-width: auto;">
<div id="append_parent"></div>
<div id="ajaxwaitid"></div>
<div class="page" style="min-width: auto;box-shadow:none;">
    <!-- 操作说明 -->
    <div id="explanation" class="explanation">
        <div id="checkZoom" class="title"><i class="fa fa-lightbulb-o"></i>
            <h4 title="提示相关设置操作时应注意的要点">提示</h4>
            <span title="收起提示" id="explanationZoom" style="display: block;"></span>
        </div>
        <ul>
            <li>移动到的目标栏目必须和当前模型类型一致，否则程序会自动忽略不符合的文档。</li>
        </ul>
    </div>
    <form class="form-horizontal" id="post_form" method="POST" action="{$form_action|default=''}" onsubmit="return false;">
        <div class="ncap-form-default">
            <dl class="row">
                <dt class="tit">
                    <label for="typeid"><em>*</em>目标栏目</label>
                </dt>
                <dd class="opt" style="width: auto;">
                    <select id="typeid" name="typeid">
                        {$arctype_html}
                    </select>
                    <input type="hidden" name="aids" id="aids" value="" class="input">
                    <p class="notic"></p>
                    <span class="err"></span>
                </dd>
            </dl>
            <dl class="row">
                <dt class="tit">
                    <label for="all_move">移动全部</label>
                </dt>
                <dd class="opt" style="width: auto;">
                    <label><input type="checkbox" name="all_move" value="1">当前栏目的所有文档移动到目标栏目</label>
                    <p class="notic"></p>
                    <span class="err"></span>
                </dd>
            </dl>
            <div class="bot" style="padding-bottom:0px;">
                <input type="hidden" name="_ajax" value="1">
                <a href="JavaScript:void(0);" onclick="check_submit();" class="ncap-btn-big ncap-btn-green" id="submitBtn">确认提交</a>
            </div>
        </div>
    </form>
</div>
<script type="text/javascript">
    var parentObj = parent.layer.getFrameIndex(window.name); //先得到当前iframe层的索引
    $('#aids').val(parent.get_aids());

    function check_submit()
    {
        layer_loading('正在处理');
        $.ajax({
            url: $('#post_form').attr('action'),
            type: 'POST',
            dataType: 'JSON',
            data: $('#post_form').serialize(),
            success: function(res){
                layer.closeAll();
                if (res.code == 1) {
                    parent.layer.msg(res.msg, {shade: layer_shade, time: 1000}, function(){
                        parent.window.location.reload();
                        parent.layer.close(parentObj);
                    });
                } else {
                    showErrorAlert(res.msg);
                }
            },
            error: function(e){
                layer.closeAll();
                showErrorAlert(e.responseText);
            }
        });
    }
</script>
{include file="public/footer" /}