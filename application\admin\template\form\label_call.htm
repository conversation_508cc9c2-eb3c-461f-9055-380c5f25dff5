{include file="public/layout" /}
<body style="background-color: rgb(255, 255, 255); overflow: auto; cursor: default; -moz-user-select: inherit;min-width: auto;">
    <div id="" class="layui-layer-content" style="height: 207px;min-width: auto;">
        <div class="dialog_content" style="margin: 0px; padding: 0px;">
            <dl style="padding:10px 30px;line-height:30px;">
                <dd>标签 form 调用：</dd>
                <dd><textarea rows="4" cols="60" style="width:700px;height:250px;" class="layui-textarea">{$content}</textarea></dd>
                <dd style="color: #F60;">请将相应标签复制并粘贴到对应模板文件中，样式风格自己美化！</dd>
                <dd style="color: #F60;">以上标签是form表单的字段自动循环显示，更多灵活标签写法，<a href="JavaScript:void(0);" data-href="https://www.eyoucms.com/plus/view.php?aid=29138&origin_eycms=1" onclick="parent_openFullframe(this,'form 表单标签');">点击查看form标签教程</a></dd>
            </dl>
        </div>
    </div>
{include file="public/footer" /}