<?php if (!defined('THINK_PATH')) exit(); /*a:4:{s:59:"./application/admin/template/shop_product\attrlist_edit.htm";i:1680508820;s:74:"C:\phpstudy_pro\WWW\www.b.com\application\admin\template\public\layout.htm";i:1701394260;s:77:"C:\phpstudy_pro\WWW\www.b.com\application\admin\template\public\theme_css.htm";i:1701394260;s:74:"C:\phpstudy_pro\WWW\www.b.com\application\admin\template\public\footer.htm";i:1680508820;}*/ ?>
<!doctype html>
<html>
<head>
<meta http-equiv="content-type" content="text/html; charset=UTF-8">
<meta charset="utf-8">
<meta http-equiv="X-UA-Compatible" content="IE=edge">
<meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
<!-- Apple devices fullscreen -->
<meta name="apple-mobile-web-app-capable" content="yes">
<!-- Apple devices fullscreen -->
<meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">
<link rel="shortcut icon" type="image/x-icon" href="/favicon.ico" media="screen"/>
<link href="/public/plugins/layui/css/layui.css?v=<?php echo $version; ?>" rel="stylesheet" type="text/css">
<link href="/public/static/admin/css/main.css?v=<?php echo $version; ?>" rel="stylesheet" type="text/css">
<link href="/public/static/admin/css/page.css?v=<?php echo $version; ?>" rel="stylesheet" type="text/css">
<link href="/public/static/admin/font/css/font-awesome.min.css?v=<?php echo $version; ?>" rel="stylesheet" />
<link href="/public/static/admin/font/css/iconfont.css?v=<?php echo $version; ?>" rel="stylesheet" />
<!--[if IE 7]>
  <link rel="stylesheet" href="/public/static/admin/font/css/font-awesome-ie7.min.css?v=<?php echo $version; ?>">
<![endif]-->
<script type="text/javascript">
    var eyou_basefile = "<?php echo \think\Request::instance()->baseFile(); ?>";
    var module_name = "<?php echo MODULE_NAME; ?>";
    var GetUploadify_url = "<?php echo url('Uploadimgnew/upload'); ?>";
    // 插件专用旧版上传图片框
    if ('Weapp@execute' == "<?php echo CONTROLLER_NAME; ?>@<?php echo ACTION_NAME; ?>") {
      GetUploadify_url = "<?php echo url('Uploadify/upload'); ?>";
    }
    var __root_dir__ = "";
    var __lang__ = "<?php echo $admin_lang; ?>";
    var __seo_pseudo__ = <?php echo (isset($global['seo_pseudo']) && ($global['seo_pseudo'] !== '')?$global['seo_pseudo']:1); ?>;
    var __web_xss_filter__ = <?php echo (isset($global['web_xss_filter']) && ($global['web_xss_filter'] !== '')?$global['web_xss_filter']:0); ?>;
    var __is_mobile__ = <?php echo (isset($is_mobile) && ($is_mobile !== '')?$is_mobile:0); ?>;
    var __security_ask_open__ = <?php echo (isset($global['security_ask_open']) && ($global['security_ask_open'] !== '')?$global['security_ask_open']:0); ?>;
</script>
<link href="/public/static/admin/js/jquery-ui/jquery-ui.min.css?v=<?php echo $version; ?>" rel="stylesheet" type="text/css"/>
<link href="/public/static/admin/js/perfect-scrollbar.min.css?v=<?php echo $version; ?>" rel="stylesheet" type="text/css"/>
<!-- <link type="text/css" rel="stylesheet" href="/public/plugins/tags_input/css/jquery.tagsinput.css?v=<?php echo $version; ?>"> -->
<style type="text/css">html, body { overflow: visible;}</style>
<link href="/public/static/admin/css/diy_style.css?v=<?php echo $version; ?>" rel="stylesheet" type="text/css" />
<?php if(file_exists(ROOT_PATH.'public/static/admin/css/theme_style.css')): ?>
    <link href="/public/static/admin/css/theme_style.css?v=<?php echo $version; ?>_<?php echo (isset($global['web_theme_style_uptime']) && ($global['web_theme_style_uptime'] !== '')?$global['web_theme_style_uptime']:0); ?>" rel="stylesheet" type="text/css">
<?php elseif(file_exists(APP_PATH.'admin/template/public/theme_css.htm') && function_exists('hex2rgba')): ?>
    <style type="text/css">
    /*官方内置样式表，升级会覆盖变动，请勿修改，否则后果自负*/

    /*左侧收缩图标*/
    #foldSidebar i { font-size: 24px;color:<?php echo $global['web_theme_color']; ?>; }
    /*左侧菜单*/
    .eycms_cont_left{ background:<?php echo $global['web_theme_color']; ?>; }
    .eycms_cont_left dl dd a:hover,.eycms_cont_left dl dd a.on,.eycms_cont_left dl dt.on{ background:<?php echo $global['web_assist_color']; ?>; }
    .eycms_cont_left dl dd a:active{ background:<?php echo $global['web_assist_color']; ?>; }
    .eycms_cont_left dl.jslist dd{ background:<?php echo $global['web_theme_color']; ?>; }
    .eycms_cont_left dl.jslist dd a:hover,.eycms_cont_left dl.jslist dd a.on{ background:<?php echo $global['web_assist_color']; ?>; }
    .eycms_cont_left dl.jslist:hover{ background:<?php echo $global['web_assist_color']; ?>; }
    /*栏目操作*/
    .cate-dropup .cate-dropup-con a:hover{ background-color: <?php echo $global['web_theme_color']; ?>; }
    /*按钮*/
    a.ncap-btn-green { background-color: <?php echo $global['web_theme_color']; ?>; }
    a:hover.ncap-btn-green { background-color: <?php echo $global['web_assist_color']; ?>; }
    .flexigrid .sDiv2 .btn:hover { background-color: <?php echo $global['web_theme_color']; ?>; }
    .flexigrid .mDiv .fbutton div.add{background-color: <?php echo $global['web_theme_color']; ?>; border: none;}
    .flexigrid .mDiv .fbutton div.add:hover{ background-color: <?php echo $global['web_assist_color']; ?>;}
    .flexigrid .mDiv .fbutton div.adds{color:<?php echo $global['web_theme_color']; ?>;border: 1px solid <?php echo $global['web_theme_color']; ?>;}
    .flexigrid .mDiv .fbutton div.adds:hover{ background-color: <?php echo $global['web_theme_color']; ?>;}
    /*选项卡字体*/
    .tab-base a.current,
    .tab-base a:hover.current {color:<?php echo $global['web_theme_color']; ?> !important;}
    .tab-base a.current:after,
    .tab-base a:hover.current:after {background-color: <?php echo $global['web_theme_color']; ?>;}
    .addartbtn input.btn:hover{ border-bottom: 1px solid <?php echo $global['web_theme_color']; ?>; }
    .addartbtn input.btn.selected{ color: <?php echo $global['web_theme_color']; ?>;border-bottom: 1px solid <?php echo $global['web_theme_color']; ?>;}
    /*会员导航*/
    .member-nav-group .member-nav-item .btn.selected{background: <?php echo $global['web_theme_color']; ?>;border: 1px solid <?php echo $global['web_theme_color']; ?>;box-shadow: -1px 0 0 0 <?php echo $global['web_theme_color']; ?>;}
    .member-nav-group .member-nav-item:first-child .btn.selected{border-left: 1px solid <?php echo $global['web_theme_color']; ?> !important;}
        
    /* 商品订单列表标题 */
   .flexigrid .mDiv .ftitle h3 {border-left: 3px solid <?php echo $global['web_theme_color']; ?>;} 
    
    /*分页*/
    .pagination > .active > a, .pagination > .active > a:focus, 
    .pagination > .active > a:hover, 
    .pagination > .active > span, 
    .pagination > .active > span:focus, 
    .pagination > .active > span:hover { border-color: <?php echo $global['web_theme_color']; ?>;color:<?php echo $global['web_theme_color']; ?>; }
    
    .layui-form-onswitch {border-color: <?php echo $global['web_theme_color']; ?> !important;background-color: <?php echo $global['web_theme_color']; ?> !important;}
    .onoff .cb-enable.selected { background-color: <?php echo $global['web_theme_color']; ?> !important;border-color: <?php echo $global['web_theme_color']; ?> !important;}
    .onoff .cb-disable.selected {background-color: <?php echo $global['web_theme_color']; ?> !important;border-color: <?php echo $global['web_theme_color']; ?> !important;}
    .pcwap-onoff .cb-enable.selected { background-color: <?php echo $global['web_theme_color']; ?> !important;border-color: <?php echo $global['web_theme_color']; ?> !important;}
    .pcwap-onoff .cb-disable.selected {background-color: <?php echo $global['web_theme_color']; ?> !important;border-color: <?php echo $global['web_theme_color']; ?> !important;}
    input[type="text"]:focus,
    input[type="text"]:hover,
    input[type="text"]:active,
    input[type="password"]:focus,
    input[type="password"]:hover,
    input[type="password"]:active,
    textarea:hover,
    textarea:focus,
    textarea:active { border-color:<?php echo hex2rgba($global['web_theme_color'],0.8); ?>;box-shadow: 0 0 0 1px <?php echo hex2rgba($global['web_theme_color'],0.5); ?> !important;}
    .input-file-show:hover .type-file-button {background-color:<?php echo $global['web_theme_color']; ?> !important; }
    .input-file-show:hover {border-color: <?php echo $global['web_theme_color']; ?> !important;box-shadow: 0 0 5px <?php echo hex2rgba($global['web_theme_color'],0.5); ?> !important;}
    .input-file-show:hover span.show a,
    .input-file-show span.show a:hover { color: <?php echo $global['web_theme_color']; ?> !important;}
    .input-file-show:hover .type-file-button {background-color: <?php echo $global['web_theme_color']; ?> !important; }
    .color_z { color: <?php echo $global['web_theme_color']; ?> !important;}
    a.imgupload{
        background-color: <?php echo $global['web_theme_color']; ?> !important;
        border-color: <?php echo $global['web_theme_color']; ?> !important;
    }
    /*专题节点按钮*/
    .ncap-form-default .special-add{background-color:<?php echo $global['web_theme_color']; ?>;border-color:<?php echo $global['web_theme_color']; ?>;}
    .ncap-form-default .special-add:hover{background-color:<?php echo $global['web_assist_color']; ?>;border-color:<?php echo $global['web_assist_color']; ?>;}
    
    /*更多功能标题*/
    .on-off_panel .title::before {background-color:<?php echo $global['web_theme_color']; ?>;}
    .on-off_panel .on-off_list-caidan .icon_bg .on{color: <?php echo $global['web_theme_color']; ?>;}
    .on-off_panel .e-jianhao {color: <?php echo $global['web_theme_color']; ?>;}
    
     /*内容菜单*/
    .ztree li a:hover{color:<?php echo $global['web_theme_color']; ?> !important;}
    .ztree li a.curSelectedNode{background-color: <?php echo $global['web_theme_color']; ?> !important; border-color:<?php echo $global['web_theme_color']; ?> !important;}
    .layout-left .on-off-btn {background-color: <?php echo $global['web_theme_color']; ?> !important;}

    /*框架正在加载*/
    .iframe_loading{width:100%;background:url(/public/static/admin/images/loading-0.gif) no-repeat center center;}
    
    .layui-btn-normal {background-color: <?php echo $global['web_theme_color']; ?>;}
    .layui-laydate .layui-this{background-color: <?php echo $global['web_theme_color']; ?> !important;}
    .laydate-day-mark::after {background-color: <?php echo $global['web_theme_color']; ?> !important;}
	
    /*上传框*/
    .upload-body .upload-con .image-header .image-header-l .layui-btn {
        background: <?php echo $global['web_theme_color']; ?> !important;
        border-color: <?php echo $global['web_theme_color']; ?> !important;
    }
    .upload-body .layui-tab-brief>.layui-tab-title .layui-this {
        color: <?php echo $global['web_theme_color']; ?> !important;
    }
    .upload-body .layui-tab-brief>.layui-tab-title .layui-this:after {
        border-bottom-color: <?php echo $global['web_theme_color']; ?> !important;
    }
    .layui-layer-btn .layui-layer-btn0 {
        border-color: <?php echo $global['web_theme_color']; ?> !important;
        background-color: <?php echo $global['web_theme_color']; ?> !important;
    }
    .upload-body .layui-btn-yes {
        background: <?php echo $global['web_theme_color']; ?> !important;
        border-color: <?php echo $global['web_theme_color']; ?> !important;
    }
    .pagination li.active a {
        color: <?php echo $global['web_theme_color']; ?> !important;
    }
    .upload-nav .item.active {
        color: <?php echo $global['web_theme_color']; ?> !important;
    }
    .upload-body .upload-con .group-item.active a {
        color: <?php echo $global['web_theme_color']; ?> !important;
    }
    .upload-body .upload-con .group-item.active {
        color: <?php echo $global['web_theme_color']; ?> !important;
        background-color: <?php echo hex2rgba($global['web_theme_color'],0.1); ?>;
    }
    .pagination li.active {
        border-color: <?php echo $global['web_theme_color']; ?> !important;
    }
    .upload-body .layui-btn-normal {
        background-color: <?php echo $global['web_theme_color']; ?> !important;
    }
    .upload-body .upload-con .image-list li.up-over .image-select-layer i {
        color: <?php echo $global['web_theme_color']; ?> !important;
    }
    .upload-body .upload-con .image-list li .layer .close {
        color: <?php echo $global['web_theme_color']; ?> !important;
    }
    .upload-dirimg-con .ztree li a.curSelectedNode {
         background-color: #FFE6B0 !important; 
         border-color: #FFB951 !important; 
    }
    /*.plug-item-content .plug-item-bottm a, .plug-item-content .plug-status .yes {
        color: <?php echo $global['web_theme_color']; ?> !important;
    }*/
</style>
<?php endif; ?>
<script type="text/javascript" src="/public/static/common/js/jquery.min.js?t=v1.6.5"></script>
<!-- <script type="text/javascript" src="/public/plugins/tags_input/js/jquery.tagsinput.js?v=<?php echo $version; ?>"></script> -->
<script type="text/javascript" src="/public/static/admin/js/jquery-ui/jquery-ui.min.js?v=<?php echo $version; ?>"></script>
<script type="text/javascript" src="/public/plugins/layer-v3.1.0/layer.js?v=<?php echo $version; ?>"></script>
<script type="text/javascript" src="/public/static/admin/js/jquery.cookie.js?v=<?php echo $version; ?>"></script>
<script type="text/javascript" src="/public/static/admin/js/admin.js?v=<?php echo $version; ?>"></script>
<script type="text/javascript" src="/public/static/admin/js/jquery.validation.min.js?v=<?php echo $version; ?>"></script>
<script type="text/javascript" src="/public/static/admin/js/common.js?v=<?php echo $version; ?>"></script>
<script type="text/javascript" src="/public/static/admin/js/perfect-scrollbar.min.js?v=<?php echo $version; ?>"></script>
<script type="text/javascript" src="/public/static/admin/js/jquery.mousewheel.js?v=<?php echo $version; ?>"></script>
<script type="text/javascript" src="/public/plugins/layui/layui.js?v=<?php echo $version; ?>"></script>
<script type="text/javascript" src="/public/static/common/js/jquery.lazyload.min.js?v=<?php echo $version; ?>"></script>
<script src="/public/static/admin/js/myFormValidate.js?v=<?php echo $version; ?>"></script>
<script src="/public/static/admin/js/myAjax2.js?v=<?php echo $version; ?>"></script>
<script src="/public/static/admin/js/global.js?v=<?php echo $version; ?>"></script>
</head>
<body style="background-color: #FFF; overflow: auto;overflow-x: hidden;">
<div id="toolTipLayer" style="position: absolute; z-index: 9999; display: none; visibility: visible; left: 95px; top: 573px;"></div>
<div id="append_parent"></div>
<div id="ajaxwaitid"></div>
<div id="geduan_div" class="h10"></div>
<div id="page_div" class="page min-hg-c mb-20" style="min-width:auto;box-shadow:none;">
    <form class="form-horizontal" id="PostForm">
        <input type="hidden" name="list_id" value="<?php echo $list['list_id']; ?>">
        <div class="ncap-form-default">
            <dl class="row">
                <dt class="tit">
                    <label for="ac_name"><em>*</em>参数名称</label>
                </dt>
                <dd class="opt">
                    <input type="text" name="list_name" value="<?php echo $list['list_name']; ?>" id="list_name" class="input-txt" autocomplete="off">
                    <span class="err" id="err_attr_name" style="color: #F00; display: none;"></span>
                    <p class="notic"></p>
                </dd>
            </dl>
            <dl class="row">
                <dt class="tit">
                    <label for="ac_name">参数描述</label>
                </dt>
                <dd class="opt">
                    <input type="text" name="desc" value="<?php echo $list['desc']; ?>" id="desc" class="input-txt" autocomplete="off">
                    <p class="notic"></p>
                </dd>
            </dl>

            <dl class="row">
                <dt class="tit">
                    <label for="attr_input_type">参数值列表</label>
                </dt>
                <dd class="opt">
                    <a href="JavaScript:void(0);" class="ncap-btn ncap-btn-green" onclick="Add();">新增</a>
                    <div class="" style="margin-top: 15px;width: 650px;">
                        <div class="flexigrid">
                            <div class="hDiv">
                                <div class="hDivBox">
                                    <table cellspacing="0" cellpadding="0" style="width: 100%;">
                                        <thead>
                                            <tr>
                                                <th abbr="article_title" axis="col3" class="">
                                                    <div style="text-align: left; padding-left: 10px;" class="">参数值</div>
                                                </th>
                                                
                                                <th abbr="article_time" axis="col4" class="w120">
                                                    <div class="tc">类型</div>
                                                </th>

                                                <th abbr="article_time" axis="col4" class="w180">
                                                    <div class="tc" id="PromptText">默认值</div>
                                                </th>

                                                <th abbr="article_time" axis="col4" class="w80">
                                                    <div class="tc" >排序</div>
                                                </th>

                                                <th abbr="article_time" axis="col6" class="w60">
                                                    <div class="tc">操作</div>
                                                </th>
                                            </tr>
                                        </thead>
                                    </table>
                                </div>
                                <div class="bDiv" style="height: auto;">
                                    <div id="flexigrid" cellpadding="0" cellspacing="0" border="0">
                                        <table style="width: 100%;">
                                            <tbody id="Template">
                                            <?php if(empty($list['attr']) || (($list['attr'] instanceof \think\Collection || $list['attr'] instanceof \think\Paginator ) && $list['attr']->isEmpty())): ?>
                                                <tr id="empty_tr">
                                                    <td class="no-data" align="center" axis="col0" colspan="50">
                                                        <div class="no_row">
                                                            <div class="no_pic"><img src="/public/static/admin/images/null-data.png"></div>
                                                        </div>
                                                    </td>
                                                </tr>
                                            <?php else: if(is_array($list['attr']) || $list['attr'] instanceof \think\Collection || $list['attr'] instanceof \think\Paginator): if( count($list['attr'])==0 ) : echo "" ;else: foreach($list['attr'] as $k=>$vo): ?>
                                                    <tr class="tr" id="tr_<?php echo $k; ?>">
                                                        <td style="width: 100%">
                                                            <div style="">
                                                                <input type="text" name="attr_name[]" value="<?php echo $vo['attr_name']; ?>" style="width: 90%;" autocomplete="off">
                                                                <input type="hidden" name="attr_id[]" value="<?php echo $vo['attr_id']; ?>">
                                                            </div>
                                                        </td>

                                                        <td>
                                                            <div class="w120 tc">
                                                                <select name="attr_input_type[]" class="w100" data-id="OptContent_<?php echo $k; ?>" onchange="ChangeDisplay(this);">
                                                                    <option value="0" <?php if($vo['attr_input_type'] == '0'): ?> selected="true" <?php endif; ?>>输入框</option>
                                                                    <option value="1" <?php if($vo['attr_input_type'] == '1'): ?> selected="true" <?php endif; ?>>下拉框</option>
                                                                </select>
                                                            </div>
                                                        </td>

                                                        <td>
                                                            <div class="w180 tc" id="OptContent_<?php echo $k; ?>">
                                                                <?php if(1 == $vo['attr_input_type']): ?>
                                                                    <textarea rows="5" cols="30" name="attr_values[]" style="width: 166px;height: 60px;" placeholder="一行代表一个可选值"><?php echo $vo['attr_values']; ?></textarea>
                                                                <?php else: ?>
                                                                    ——————
                                                                    <input type="hidden" name="attr_values[]" value="<?php echo $vo['attr_values']; ?>" class="w160">
                                                                <?php endif; ?>
                                                            </div>
                                                        </td>

                                                        <td>
                                                            <div class="w80 tc">
                                                                <input type="text" name="attr_sort_order[]" style="width: 50px;text-align: center;" value="<?php echo $vo['sort_order']; ?>" >
                                                            </div>
                                                        </td>

                                                        <td class="">
                                                            <div class="w60 tc">
                                                                <a class="btn red" href="javascript:void(0);" data-id="tr_<?php echo $k; ?>" onclick="DelHtml(this)">删除</a>
                                                            </div>
                                                        </td>
                                                    </tr>
                                                <?php endforeach; endif; else: echo "" ;endif; endif; ?>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </dd>
            </dl>
        </div>
        <div class="ncap-form-default">
            <div class="bot">
                <a href="JavaScript:void(0);" class="ncap-btn-big ncap-btn-green" onclick="SubmitForm(this);" data-url="<?php echo url('ShopProduct/attrlist_edit', ['_ajax'=>1]); ?>">确认提交</a>
            </div>
        </div> 
    </form>
</div>

<script type="text/javascript">

    // tr数,取唯一标识
    var tr_id = $('#Template .tr').length;

    function Add() {
        if (0 == tr_id){
            $('#empty_tr').hide();
        }
        var AddHtml = '';
        AddHtml += 
            '<tr class="tr" id="tr_'+tr_id+'">'+
                '<td style="width: 100%">'+
                    '<div style="">'+
                        '<input type="text" name="attr_name[]" style="width: 90%;" autocomplete="off">'+
                        '<input type="hidden" name="attr_id[]">'+
                    '</div>'+
                '</td>'+

                '<td>'+
                    '<div class="w120 tc">'+
                        '<select name="attr_input_type[]" class="w100" data-id="OptContent_'+tr_id+'" onchange="ChangeDisplay(this);">'+
                            '<option value="0">输入框</option>'+
                            '<option value="1">下拉框</option>' +
                        '</select>'+
                    '</div>'+
                '</td>'+

                '<td>'+
                    '<div class="w180 tc" id="OptContent_'+tr_id+'">'+
                        '——————<input type="hidden" name="attr_values[]" autocomplete="off" class="w160">'+
                    '</div>'+
                '</td>'+

                '<td>'+
                    '<div class="w80 tc">'+
                        '<input type="text" name="attr_sort_order[]" style="width: 50px;text-align: center;" value="100" >'+
                    '</div>'+
                '</td>'+

                '<td class="">'+
                    '<div class="w60 tc">'+
                        '<a class="btn red" href="javascript:void(0);" data-id="tr_'+tr_id+'" onclick="DelHtml(this)">删除</a>'+
                    '</div>'+
                '</td>'+
            '</tr>';
        $('#Template').append(AddHtml);
        tr_id++;
    }

    // 删除未保存的数据
    function DelHtml(obj){
        $('#'+$(obj).attr('data-id')).remove();
        if (0 == $('.tr').length) $('#empty_tr').show();
    }

    // 字段切换，字段可选值框跟着改变
    function ChangeDisplay(obj) {
        var value = $(obj).val();
        if (1 == value){
            //多行文本
            var html = '<textarea rows="5" cols="30" name="attr_values[]" style="width: 166px;height: 60px;" placeholder="一行代表一个可选值"></textarea>';
        } else{
            var html = '——————<input type="hidden" name="attr_values[]" autocomplete="off" class="w160">';
        }
        $('#'+$(obj).attr('data-id')).empty().html(html);
    }

    // 添加新增数据
    function SubmitForm(obj) {
        var parentObj = parent.layer.getFrameIndex(window.name); //先得到当前iframe层的索引

        if($.trim($('input[name=list_name]').val()) == ''){
            showErrorMsg('参数名称不能为空！');
            $('input[name=list_name]').focus();
            return false;
        }

        layer_loading('正在处理');
        $.ajax({
            url : $(obj).attr('data-url'),
            data: $('#PostForm').serialize(),
            type: 'post',
            dataType: 'json',
            success: function(res) {
                layer.closeAll();
                if (res.code == 1) {
                    var _parent = parent;
                    _parent.layer.close(parentObj);
                    _parent.layer.msg(res.msg, {icon: 1, shade: layer_shade, time: 1000}, function(){
                        _parent.window.location.reload();
                    });
                } else {
                    showErrorAlert(res.msg);
                }
            },
            error: function(e) {
                layer.closeAll();
                showErrorAlert(e.responseText);
            }
        })
    }
</script>
<div id="goTop">
    <a href="JavaScript:void(0);" id="btntop">
        <i class="fa fa-angle-up"></i>
    </a>
    <a href="JavaScript:void(0);" id="btnbottom">
        <i class="fa fa-angle-down"></i>
    </a>
</div>

<script type="text/javascript">
    $(document).ready(function(){
        $('#think_page_trace_open').css('z-index', 99999);
    });
</script>
</body>
</html>
<script>
    try{
        var mt20_1649209614 = sessionStorage.getItem("mt20_1649209614");
        if (mt20_1649209614 == 1){
            $("#geduan_div").removeClass("h10");
        }else{
            $("#geduan_div").addClass("h10");
        }
    }catch(e){}
</script>