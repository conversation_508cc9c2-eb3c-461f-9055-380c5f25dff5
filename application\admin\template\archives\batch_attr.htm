<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>批量属性操作</title>
    <!-- 最新版本的 Bootstrap 核心 CSS 文件 -->
    <link rel="stylesheet" type="text/css" href="__PUBLIC__/plugins/bootstrap/css/bootstrap.min.css">
    {load href="__STATIC__/common/js/jquery.min.js" /}
    {load href="__PUBLIC__/plugins/layer-v3.1.0/layer.js" /}
    <script type="text/javascript">
        var eyou_basefile = "{$Request.baseFile}";
        var module_name = "{$Think.const.MODULE_NAME}";
        var __root_dir__ = "__ROOT_DIR__";
        var __lang__ = "{$admin_lang}";
        var __main_lang__ = "{$main_lang}";
    </script>
    <script src="__SKIN__/js/global.js?v={$version}"></script>
    <style type="text/css">
        .container-fluid {
            padding: 10px 10px;
            overflow: hidden;
        }
        .control-group {
            padding: 5px 0px;
        }
        .input{
            width: 100px;
        }
        .select {
            height: 28px;
            font-size: 12px;
            border: 1px solid #eee;
        	padding: 4px 0;
        }
        .btn{
            background-color: #4fc0e8;
            color: #FFF;
            border-radius: 4px;
            padding: 6px 20px;
        }
        .btn:hover {
            color: #FFF;
            background-color: #3aa8cf;

        }
        .controls{
            margin: 0 auto;
        }
        .notic{
            border:1px dashed #cae9f5;
            border-radius:6px;
            padding: 10px 16px;
            margin-top: 10px;
            background-color:#e7f6fc;
            color: #0d7fca;
        }
        label{
            font-weight: normal;
			color: #555;
        }
        em{
            color: red;
        }
		input[type="text"]{
		    color: #555555;
		    background-color: #FFF;
		    border: solid 1px #eee;
			padding: 4px 6px;
			resize: none;
		}
		input[type="text"]:focus, input[type="text"]:hover, input[type="text"]:active{
			border: 1px solid;
			border-color: rgba(82,168,236,0.8);
			box-shadow: 0 0 0 2px rgba(82, 168, 236, 0.15);
			outline: 0 none;
		}
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row-fluid">
            <div class="span12">
                <form class="form-horizontal" id="post_form" method="POST" action="{:url('Archives/batch_attr')}" onsubmit="check_submit();">
                    <div class="control-group">
                        <div class="controls">
                            <label class="control-label" for="inputEmail">文档属性：</label>
                            {volist name="archives_flags" id="vo"}
                            <label><input type="radio" value="{$vo.flag_fieldname}" name="attrType">{$vo.flag_name}[{$vo.flag_attr}]</label>&nbsp;
                            {/volist}
                        </div>
                    </div>
                    <div class="control-group">
                        <div class="controls">
                            <label class="control-label" for="inputEmail">文档ID：</label>
                            <input type="text" name="aids" value="" id="aids" class="input-txt" style="width: 280px;">
                        </div>
                    </div>
                    <div class="control-group">
                        <div class="controls" style="text-align: center; margin-top: 10px;">
							<input type="hidden" name="opt" value="{$Request.param.opt|default=''}">
							<input type="hidden" name="_ajax" value="1">
							<input type="button" class="btn" value="确认提交" onclick="check_submit();" />
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
    <script type="text/javascript">
        var parentObj = parent.layer.getFrameIndex(window.name); //先得到当前iframe层的索引
        function check_submit()
        {
            layer_loading('正在处理');
            $.ajax({
                url: $('#post_form').attr('action'),
                type: 'POST',
                dataType: 'JSON',
                data: $('#post_form').serialize(),
                success: function(res){
                    layer.closeAll();
                    if (res.code == 1) {
                        parent.layer.msg(res.msg, {shade: layer_shade, time: 1000}, function(){
                            parent.window.location.reload();
                            parent.layer.close(parentObj);
                        });
                    } else {
                        layer.alert(res.msg, {icon: 5, title:false});
                    }
                    return false;
                },
                error: function(e){
                    layer.closeAll();
                    layer.alert('操作失败', {icon: 5, title:false});
                    return false;
                }
            });
        }

        /**
         * 封装的加载层
         */
        function layer_loading(msg){
            var loading = layer.msg(
            msg+'...&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;请勿刷新页面', 
            {
                icon: 1,
                time: 3600000, //1小时后后自动关闭
                shade: [0.2] //0.1透明度的白色背景
            });
            //loading层
            var index = layer.load(3, {
                shade: [0.1,'#fff'] //0.1透明度的白色背景
            });

            return loading;
        }
    </script>
</body>
</html>