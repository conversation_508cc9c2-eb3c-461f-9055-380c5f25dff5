{include file="public/layout" /}
<body class="bodystyle">
<div id="toolTipLayer" style="position: absolute; z-index: 9999; display: none; visibility: visible; left: 95px; top: 573px;"></div>
<div id="append_parent"></div>
<div id="ajaxwaitid"></div>
<div class="page">
    <div class="fixed-bar">
        <div class="item-title">
            <div class="subject">
                <h3>修改管理员密码</h3>
                <h5></h5>
            </div>
        </div>
    </div>
    <form id="admin_form" name="admin_form" class="form-horizontal" action="{:url('Admin/admin_pwd')}" method="post">
        <input type="hidden" name="admin_id" value="{$admin_info.admin_id}">
        <div class="ncap-form-default">
            <dl class="row">
                <dt class="tit">
                    <label for="old_pw"><em>*</em>原密码</label>
                </dt>
                <dd class="opt">
                    <input type="password" name="old_pw" value="" id="old_pw" class="input-txt">
                    <p class="notic"></p>
                </dd>
            </dl>
            <dl class="row">
                <dt class="tit">
                    <label for="new_pw"><em>*</em>新密码</label>
                </dt>
                <dd class="opt">
                    <input type="password" name="new_pw" value="" id="new_pw" class="input-txt">
                    <p class="notic"></p>
                </dd>
            </dl>
            <dl class="row">
                <dt class="tit">
                    <label for="new_pw2"><em>*</em>确认密码</label>
                </dt>
                <dd class="opt">
                    <input type="password" name="new_pw2" value="" id="new_pw2" class="input-txt">
                    <p class="notic"></p>
                </dd>
            </dl>
            <div class="bot"><a href="JavaScript:void(0);" class="ncap-btn-big ncap-btn-green" id="submitBtn">确认提交</a></div>
        </div>
    </form>
</div>
<script type="text/javascript">
    $(function () {
     
        $('#submitBtn').click(function(){
            $('#admin_form').submit();
        });
        
        $("#admin_form").validate({
            debug: false, //调试模式取消submit的默认提交功能   
            focusInvalid: false, //当为false时，验证无效时，没有焦点响应  
            onkeyup: false,   
            submitHandler: function(form){   //表单提交句柄,为一回调函数，带一个参数：form   
                $.ajax({
                    url:"{:url('Admin/admin_pwd', ['_ajax'=>1])}",
                    type:'post',
                    dataType:'json',
                    data: $("#admin_form").serialize(),
                    success:function(obj){
                        if(obj.status !=1){
                            layer.alert(obj.msg, {icon: 5, title:false});
                        }else{
                            layer.alert('操作成功!', {icon: 6, title:false}, function(){
                                window.location.reload();
                            });
                        }  
                    }
                });
            },  
            ignore:":button",   //不验证的元素
            rules:{
                old_pw:{
                    required:true,
                    minlength:5
                },
                new_pw:{
                    required:true,
                    minlength:5
                },
                new_pw2:{
                    required:true,
                    minlength:5,
                    equalTo: "#new_pw"
                }
            },
            messages:{
                old_pw:{
                    required:"请输入原密码",
                    minlength:"原始密码长度不能少于5位"
                },
                new_pw:{
                    required:"请输入新密码",
                    minlength:"新密码长度不能少于5位"
                },
                new_pw2:{
                    required:"请输入确认密码",
                    minlength:"确认密码长度不能少于5位",
                    equalTo:"两次密码输入不一致"
                }
            }
        });
        
    });
</script>

{include file="public/footer" /}