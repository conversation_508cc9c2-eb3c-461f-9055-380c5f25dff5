{include file="public/layout" /}
<body class="rolecss bodystyle">
<div id="toolTipLayer" style="position: absolute; z-index: 9999; display: none; visibility: visible; left: 95px; top: 573px;"></div>
<div id="append_parent"></div>
<div id="ajaxwaitid"></div>
<div class="page">
    <form class="form-horizontal" id="postForm" action="{:url('Admin/admin_add')}" method="post">
        <div class="ncap-form-default">
            <dl class="row">
                <dt class="tit">
                    <label for="user_name"><em>*</em>用&nbsp;&nbsp;户&nbsp;&nbsp;名</label>
                </dt>
                <dd class="opt">
                    <input type="text" name="user_name" value="" id="user_name" onkeyup="check_user_name(this);" class="input-txt">
                    <p class="red" id="tips_user_name"></p>
                </dd>
            </dl>
            <dl class="row">
                <dt class="tit">
                    <label for="password"><em>*</em>用户密码</label>
                </dt>
                <dd class="opt">
                    <input type="text" name="password" value="" id="password" autocomplete="off" class="input-txt">
                    <p class="notic">推荐密码至少是以0-9a-zA-Z.@_-!等符号组合！</p>
                    <p id="password_tips"></p>
                </dd>
            </dl>
            <!-- <dl class="row">
                <dt class="tit">
                    <label for="password"><em>*</em>确认密码</label>
                </dt>
                <dd class="opt">
                    <input type="password" name="password2" value="" id="password2" autocomplete="off" class="input-txt">
                    <p class="notic"></p>
                    <p id="password2_tips"></p>
                </dd>
            </dl> -->
            <dl class="row">
                <dt class="tit">
                    <label for="pen_name">笔名</label>
                </dt>
                <dd class="opt">
                    <input type="text" name="pen_name" value="" id="pen_name" class="input-txt">
                    <p class="notic">发布文档后显示责任编辑的名字</p>
                </dd>
            </dl>
            <dl class="row">
                <dt class="tit">
                    <label for="true_name">真实姓名</label>
                </dt>
                <dd class="opt">
                    <input type="text" name="true_name" value="" id="true_name" class="input-txt">
                    <p class="notic"></p>
                </dd>
            </dl>
            <dl class="row">
                <dt class="tit">
                    <label for="mobile">手机号码</label>
                </dt>
                <dd class="opt">
                    <input type="text" name="mobile" value="" id="mobile" class="input-txt">
                    <p class="notic"></p>
                </dd>
            </dl>
            <dl class="row">
                <dt class="tit">
                    <label for="email">Email邮箱</label>
                </dt>
                <dd class="opt">
                    <input type="text" name="email" value="" id="email" class="input-txt">
                    <p class="notic"></p>
                </dd>
            </dl>
            <dl class="row">
                <dt class="tit">
                    <label>工作内容</label>
                </dt>
                <dd class="opt">
                    <textarea rows="5" cols="60" id="desc" name="desc" style="height:60px;"></textarea>
                    <span class="err"></span>
                    <p class="notic"></p>
                </dd>
            </dl>
            <dl class="row"><dt class="tit"><label><b>管理员权限设置</b></label></dt></dl>
            <dl class="row">
                <dt class="tit">
                    <label for="name">管理员角色组</label>
                </dt>
                <dd class="opt">
                    <p><label><input type="radio" name="role_id" value="-1" onclick="changeRole(-1);" checked="checked" />超级管理员</label></p>
                    {foreach name="admin_role_list" item="role" key="k"}
                    <p>
                        <label><input type="radio" name="role_id" value="{$role.id}" onclick="changeRole({$role.id});" {if condition="$role.id == $Request.param.role_id"} checked="checked"{/if} />{$role.name}</label>
                        <!-- &nbsp;<a href="javascript:void;" data-url="{:url('AuthRole/edit', array('id'=>$role.id,'iframe'=>1))}" onclick="addRole(this);">[编辑]</a>&nbsp;&nbsp;<a href="javascript:void;" data-url="{:url('AuthRole/del')}" data-id="{$role.id}" onclick="delfun(this);">[删除]</a> -->
                    </p>
                    {/foreach}
                    <p id="custom_role" style="padding-left: 13px; text-decoration:underline;"><label><a href="javascript:void(0);" data-url="{:url('AuthRole/add', array('iframe'=>1))}" onclick="addRole(this);">自定义</a></label></p>
                </dd>
            </dl>
            <dl class="row"><dt class="tit"><label><b>当前权限组预览</b></label></dt></dl>
<!--             <dl class="row">
                <dt class="tit">
                    <label for="name">语言权限</label>
                </dt>
                <dd class="opt">
                    <label><img class="cboximg" src="__SKIN__/images/ok.png" /><input type="checkbox" name="language[]" value="cn" class="none" />简体中文</label>
                    <span class="err"></span>
                    <p class="notic"></p>
                </dd>
            </dl> -->
            <dl class="row">
                <dt class="tit">
                    <label for="name">在线升级</label>
                </dt>
                <dd class="opt">
                    <label><img class="cboximg" src="__SKIN__/images/ok.png" /><input type="checkbox" name="online_update" value="1" class="none" />允许操作</label>
                    <span class="err"></span>
                    <p class="notic"></p>
                </dd>
            </dl>
            <dl class="row">
                <dt class="tit">
                    <label for="name">功能地图</label>
                </dt>
                <dd class="opt">
                    <label><img class="cboximg" src="__SKIN__/images/ok.png" /><input type="checkbox" name="switch_map" value="1" class="none" />允许操作</label>
                    <span class="err"></span>
                    <p class="notic"></p>
                </dd>
            </dl>
            <dl class="row">
                <dt class="tit">
                    <label for="name">文档权限</label>
                </dt>
                <dd class="opt">
                    <label><img class="cboximg" src="__SKIN__/images/ok.png" /><input type="checkbox" name="only_oneself" value="1" class="none" />只允许查看自己发布的文档</label>
                    <span class="err"></span>
                    <p class="notic"></p>
                </dd>
            </dl>
            <dl class="row">
                <dt class="tit">
                    <label for="name">文档审核</label>
                </dt>
                <dd class="opt">
                    <label><img class="cboximg" src="__SKIN__/images/ok.png" /><input type="checkbox" name="check_oneself" value="1" class="none" />发布文档自动通过审核</label>
                    <span class="err"></span>
                    <p class="notic"></p>
                </dd>
            </dl>
            <dl class="row">
                <dt class="tit">
                    <label for="name">操作权限</label>
                </dt>
                <dd class="opt">
                    <p><label><img class="cboximg" src="__SKIN__/images/ok.png" /><input type="checkbox" id="select_cud" class="none" />完全控制</label></p>
                    <p><label><img class="cboximg" src="__SKIN__/images/ok.png" /><input type="checkbox" name="cud[]" value="add" class="none" />添加信息</label></p>
                    <p><label><img class="cboximg" src="__SKIN__/images/ok.png" /><input type="checkbox" name="cud[]" value="edit" class="none" />修改信息</label></p>
                    <p><label><img class="cboximg" src="__SKIN__/images/ok.png" /><input type="checkbox" name="cud[]" value="del" class="none" />删除信息</label></p>
                    <p><label><img class="cboximg" src="__SKIN__/images/ok.png" /><input type="checkbox" name="cud[]" value="changetableval" class="none" />审核信息</label></p>
                    <span class="err"></span>
                    <p class="notic"></p>
                </dd>
            </dl>
            <dl class="row">
                <dt class="tit">
                    <label for="name">功能权限</label>
                </dt>
                <dd class="opt">
                    <p>
                        <label><img class="cboximg" src="__SKIN__/images/ok.png" /><input type="checkbox" id="select_all_permission" class="none" />全部选择</label>
                    </p>

                    {foreach name="modules" item="vo"}
                      {foreach name="vo.child" item="vo2"}
                        {if condition="1 == $vo2['is_modules'] AND ! empty($auth_rule_list[$vo2.id])"}
                          {if condition="1002 == $vo2['id']"}
                            <div class="admin_poplistdiv">
                                <h2>{$vo2.name}</h2>
                                {if condition="! empty($arctype_p_html)"}
                                <p>
                                    {$arctype_p_html}
                                </p>
                                {$arctype_child_html}
                                {/if}
                            </div>
                          {else /}
                            <div class="admin_poplistdiv">
                                <h2>{$vo2.name}</h2>
                                <p>
                                    {foreach name="auth_rule_list[$vo2.id]" item="rule"}
                                    <label><img class="cboximg" src="__SKIN__/images/ok.png" /><input type="checkbox" class="none" name="permission[rules][]" value="{$rule.id}" />{$rule.name}</label>
                                    {/foreach}
                                </p>
                            </div>
                          {/if}
                        {/if}
                      {/foreach}
                    {/foreach}
                    
                    {if condition="! empty($plugins)"}
                    <div class="admin_poplistdiv">
                        <h2>插件应用</h2>
                        <ul>
                            {foreach name="plugins" item="plugin"}
                            <li>
                                <label><img class="cboximg" src="__SKIN__/images/ok.png" /><input type="checkbox" class="none" name="permission[plugins][{$plugin.code}][code]" value="{$plugin.code}" />{$plugin.name}</label>
                                {php}$config = json_decode($plugin['config'], true);{/php}
                                {if condition="! empty($config['permission'])"}
                                <p style="padding-left:10px;">
                                    <span class="button level1 switch center_docu"></span>
                                    {foreach $config['permission'] as $index => $text}
                                    <label><img class="cboximg" src="__SKIN__/images/ok.png" /><input type="checkbox" class="none" name="permission[plugins][{$plugin.code}][child][]" value="{$index}" />{$text}</label>
                                    {/foreach}
                                </p>
                                {/if}
                            </li>
                            {/foreach}
                        </ul>
                    </div>
                    {/if}
                </dd>
            </dl>
            <div class="bot2">
                {:token('__token_admin_add__')}
                <a href="JavaScript:void(0);" onclick="adsubmit();" class="ncap-btn-big ncap-btn-green" id="submitBtn">确认提交</a>
            </div>
        </div>
    </form>
</div>
<textarea name="admin_role_list" id="admin_role_list" class="none">{$admin_role_list|json_encode}</textarea>
<script type="text/javascript">
    $(function(){
        // 默认全部禁用复选框
        $('#postForm input[type="checkbox"]').attr("disabled","disabled");

        /*超级管理员默认全选复选框*/
        if (-1 == $('#postForm input[name="role_id"]').val()) {
            $('#postForm input[type="checkbox"]').prop('checked', 'checked');
            $('#postForm img.cboximg').attr('src', '__SKIN__/images/ok.png');
            // $('#postForm input[name=only_oneself]').val(0);
            // $('#postForm input[name=only_oneself]').parent().find('img.cboximg').attr('src', '__SKIN__/images/del.png');
        }
        /*--end*/

        $('.arctype_bg').bind('click', function(){
            var acid = $(this).next().find('input').val(), input = 'arctype_child_' + acid;
            $('.arctype_child').hide();
            if( $(this).attr('class').indexOf('expandable') == -1 ){
                $(this).removeClass('collapsable').addClass('expandable');
            }else{
                $('.arctype_bg').removeClass('collapsable').addClass('expandable');
                $(this).removeClass('expandable').addClass('collapsable');
                $('#'+input).show();
            }
        });
        $('.arctype_cbox').bind('click', function(){
            var acid = $(this).val(), input = 'arctype_child_' + acid;
            var pid = $(this).data('pid');
            var tpid = $(this).data('tpid');
            if($(this).prop('checked')){
                if (0 < $('input[data-pid="'+pid+'"]:checked').length) {
                    $('.arctype_id_'+pid).prop('checked', 'checked');
                    $('.arctype_id_'+pid).parent().find('img.cboximg').attr('src', '__SKIN__/images/ok.png');
                }
                if (0 < $('#arctype_child_'+tpid).find('input[type="checkbox"]:checked').length) {
                    $('.arctype_id_'+tpid).prop('checked', 'checked');
                    $('.arctype_id_'+tpid).parent().find('img.cboximg').attr('src', '__SKIN__/images/ok.png');
                }
                $('#'+input).find('input[type="checkbox"]').prop('checked', 'checked');
                $('#'+input).find('input[type="checkbox"]').parent().find('img.cboximg').attr('src', '__SKIN__/images/ok.png');
            }else{
                if (1 > $('input[data-pid="'+pid+'"]:checked').length) {
                    $('.arctype_id_'+pid).removeAttr('checked');
                    $('.arctype_id_'+pid).parent().find('img.cboximg').attr('src', '__SKIN__/images/del.png');
                }
                if (1 > $('#arctype_child_'+tpid).find('input[type="checkbox"]:checked').length) {
                    $('.arctype_id_'+tpid).removeAttr('checked');
                    $('.arctype_id_'+tpid).parent().find('img.cboximg').attr('src', '__SKIN__/images/del.png');
                }
                $('#'+input).find('input[type="checkbox"]').removeAttr('checked');
                $('#'+input).find('input[type="checkbox"]').parent().find('img.cboximg').attr('src', '__SKIN__/images/del.png');
            }
        });
        $('#select_cud').bind('click', function(){
            if($(this).prop('checked')){
                $('#postForm input[name^="cud"]').prop('checked', 'checked');
                $('#postForm input[name^="cud"]').parent().find('img.cboximg').attr('src', '__SKIN__/images/ok.png');
            }else{
                $('#postForm input[name^="cud"]').removeAttr('checked');
                $('#postForm input[name^="cud"]').parent().find('img.cboximg').attr('src', '__SKIN__/images/del.png');
            }
        });

        $('#select_all_permission').bind('click', function(){
            if($(this).prop('checked')){
                $('#postForm input[name^="permission"]').prop('checked', 'checked');
                $('#postForm input[name^="permission"]').parent().find('img.cboximg').attr('src', '__SKIN__/images/ok.png');
            }else{
                $('#postForm input[name^="permission"]').removeAttr('checked');
                $('#postForm input[name^="permission"]').parent().find('img.cboximg').attr('src', '__SKIN__/images/del.png');
            }
        });
        $('#postForm input[name^="permission"],#postForm input[name^="cud"]').bind('click', function(){
            hasSelectAll();
        });
    });

    function hasSelectAll(){
        var c = true;
        $('#postForm input[name^="permission"]').each(function(idx, ele){
            if(! $(ele).prop('checked')){
                c = false;
                return;
            }
        });
        if(c){
            $('#select_all_permission').prop('checked', 'checked');
            $('#select_all_permission').parent().find('img.cboximg').attr('src', '__SKIN__/images/ok.png');
        }else{
            $('#select_all_permission').removeAttr('checked');
            $('#select_all_permission').parent().find('img.cboximg').attr('src', '__SKIN__/images/del.png');
        }

        var c1 = true;
        $('#postForm input[name^="cud"]').each(function(idx, ele){
            if(! $(ele).prop('checked')){
                c1 = false;
                return;
            }
        });
        if(c1){
            $('#select_cud').prop('checked', 'checked');
            $('#select_cud').parent().find('img.cboximg').attr('src', '__SKIN__/images/ok.png');
        }else{
            $('#select_cud').removeAttr('checked');
            $('#select_cud').parent().find('img.cboximg').attr('src', '__SKIN__/images/del.png');
        }
    }

    function changeRole(value){
        if (-1 == value) {
            $('#postForm input[type="checkbox"]').attr("checked","checked").attr('disabled', 'disabled');
            $('#postForm img.cboximg').attr('src', '__SKIN__/images/ok.png');
            // $('#postForm input[name=only_oneself]').val(0);
            // $('#postForm input[name=only_oneself]').parent().find('img.cboximg').attr('src', '__SKIN__/images/del.png');
            return;
        }
        
        $('#postForm input[name!="role_id"]').removeAttr('checked').removeAttr('disabled');
        $('#postForm img.cboximg').attr('src', '__SKIN__/images/del.png');

        /*自定义权限组*/
        // if(value == "0"){
        //     $('#postForm input[name!="role_id"]').prop('checked', 'checked');
        //     $('#postForm input[name="online_update"]').removeAttr('checked');
        //     $('#postForm input[name="switch_map"]').removeAttr('checked');
        //     $('#postForm input[name="only_oneself"]').removeAttr('checked');
        //     return ;
        // }
        /*--end*/
        var admin_role_list = JSON.parse($('#admin_role_list').val());
        for(var i in admin_role_list){
            var item = admin_role_list[i];
            if(item.id == value){
                if(item.language){
                    item.language.map(function(row){
                        $('#postForm input[name^="language"][value="'+row+'"]').prop('checked', 'checked');
                        $('#postForm input[name^="language"][value="'+row+'"]').parent().find('img.cboximg').attr('src', '__SKIN__/images/ok.png');
                    });
                }

                if(item.online_update){
                    $('#postForm input[name="online_update"]').prop('checked', 'checked');
                    $('#postForm input[name="online_update"]').parent().find('img.cboximg').attr('src', '__SKIN__/images/ok.png');
                };
                if(item.switch_map){
                    $('#postForm input[name="switch_map"]').prop('checked', 'checked');
                    $('#postForm input[name="switch_map"]').parent().find('img.cboximg').attr('src', '__SKIN__/images/ok.png');
                };
                // if(item.editor_visual){
                //     $('#postForm input[name="editor_visual"]').prop('checked', 'checked');
                //     $('#postForm input[name="editor_visual"]').parent().find('img.cboximg').attr('src', '__SKIN__/images/ok.png');
                // };
                if(item.only_oneself){
                    $('#postForm input[name="only_oneself"]').prop('checked', 'checked');
                    $('#postForm input[name="only_oneself"]').parent().find('img.cboximg').attr('src', '__SKIN__/images/ok.png');
                };
                if(item.check_oneself){
                    $('#postForm input[name="check_oneself"]').prop('checked', 'checked');
                    $('#postForm input[name="check_oneself"]').parent().find('img.cboximg').attr('src', '__SKIN__/images/ok.png');
                };
                if(item.cud){
                    item.cud.map(function(row){
                        $('#postForm input[name^="cud"][value="'+row+'"]').prop('checked', 'checked');
                        $('#postForm input[name^="cud"][value="'+row+'"]').parent().find('img.cboximg').attr('src', '__SKIN__/images/ok.png');
                    });
                }

                if(item.permission){
                    for(var p in item.permission){
                        if(p == 'plugins'){
                            if(item.permission[p]){
                                for(var pluginId in item.permission[p]){
                                    $('#postForm input[name="permission['+p+']['+pluginId+'][code]"][value="'+pluginId+'"]').prop('checked', 'checked');
                                    $('#postForm input[name="permission['+p+']['+pluginId+'][code]"][value="'+pluginId+'"]').parent().find('img.cboximg').attr('src', '__SKIN__/images/ok.png');
                                    if(item.permission[p][pluginId].child){
                                        item.permission[p][pluginId].child.map(function(row){
                                            $('#postForm input[name="permission['+p+']['+pluginId+'][child][]"][value="'+row+'"]').prop('checked', 'checked');
                                            $('#postForm input[name="permission['+p+']['+pluginId+'][child][]"][value="'+row+'"]').parent().find('img.cboximg').attr('src', '__SKIN__/images/ok.png');
                                        });
                                    }
                                }
                            }
                        } else if (p == 'arctype') {
                            item.permission[p].map(function(row){
                                $('#postForm .arctype_id_'+row).prop('checked', 'checked');
                                $('#postForm .arctype_id_'+row).parent().find('img.cboximg').attr('src', '__SKIN__/images/ok.png');
                            });
                        }else{
                            item.permission[p].map(function(row){
                                $('#postForm input[name="permission['+p+'][]"][value="'+row+'"]').prop('checked', 'checked');
                                $('#postForm input[name="permission['+p+'][]"][value="'+row+'"]').parent().find('img.cboximg').attr('src', '__SKIN__/images/ok.png');
                            });
                        }
                        
                    }
                }

                hasSelectAll();
                $('#postForm input[type="checkbox"]').attr('disabled', 'disabled');
                break;
            }
        }
    }

    function addRole(obj)
    {
        var url = $(obj).data('url');
        // iframe窗
        layer.open({
            type: 2,
            title: '自定义权限组',
            fixed: true, //不固定
            shadeClose: false,
            shade: layer_shade,
            maxmin: false, //开启最大化最小化按钮
            area: ['90%', '90%'],
            content: url
        });
    }

    function custom_role(str, new_role_id, auth_role_list)
    {
        $('#custom_role').before(str);
        $('#admin_role_list').val(auth_role_list);
        changeRole(new_role_id);
    }

    function check_user_name(obj)
    {
        var user_name = $(obj).val();
        $.ajax({
            url: "{:url('Admin/ajax_add_user_name')}",
            type: "POST",
            dataType: "JSON",
            async: false,
            data: {user_name:user_name, _ajax:1},
            success: function(res){
                if (0 == res.code) {
                    $('#tips_user_name').html(res.msg);
                } else {
                    $('#tips_user_name').html('');
                }
            }
        });
    }

    $('#password').keyup(function(){
        var password = $(this).val();
        $.ajax({
            url: "{:url('Admin/ajax_checkPasswordLevel')}",
            type: "POST",
            dataType: "JSON",
            data: {password:password, _ajax:1},
            success: function(res){
                $('#password_tips').removeAttr('class');
                if (1 == res.code) {
                    $('#password_tips').addClass('rank r'+res.data.pwdLevel);
                }
            }
        });
    });
/*
    $('#password2').keyup(function(){
        var password = $('#password').val();
        var password2 = $('#password2').val();
        $('#password2_tips').hide();
        if (password != '' || password2 != '') {
            if (password != password2) {
                $('#password2_tips').html('<font color="red">两次密码输入不一致！</font>').show();
            } else {
                $('#password2_tips').html('<font color="green">校验通过！</font>').show();
            }
        }
    });
*/
    var parentObj = parent.layer.getFrameIndex(window.name); //先得到当前iframe层的索引
    // 判断输入框是否为空
    function adsubmit(){
        var user_name = $('input[name=user_name]').val();
        if($.trim(user_name) == ''){
            showErrorMsg('用户名不能为空！');
            $('input[name=user_name]').focus();
            return false;
        }
        var password = $.trim($('#password').val());
        if(password == ''){
            showErrorMsg('用户密码不能为空！');
            $('input[name=password]').focus();
            return false;
        } else {
            if (0 < password.length) {
                {$pwdJsCode|default=''}
            }
        }
        // var password2 = $('#password2').val();
        // if(password2 == ''){
        //     showErrorMsg('确认密码不能为空！');
        //     $('input[name=password2]').focus();
        //     return false;
        // }
        // if (password != password2) {
        //     showErrorMsg('两次密码输入不一致！');
        //     $('input[name=password]').focus();
        //     return false;
        // }
        var role_id = $('input[name=role_id]:checked').val();
        if(role_id == '' || undefined == role_id){
            showErrorMsg('请选择权限组！');
            $('input[name=role_id]').focus();
            return false;
        }

        layer_loading('正在处理');
        $.ajax({
            type : 'post',
            url : "{:url('Admin/admin_add', ['_ajax'=>1])}",
            data : $('#postForm').serialize(),
            dataType : 'json',
            success : function(res){
                layer.closeAll();
                if(res.code == 1){
                    var _parent = parent;
                    _parent.layer.msg(res.msg, {shade: layer_shade, time: 1000}, function(){
                        _parent.window.location.reload();
                    });
                }else{
                    showErrorMsg(res.msg);
                    $('input[name='+res.data.input_name+']').focus();
                }
            },
            error: function(e){
                layer.closeAll();
                layer.alert(e.responseText, {icon: 5, title:false});
            }
        });
    }
</script>

{include file="public/footer" /}