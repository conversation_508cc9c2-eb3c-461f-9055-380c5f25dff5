{include file="public/layout" /}
<body style="background-color: #FFF; overflow: auto;">
<div id="toolTipLayer" style="position: absolute; z-index: 9999; display: none; visibility: visible; left: 95px; top: 573px;"></div> 
<div id="append_parent"></div>
<div id="ajaxwaitid"></div>
<div class="page" style="box-shadow:none;">
    <div class="fixed-bar">
        <div class="item-title"><a class="back_xin" href="javascript:history.back();" title="返回"><i class="iconfont e-fanhui"></i></a>
            <div class="subject">
                <h3>批量增加</h3>
                <h5></h5>
            </div>
        </div>
    </div>
    <form class="form-horizontal" id="post_form" action="{:url('Arctype/batch_add')}" method="post">
        <!-- 常规选项 -->
        <div class="ncap-form-default">
            <dl class="row">
                <dt class="tit">
                    <label for="current_channel">内容模型</label>
                </dt>
                <dd class="opt">
                    <select class="small form-control" id="current_channel" name="current_channel" onchange="ajax_get_template();">
                        {volist name="channeltype_list" id="vo"}
                        <option value="{$vo.id}" data-nid="{$vo.nid}">{$vo.title}</option>
                        {/volist}
                    </select>
                    <span class="err"></span>
                    <p class="" id="notic_current_channel"></p>
                </dd>
            </dl>
            <dl class="row">
                <dt class="tit">
                    <label for="parent_id">所属栏目</label>
                </dt>
                <dd class="opt">
                    <select class="small form-control" id="parent_id" name="parent_id" onchange="set_grade(this);">
                        {$select_html}
                    </select>
                    <span class="err"></span>
                    <p class="notic">如果选择上级栏目，那么新增的栏目则为被选择上级栏目的子栏目</p>
                </dd>
            </dl>
            <dl class="row">
                <dt class="tit">
                    <label>隐藏栏目</label>
                </dt>
                <dd class="opt">
                    <label class="curpoin"><input id="is_hidden1" name="is_hidden" value="1" type="radio">是</label>
                    &nbsp;
                    <label class="curpoin"><input id="is_hidden0" name="is_hidden" value="0" type="radio" checked="checked">否</label>
                    <p class="notic">隐藏之后，不显示在前台页面</p>
                </dd>
            </dl>
            <dl class="row" id="dl_templist">
                <dt class="tit">
                    <label for="templist"><em>*</em>列表模板</label>
                </dt>
                <dd class="opt">
                    <select name="templist" id="templist">
                    </select>
                    <span class="err"></span>
                    <p class="notic">列表模板命名规则：<br/>lists_<font class="font_nid">模型标识</font>.htm<br/>lists_<font class="font_nid">模型标识</font>_自定义.htm</p>
                    &nbsp;<a href="javascript:void(0);" onclick="newtpl('lists');" class="ncap-btn ncap-btn-green">新建模板</a>
                </dd>
            </dl>
            <dl class="row" id="dl_tempview">
                <dt class="tit">
                    <label for="tempview"><em>*</em>文档模板</label>
                </dt>
                <dd class="opt">
                    <select name="tempview" id="tempview">
                    </select>
                    <span class="err"></span>
                    <p class="notic">文档模板命名规则：<br/>view_<font class="font_nid">模型标识</font>.htm<br/>view_<font class="font_nid">模型标识</font>_自定义.htm</p>
                    &nbsp;<a href="javascript:void(0);" onclick="newtpl('view');" class="ncap-btn ncap-btn-green">新建模板</a>
                </dd>
            </dl>
            <dl class="row flexigrid">
                <dt class="tit">
                    <label for="title"><em>*</em>栏目填写</label>
                </dt>
                <dd class="opt bDiv">
                    <div class="hDiv">
                        <div class="hDivBox">
                            <table cellspacing="0" cellpadding="0" style="width: 100%">
                                <thead>
                                <tr id="thead_header_0" class="">
                                    <th abbr="article_time" axis="col6" class="w60">
                                        <div class="tc">排序</div>
                                    </th>
                                    <th abbr="article_time" axis="col6" class="w100">
                                        <div class="tc">栏目图片</div>
                                    </th>
                                    <th abbr="article_time" axis="col6" class="w200">
                                        <div class="tc"><font color="red" style="font: bold 14px/20px tahoma, verdana;margin-right: 5px;">*</font>顶级栏目名称</div>
                                    </th>
                                    <th abbr="article_title" axis="col3" class="">
                                        <div style="text-align: left; padding-left: 10px;" class="">子栏目(用"目录名称1,目录名称2..."这样表示多个栏目)</div>
                                    </th>
                                    <th axis="col1" class="w60">
                                        <div class="tc">
                                            <a href="javascript:void(0);" class="btn blue" onclick="batch_add_tr(0);"><i class="fa fa-pencil-square-o"></i>添加</a>
                                        </div>
                                    </th>
                                </tr>
                                <tr id="thead_header_1" class="none">
                                    <th abbr="article_time" axis="col6" class="w60">
                                        <div class="tc">排序</div>
                                    </th>
                                    <th abbr="article_time" axis="col6" class="w100">
                                        <div class="tc">栏目图片</div>
                                    </th>
                                    <th abbr="article_title" axis="col3" class="">
                                        <div style="text-align: left; padding-left: 10px;" class=""><font color="red" style="font: bold 14px/20px tahoma, verdana;margin-right: 5px;">*</font>栏目名称</div>
                                    </th>
                                    <th axis="col1" class="w60">
                                        <div class="tc">
                                            <a href="javascript:void(0);" class="btn blue" onclick="batch_add_tr(1);"><i class="fa fa-pencil-square-o"></i>添加</a>
                                        </div>
                                    </th>
                                </tr>
                                </thead>
                            </table>
                        </div>
                    </div>
                    <div class="bDiv" style="height: auto;">
                        <div cellpadding="0" cellspacing="0" border="0">
                            <table style="width: 100%;">
                                <tbody id="batch_list_0">
                                    {for start="1" end="6"}
                                    <tr id="tr_0_{$i}" class="">
                                        <td class="sort">
                                            <div class="w60 tc">
                                                <input type="text" name="sort_order[]" value="100" onkeyup="this.value=this.value.replace(/[^\d]/g,'');" onpaste="this.value=this.value.replace(/[^\d]/g,'');" />
                                            </div>
                                        </td>
                                        <td class="">
                                            <div class="type-dan-pane w100 tc">
                                                <div class="images_upload images_upload_html">
                                                    <a href="javascript:void(0);" onclick="arctypeImageAdd({$i});" class="img-upload no-float" title="点击上传">
                                                        <div class="y-line" id="litpic_local_y_line_{$i}" ></div>
                                                        <div class="x-line" id="litpic_local_x_line_{$i}" ></div>
                                                        <img src="" id="litpic_local_src_{$i}" class="pic_con" style="display: none;">
                                                    </a>
                                                    <a href="javascript:void(0)" onclick="arctypeImageClear({$i})" class="delect" title="删除"></a>
                                                    <a href="javascript:void(0)" onclick="arctypeImageAdd({$i});" class="edit" title="编辑"><i class="iconfont e-bianji"></i></a>
                                                    <input type="hidden" id="litpic_local_{$i}" name="litpic_local[]">
                                                </div>
                                            </div>
                                        </td>
                                        <td class="">
                                            <div class="w200 tc">
                                                <input type="text" name="toptype[]" class="w150" value="" />
                                            </div>
                                        </td>
                                        <td class="" style="width: 100%;">
                                            <div class="tl" style="padding-left: 10px;">
                                                <input type="text" name="sontype[]" style="width: 96%;" value="" />
                                            </div>
                                        </td>
                                        <td class="">
                                            <div class="w60 tc btn_del_0">
                                                {eq name='$i' value='1'}
                                                <a class="btn grey" href="javascript:void(0);" title='至少保留一个'>删除</a>
                                                {else /}
                                                <a class="btn red" href="javascript:void(0);" onclick="$('#tr_0_{$i}').remove();">删除</a>
                                                {/eq}
                                            </div>
                                        </td>
                                    </tr>
                                    {/for}
                                </tbody>
                                <tbody id="batch_list_1" class="none">
                                    {for start="1" end="6"}
                                    <tr id="tr_1_{$i}">
                                        <td class="sort">
                                            <div class="w60 tc">
                                                <input type="text" name="sort_order_1[]" value="100"  onkeyup="this.value=this.value.replace(/[^\d]/g,'');" onpaste="this.value=this.value.replace(/[^\d]/g,'');" />
                                            </div>
                                        </td>
                                        <td class="">
                                            <div class="type-dan-pane w100 tc">
                                                <div class="images_upload images_upload_html">
                                                    <a href="javascript:void(0);" onclick="arctypeImageAdd({$i});" class="img-upload no-float" title="点击上传">
                                                        <div class="y-line" id="litpic_local_y_line_{$i}" ></div>
                                                        <div class="x-line" id="litpic_local_x_line_{$i}" ></div>
                                                        <img src="" id="litpic_local_src_{$i}" class="pic_con" style="display: none;">
                                                    </a>
                                                    <a href="javascript:void(0)" onclick="arctypeImageClear({$i})" class="delect" title="删除"></a>
                                                    <a href="javascript:void(0)" onclick="arctypeImageAdd({$i});" class="edit" title="编辑"><i class="iconfont e-bianji"></i></a>
                                                    <input type="hidden" id="litpic_local_{$i}" name="litpic_local[]">
                                                </div>
                                            </div>
                                        </td>
                                        <td class="" style="width: 100%;">
                                            <div class="tl" style="padding-left: 10px;">
                                                <input type="text" name="reltype[]" style="width: 96%;" value="" />
                                            </div>
                                        </td>
                                        <td class="">
                                            <div class="w60 tc btn_del_1">
                                                {eq name='$i' value='1'}
                                                <a class="btn" href="javascript:void(0);" style="background: #e6e6e6;"><i class="fa fa-trash-o"></i>删除</a>
                                                {else /}
                                                <a class="btn red" href="javascript:void(0);" onclick="$('#tr_1_{$i}').remove();"><i class="fa fa-trash-o"></i>删除</a>
                                                {/eq}
                                            </div>
                                        </td>
                                    </tr>
                                    {/for}
                                </tbody>
                            </table>
                        </div>
                        <div class="iDiv" style="display: none;"></div>
                    </div>
                </dd>
            </dl>
        </div>
        <!-- 常规选项 -->
        <div class="ncap-form-default">
            <div class="bot">
                <input type="hidden" name="grade" id="grade" value="0">
                <input type="hidden" name="dirpath" id="dirpath">
                <a href="JavaScript:void(0);" onclick="check_submit();" class="ncap-btn-big ncap-btn-green" id="submitBtn">确认提交</a>
            </div>
        </div> 
    </form>
</div>

<div id="append_tr" class="none"></div>

<script type="text/javascript">
    var arctypeIndex = 0;
    function arctypeImageAdd(index) {
        arctypeIndex = index;
        GetUploadify(1, '', 'allimg', 'arctypeImageCallBack');
    }
    function arctypeImageCallBack(fileurl_tmp) {
        $('#litpic_local_'+arctypeIndex).val(fileurl_tmp);
        $('#litpic_local_src_'+arctypeIndex).show().attr('src', fileurl_tmp);
        $('#litpic_local_y_line_'+arctypeIndex+', #litpic_local_x_line_'+arctypeIndex).hide();
    }
    function arctypeImageClear(arctypeIndex) {
        $('#litpic_local_'+arctypeIndex).val('');
        $('#litpic_local_src_'+arctypeIndex).hide().attr('src', '');
        $('#litpic_local_y_line_'+arctypeIndex+', #litpic_local_x_line_'+arctypeIndex).show();
    }

    var templateList = {$templateList|json_encode};

    $(document).ready(function(){    
        ajax_get_template();
    });

    /*根据模型ID获取模板文件名*/
    function ajax_get_template() {
        var obj = $('#current_channel');
        var channel = parseInt($(obj).find('option:selected').val());
        var js_allow_channel_arr = {$js_allow_channel_arr};
        $('#notic_current_channel').html('');

        // 重新定义模板变量，专用于新建模板功能
        $.ajax({
            url: "{:url('Arctype/ajax_getTemplateList')}",
            type: 'GET',
            dataType: 'JSON',
            data: {_ajax:1},
            success: function(res){
                if (res.code == 1) {
                    templateList = res.data.templateList;
                }
            }
        });
        // end

        if (templateList[channel] == undefined || templateList[channel] == '') {
            showErrorMsg('模板文件不存在！');
            return false;
        } else if (templateList[channel]['msg'] != '') {
            $('#notic_current_channel').html(templateList[channel]['msg']);
        }

        $('#templist').html(templateList[channel]['lists']);
        if ($.inArray(channel, js_allow_channel_arr) == -1) {
            if (channel == 6) {
                $('#dl_templist').find('label[for=templist]').html('<em>*</em>单页模板');
            } else if (channel == 8) {
                $('#dl_templist').find('label[for=templist]').html('<em>*</em>留言模板');
            }
            $('#dl_tempview').hide();
        } else {
            $('#dl_templist').find('label[for=templist]').html('<em>*</em>列表模板');
            $('#dl_tempview').show();
        }
        $('#tempview').html(templateList[channel]['view']);

        $('.font_nid').html(templateList[channel]['nid']);

        return false;
    }
    /*--end*/

    function set_grade(obj) {
        var grade = parseInt($(obj).find("option:selected").attr("data-grade"));
        $('#grade').val(grade + 1);
        var dirpath = $(obj).find("option:selected").attr("data-dirpath");
        $('#dirpath').val(dirpath);

        if (-1 == grade) {
            $('#batch_list_0').show();
            $('#thead_header_0').show();
            $('#batch_list_1').hide();
            $('#thead_header_1').hide();
        } else {
            $('#batch_list_0').hide();
            $('#thead_header_0').hide();
            $('#batch_list_1').show();
            $('#thead_header_1').show();
        }
    }

    function check_submit(){
        if (0 < $('input[name=grade]').val()) {
            var is_empty = true;
            $('input[name^=reltype]').each(function(index, item){
                if ($.trim($(item).val())) {
                    is_empty = false;
                }
            });
            if (is_empty) {
                showErrorMsg('至少填写一个栏目名称！');
                $($('input[name^=reltype]').get(0)).focus();
                return false;
            }
        } else {
            var is_empty = true;
            $('input[name^=toptype]').each(function(index, item){
                if ($.trim($(item).val())) {
                    is_empty = false;
                }
            });
            if (is_empty) {
                showErrorMsg('至少填写一个顶级栏目名称！');
                $($('input[name^=toptype]').get(0)).focus();
                return false;
            }
        }

        if($('#templist').val() == ''){
            $('.tab-base').find('.tab').each(function(){
                $(this).removeClass('current');
            });
            $($('.tab-base').find('.tab').get(1)).addClass('current');
            $('.tab_div_1').hide();
            $('.tab_div_2').show();
            showErrorMsg('请选择列表模板');
            $('#templist').focus();
            return false;
        }

        var channel = parseInt($('#current_channel').find('option:selected').val());
        var js_allow_channel_arr = {$js_allow_channel_arr};
        if($('#tempview').val() == '' && $.inArray(channel, js_allow_channel_arr) != -1){
            $('.tab-base').find('.tab').each(function(){
                $(this).removeClass('current');
            });
            $($('.tab-base').find('.tab').get(1)).addClass('current');
            $('.tab_div_1').hide();
            $('.tab_div_2').show();
            showErrorMsg('请选择文档模板');
            $('#tempview').focus();
            return false;
        }

        $('#post_form').submit();
    }

    function newtpl(type)
    {
        $.ajax({
            url: "{:url('Security/ajax_security_ask_open', ['_ajax'=>1])}",
            type: 'GET',
            dataType: 'JSON',
            data: {},
            success: function(res){
                var security_ask_open = res.data.security_ask_open;
                if (0 == security_ask_open) {
                    showConfirm('需要设置密保问题验证才可以继续', {btn:['去设置', '取消']}, function(){
                        layer.closeAll();
                        var iframes = layer.open({
                            type: 2,
                            title: '安全验证中心',
                            fixed: true, //不固定
                            shadeClose: false,
                            shade: layer_shade,
                            offset: 'auto',
                            // maxmin: true, //开启最大化最小化按钮
                            area: ['100%', '100%'],
                            content: "{:url('Security/second_ask_init')}"+"&source=arctype_"+type,
                            success: function(layero, index){

                            }
                        });
                        layer.full(iframes);
                    });
                    return false;
                }

                var nid = $('#current_channel').find('option:selected').attr('data-nid');
                var url = "{:url('Arctype/ajax_newtpl')}";
                if (url.indexOf('?') > -1) {
                    url += '&';
                } else {
                    url += '?';
                }
                url += 'type='+type+'&nid='+nid;

                if ('lists' == type) {
                    var title = '新建列表模板';
                } else {
                    var title = '新建文档模板';
                }
                //iframe窗
                layer.open({
                    type: 2,
                    title: title,
                    fixed: true, //不固定
                    shadeClose: false,
                    shade: layer_shade,
                    closeBtn: 3,
                    maxmin: true, //开启最大化最小化按钮
                    area: ['90%', '90%'],
                    content: url
                });
            },
            error: function(e){
                showErrorMsg(e.responseText);
                return false;
            }
        });
    }

    function batch_add_tr(type)
    {
        $('#batch_list_0').hide();
        $('#thead_header_0').hide();
        $('#batch_list_1').hide();
        $('#thead_header_1').hide();
        $('#batch_list_'+type).show();
        $('#thead_header_'+type).show();

        var tr = $($('#batch_list_'+type+' tr').get(0));
        var id = parseInt($('#batch_list_'+type+' tr').length) + 1;
        var html = "<tr id='tr_"+type+"_"+id+"'>"+$(tr).html()+"</tr>";
        $('#append_tr').html(html);
        $('#append_tr').find('.btn_del_'+type).html('<a class="btn red" href="javascript:void(0);" onclick="$(\'#tr_'+type+'_'+id+'\').remove();"><i class="fa fa-trash-o"></i>删除</a>');

        var htmlnew = $('#append_tr').html();
        $('#batch_list_'+type).append(htmlnew);
    }
</script>

{include file="public/footer" /}