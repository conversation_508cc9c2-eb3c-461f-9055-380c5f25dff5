{include file="public/layout" /}

<body class="bodystyle" style="overflow-y: scroll; cursor: default; -moz-user-select: inherit;">
<div id="append_parent"></div>
<div id="ajaxwaitid"></div>
<div class="page" style="min-width: auto;">
    {include file="ask/bar" /}
    <div class="flexigrid">
        <div class="hDiv">
            <div class="hDivBox">
                <table cellspacing="0" cellpadding="0" style="width: 100%">
                    <thead>
                    <tr>
                        <th abbr="ac_id" axis="col4">
                            <div style="text-align: left; padding-left: 10px;" class="">头衔名称</div>
                        </th>
                        <th abbr="article_time" class="w190">
                            <div class="tc">积分大于</div>
                        </th>
                        <th abbr="article_time" class="w60">
                            <div class="tc">操作</div>
                        </th>
                    </tr>
                    </thead>
                </table>
            </div>
        </div>
        <div class="bDiv" style="height: auto;">
            <form id="PostForm">
                <div id="flexigrid" cellpadding="0" cellspacing="0" border="0">
                    <table style="width: 100%">
                        <tbody id='TbodyTemplate'>
                        {empty name="list"}
                            <tr>
                                <td class="no-data" align="center" axis="col0" colspan="50">
                                    <div class="no_row">
                                        <div class="no_pic"><img src="__SKIN__/images/null-data.png"></div>
                                    </div>
                                </td>
                            </tr>
                        {else/}
                            {volist name="list" id="vo"}
                            <tr class="tr">
                                <td style="width: 100%">
                                    <div class="tl" style="padding-left: 10px;">
                                        <input type="hidden" name="id[]" value="{$vo.id}">
                                        <input type="text" name="name[]" value="{$vo.name}">
                                    </div>
                                </td>
                                <td class="">
                                    <div class="w190 tc">
                                        <input type="text" name="min[]" value="{$vo.min}" style="width: 50px;">
                                    </div>
                                </td>
                                <td class="operation">
                                    <div class="w60 tc">
                                        {eq name="$Think.const.CONTROLLER_NAME.'@score_level_del'|is_check_access" value="1"}
                                        <a class="btn red" href="javascript:void(0);" data-url="{:url('Ask/score_level_del')}" onclick="FindDelData(this, '{$vo.id}');">删除</a>
                                        {/eq}
                                    </div>
                                </td>
                            </tr>
                            {/volist}
                        {/empty}
                        </tbody>
                    </table>
                    <div id='Template'></div>
                </div>
            </form>
            <div class="iDiv" style="display: none;"></div>
        </div>
        <div class="footer-oper">
            <div class="fbutton">
                <a href="javascript:void(0);" data-url="{:url('Ask/score_level')}" onclick="AddAskData(this);" class="layui-btn layui-btn-primary">
                    <span class="">保存</span>
                </a>
            </div>
            <div class="fbutton">
                <a onclick="AskType(this);" class="layui-btn layui-btn-primary">
                    <span class="red">新增头衔</span>
                </a>
            </div>
        </div>
        <div style="clear:both"></div>
    </div>
</div>
<input type="hidden" id="IsEmpty" value="{$IsEmpty}">
<script type="text/javascript">
    // tr数,取唯一标识
    var SerialNum = $('#TbodyTemplate .tr').length;
    function AskType(){
        var AddHtml = [];
        AddHtml += 
        [
            '<tr class="tr" id="tr_'+SerialNum+'">'+
                '<td style="width: 100%">'+
                    '<div class="tl" style="padding-left: 10px;">'+
                        '<input type="hidden" name="id[]" >'+
                        '<input type="text" name="name[]" >'+
                    '</div>'+
                '</td>'+

                '<td class="">'+
                    '<div class="w190 tc">'+
                        '<input type="text" name="min[]" style="width: 50px;">\n'+
                    '</div>'+
                '</td>'+

                '<td class="operation">'+
                    '<div class="w60 tc">'+
                        '<a class="btn red" href="javascript:void(0);" data-id="tr_'+SerialNum+'" onclick="DelHtml(this)">删除</a>'+
                    '</div>'+
                '</td>'+
            '</tr>'
        ];

        /*加载方式*/
        if (1 == $('#IsEmpty').val()) {
            // 已有栏目数据则执行追加
            $('#Template').append(AddHtml);
        }else{
            if (0 == SerialNum) {
                // 没有栏目则执行覆盖
                $('#TbodyTemplate').html(AddHtml);
            }else{
                // 已新增过一次则执行追加
                $('#TbodyTemplate').append(AddHtml);
            }
        }
        /* END */

        SerialNum++;
    }

    // 删除未保存的级别
    function DelHtml(obj){
        $('#'+$(obj).attr('data-id')).remove();
    }

    // 保存数据
    function AddAskData(obj){
        if ($('input[name*=name]').length == 0) {
            showErrorAlert('至少新增一个级别！');
            return false;
        } else {
            var is_empty = true;
            $('input[name*=name]').each(function(index, item){
                if ($.trim($(item).val()) != '') {
                    is_empty = false;
                    return false;
                }
            });
            if (true == is_empty) {
                showErrorAlert('级别名称不能为空！');
                return false;
            }
        }

        layer_loading('正在处理');
        $.ajax({
            type : 'post',
            url : $(obj).attr('data-url'),
            data : $('#PostForm').serialize(),
            dataType : 'json',
            success : function(res){
                layer.closeAll();
                if(res.code == 1){
                    layer.msg(res.msg, {icon: 1, time:1000},function(){
                        window.location.reload();
                    });
                }else{
                    showErrorAlert(res.msg);
                }
            },
            error : function(e) {
                layer.closeAll();
                showErrorAlert(e.responseText);
            }
        })
    }

    /**
     * 单个删除
     */
    function FindDelData(obj, del_id) {
        layer.confirm('此操作不可恢复，确认删除？', {
            area: ['480px', '190px'],
            move: false,
            title: '提示',
            shade: layer_shade,
            btnAlign:'r',
            closeBtn: 3,
            btn: ['确定','取消'] ,//按钮
            success: function () {
                  $(".layui-layer-content").css('text-align', 'left');
              }
        }, function () {
            layer_loading('正在处理');
            $.ajax({
                url: $(obj).data('url'),
                type: 'post',
                dataType: 'json',
                data:{del_id:del_id,_ajax:1},
                success: function(res){
                    layer.closeAll();
                    if (1 == res.code) {
                        layer.msg(res.msg, {time: 1000},function(){
                            window.location.reload();
                        });
                    } else {
                        showErrorAlert(res.msg);
                    }
                },
                error : function(e) {
                    layer.closeAll();
                    showErrorAlert(e.responseText);
                }
            });
        }, function (index) {
            layer.closeAll(index);
        });
    }
</script>
{include file="public/footer" /}

