{include file="public/layout" /}
<script src="__SKIN__/js/users_upgrade.js?v={$version}"></script>
<script type="text/javascript" src="__PUBLIC__/plugins/colpick/js/colpick.js"></script>
<link href="__PUBLIC__/plugins/colpick/css/colpick.css" rel="stylesheet" type="text/css"/>
<body class="bodystyle" style="overflow-y: scroll; cursor: default; -moz-user-select: inherit;">
<style type="text/css">
#web_theme_color,#web_assist_color {
    /*margin:0;*/
    /*padding:0;*/
    border:solid 1px #ccc;
    width:70px;
    height:20px;
    border-right:40px solid green;
    /*line-height:20px;*/
} 
</style>
<div id="append_parent"></div>
<div id="ajaxwaitid"></div>
<div class="page">
    <div class="flexigrid">
        <form class="form-horizontal" id="postForm" action="{:url('Encodes/theme_conf')}" method="post">
            <div class="ncap-form-default">
                <dl class="row">
                    <dt class="tit">
                        <label for="uname">主题色选择</label>
                    </dt>
                    <dd class="opt">
                        <div class="theme-color">
                            <input type="hidden" name="web[web_theme_color_model]" value="{$globalTpCache.web_theme_color_model|default='1'}" id="web_theme_color_model"/>
                            <ul>
                                <li {if condition="empty($globalTpCache['web_theme_color_model']) || $globalTpCache['web_theme_color_model'] == 1"} class="active" {/if} data-model="1">
                                    <span class="theme_color" style="background-color: #3398cc;"></span>
                                    <span class="assist_color" style="background-color: #2189be;"></span>
                                </li>
                                <li {if condition="$globalTpCache['web_theme_color_model'] == 2"} class="active" {/if} data-model="2">
                                    <span class="theme_color" style="background-color: #1e9fff;"></span>
                                    <span class="assist_color" style="background-color: #0069b7;"></span>
                                </li>
                                <li {if condition="$globalTpCache['web_theme_color_model'] == 3"} class="active" {/if} data-model="3">
                                    <span class="theme_color" style="background-color: #1aa094;"></span>
                                    <span class="assist_color" style="background-color: #197971;"></span>
                                </li>
                                <li {if condition="$globalTpCache['web_theme_color_model'] == 4"} class="active" {/if} data-model="4">
                                    <span class="theme_color" style="background-color: #e82121;"></span>
                                    <span class="assist_color" style="background-color: #ae1919;"></span>
                                </li>
                                <li {if condition="$globalTpCache['web_theme_color_model'] == 5"} class="active" {/if} data-model="5">
                                    <span class="theme_color" style="background-color:#197971;"></span>
                                    <span class="assist_color" style="background-color: #fa921b;"></span>
                                </li>
                                <li {if condition="$globalTpCache['web_theme_color_model'] == 6"} class="active" {/if} data-model="6">
                                    <span class="theme_color" style="background-color:#963885;"></span>
                                    <span class="assist_color" style="background-color: #772c6a;"></span>
                                </li>
                                <li {if condition="$globalTpCache['web_theme_color_model'] == 'custom'"} class="active" {/if} data-model="custom">
                                    <span style="background-color:#ffffff;">
                                        <img src="__STATIC__/admin/images/empty-color.png?v={$version|default='v1.4.7'}">
                                    </span>
                                    <span style="background-color:#ffffff;">
                                        <img src="__STATIC__/admin/images/empty-color.png?v={$version|default='v1.4.7'}">
                                    </span>
                                </li>
                            </ul>
                        </div>
                    </dd>
                </dl>
                <div id="div_custom_theme_color" class="{if condition="$globalTpCache['web_theme_color_model'] != 'custom'"} none {/if}">
                    <dl class="row">
                        <dt class="tit">
                            <label for="uname">自定义主色</label>
                        </dt>
                        <dd class="opt">
                            <input type="text" name="web[web_theme_color]" value="{$globalTpCache.web_theme_color|default='#3398cc'}" id="web_theme_color" style="border-color: {$globalTpCache.web_theme_color|default='#3398cc'};" />
                        </dd>
                    </dl>
                    <dl class="row">
                        <dt class="tit">
                            <label for="uname">自定义辅色</label>
                        </dt>
                        <dd class="opt">
                            <input type="text" name="web[web_assist_color]" value="{$globalTpCache.web_assist_color|default='#2189be'}" id="web_assist_color" style="border-color: {$globalTpCache.web_assist_color|default='#2189be'};" />
                        </dd>
                    </dl>
                </div>
                {eq name='$main_lang' value='$admin_lang'}
                <dl class="row">
                    <dt class="tit">
                        <label for="web_adminlogo">后台LOGO</label>
                    </dt>
                    <dd class="opt">
                        <div class="input-file-show">
                            <span class="show">
                                <a id="img_a_web_adminlogo" class="nyroModal" rel="gal" href="{$globalTpCache.web_adminlogo|default='javascript:void(0);'}?t={php}echo getTime();{/php}" target="_blank">
                                    <i id="img_i_web_adminlogo" class="fa fa-picture-o" {notempty name="$globalTpCache.web_adminlogo"}onmouseover="layer_tips=layer.tips('<img src={$globalTpCache.web_adminlogo|default=''} class=\'layer_tips_img\'>',this,{tips: [1, '#fff']});"{/notempty} onmouseout="layer.close(layer_tips);"></i>
                                </a>
                            </span>
                            <span class="type-file-box">
                                <input type="text" id="web_adminlogo" name="web[web_adminlogo]" value="{$globalTpCache.web_adminlogo|default=''}" class="type-file-text" autocomplete="off">
                                <input type="button" name="button" id="button1" value="选择上传..." class="type-file-button">
                                <input class="type-file-file" onClick="GetUploadify(1,'','adminlogo','adminlogo_call_back','{:url('Uploadify/upload_full')}');" size="30" hidefocus="true" nc_type="change_site_logo" title="点击前方预览图可查看大图，点击按钮选择文件并提交表单后上传生效">
                            </span>
                        </div>
                        <span class="err"></span>
                        <p class="notic"></p>
                    </dd>
                </dl>
                <dl class="row">
                    <dt class="tit">
                        <label for="web_loginlogo">登录LOGO</label>
                    </dt>
                    <dd class="opt">
                        <div class="input-file-show">
                            <span class="show">
                                <a id="img_a_web_loginlogo" class="nyroModal" rel="gal" href="{$globalTpCache.web_loginlogo|default='javascript:void(0);'}?t={php}echo getTime();{/php}" target="_blank">
                                    <i id="img_i_web_loginlogo" class="fa fa-picture-o" {notempty name="$globalTpCache.web_loginlogo"}onmouseover="layer_tips=layer.tips('<img src={$globalTpCache.web_loginlogo|default=''} class=\'layer_tips_img\'>',this,{tips: [1, '#fff']});"{/notempty} onmouseout="layer.close(layer_tips);"></i>
                                </a>
                            </span>
                            <span class="type-file-box">
                                <input type="text" id="web_loginlogo" name="web[web_loginlogo]" value="{$globalTpCache.web_loginlogo|default=''}" class="type-file-text" autocomplete="off">
                                <input type="button" name="button" id="button1" value="选择上传..." class="type-file-button">
                                <input class="type-file-file" onClick="GetUploadify(1,'','loginlogo','loginlogo_call_back','{:url('Uploadify/upload_full')}');" size="30" hidefocus="true" nc_type="change_site_logo" title="点击前方预览图可查看大图，点击按钮选择文件并提交表单后上传生效">
                            </span>
                        </div>
                        <span class="err"></span>
                        <p class="notic"></p>
                    </dd>
                </dl>
                
                <dl class="row">
                    <dt class="tit">
                        <label for="web_loginbgimg">登录背景选择</label>
                    </dt>
                    <dd class="opt">
                        <input type="hidden" name="web[web_loginbgimg_model]" value="{$globalTpCache.web_loginbgimg_model|default='1'}" id="web_loginbgimg_model"/>
                        <div class="theme_bg">
                            <ul>
                                <li {if condition="$globalTpCache['web_loginbgimg_model'] == 1"} class="active" {/if} data-model="1"><img src="__SKIN__/loginbg/login-bg-1.png"></li>
                                <li {if condition="$globalTpCache['web_loginbgimg_model'] == 2"} class="active" {/if} data-model="2"><img src="__SKIN__/loginbg/login-bg-2.png"></li>
                                {if condition="$php_servicemeal >= 2"}
                                <li {if condition="$globalTpCache['web_loginbgimg_model'] == 3"} class="active" {/if} data-model="3"><img src="__SKIN__/loginbg/login-bg-3.png"></li>
                                {/if}
                                <li {if condition="$globalTpCache['web_loginbgimg_model'] == 'custom'"} class="active" {/if} data-model="custom"><img src="__SKIN__/images/login-bg-empty.png" title="自定义背景"></li>
                            </ul>
                        </div>
                    </dd>
                </dl>
                <dl class="row" id="div_custom_loginbgimg" {if condition="$globalTpCache['web_loginbgimg_model'] != 'custom'"} style="display: none;" {/if}>
                    <dt class="tit">
                        <label for="web_loginbgimg">&nbsp;</label>
                    </dt>
                    <dd class="opt">
                        <div class="input-file-show">
                            <span class="show">
                                <a id="img_a_web_loginbgimg" class="nyroModal" rel="gal" href="{$globalTpCache.web_loginbgimg|default='javascript:void(0);'}" target="_blank">
                                    <i id="img_i_web_loginbgimg" class="fa fa-picture-o" {notempty name="$globalTpCache.web_loginbgimg"}onmouseover="layer_tips=layer.tips('<img src={$globalTpCache.web_loginbgimg|default=''} class=\'layer_tips_img\'>',this,{tips: [1, '#fff']});"{/notempty} onmouseout="layer.close(layer_tips);"></i>
                                </a>
                            </span>
                            <span class="type-file-box">
                                <input type="text" id="web_loginbgimg" name="web[web_loginbgimg]" value="{$globalTpCache.web_loginbgimg|default=''}" class="type-file-text" autocomplete="off">
                                <input type="button" name="button" id="button1" value="选择上传..." class="type-file-button">
                                <input class="type-file-file" onClick="GetUploadify(1,'','loginbgimg','loginbgimg_call_back','{:url('Uploadify/upload_full')}');" size="30" hidefocus="true" nc_type="change_site_logo" title="点击前方预览图可查看大图，点击按钮选择文件并提交表单后上传生效">
                            </span>
                        </div>
                        <span class="err"></span>
                        <p class="notic"></p>
                    </dd>
                </dl>
                {/eq}
                <dl class="row">
                    <div class="bot">
                        <a href="JavaScript:void(0);" onclick="checkSubmit();" class="ncap-btn-big ncap-btn-green" id="submitBtn">确认提交</a>
                    </div>
                </dl>
            </div>
        </form>
    </div>
</div>
<script>
    var parentObj = parent.layer.getFrameIndex(window.name); //先得到当前iframe层的索引

    $(document).ready(function(){

        // 主题色切换
        $('.theme-color').find('li').click(function(){
            $('.theme-color').find('li').removeClass('active');
            $(this).addClass('active');
            var data_model = $(this).attr('data-model');
            $('#web_theme_color_model').val(data_model);
            if ('custom' == data_model) {
                $('#div_custom_theme_color').show();
            } else {
                $('#div_custom_theme_color').hide();
                // 主题色
                var theme_color = $(this).find('span.theme_color').css('background-color');
                theme_color = theme_color.match(/^rgb\((\d+),\s*(\d+),\s*(\d+)\)$/);
                theme_color= "#" + hex(theme_color[1]) + hex(theme_color[2]) + hex(theme_color[3]);
                $('#web_theme_color').val(theme_color);
                // 辅助色
                var assist_color = $(this).find('span.assist_color').css('background-color');
                assist_color = assist_color.match(/^rgb\((\d+),\s*(\d+),\s*(\d+)\)$/);
                assist_color= "#" + hex(assist_color[1]) + hex(assist_color[2]) + hex(assist_color[3]);
                $('#web_assist_color').val(assist_color);
            }
        });

        // 10进制转为16进制
        function hex(x) {
            return ("0" + parseInt(x).toString(16)).slice(-2);
        }

        // 登录背景图切换
        $('.theme_bg').find('li').click(function(){
            $('.theme_bg').find('li').removeClass('active');
            $(this).addClass('active');
            var data_model = $(this).attr('data-model');
            $('#web_loginbgimg_model').val(data_model);
            if ('custom' == data_model) {
                $('#div_custom_loginbgimg').show();
            } else {
                $('#div_custom_loginbgimg').hide();
                // 登录背景图
                var loginbgimg = $(this).find('img').attr('src');
                $('#web_loginbgimg').val(loginbgimg);
            }
        });

        // 自定义 - 主题颜色选择
        $('#web_theme_color').colpick({
            flat:false,
            layout:'rgbhex',
            submit:0,
            colorScheme:'light',
            color:$('#web_theme_color').val(),
            onChange:function(hsb,hex,rgb,el,bySetColor) {
                $(el).css('border-color','#'+hex);
                // Fill the text box just if the color was set using the picker, and not the colpickSetColor function.
                if(!bySetColor) $(el).val('#'+hex);
            }
        }).keyup(function(){
            $(this).colpickSetColor('#'+this.value);
        });

        // 自定义 - 辅助颜色选择
        $('#web_assist_color').colpick({
            flat:false,
            layout:'rgbhex',
            submit:0,
            colorScheme:'light',
            color:$('#web_assist_color').val(),
            onChange:function(hsb,hex,rgb,el,bySetColor) {
                $(el).css('border-color','#'+hex);
                // Fill the text box just if the color was set using the picker, and not the colpickSetColor function.
                if(!bySetColor) $(el).val('#'+hex);
            }
        }).keyup(function(){
            $(this).colpickSetColor('#'+this.value);
        });
    });

    function checkSubmit(){
        layer_loading('正在处理');
        $.ajax({
            type : 'post',
            url : "{:url('Encodes/theme_conf', ['_ajax'=>1])}",
            data : $('#postForm').serialize(),
            dataType : 'json',
            success : function(res){
                layer.closeAll();
                if(res.code == 1){
                    var _parent = parent;
                    _parent.layer.close(parentObj);
                    _parent.layer.msg(res.msg, {shade: layer_shade, time: 1000}, function(){
                        _parent.top.window.location.reload();
                    });
                }else{
                    showErrorMsg(res.msg);
                }
            },
            error: function(e){
                layer.closeAll();
                layer.alert(e.responseText, {icon: 5, title:false});
            }
        });
    }

    function adminlogo_call_back(fileurl_tmp)
    {
        $("#web_adminlogo").val(fileurl_tmp);
        $("#img_a_web_adminlogo").attr('href', fileurl_tmp);
        $("#img_i_web_adminlogo").attr('onmouseover', "layer_tips=layer.tips('<img src="+fileurl_tmp+" class=\\'layer_tips_img\\'>',this,{tips: [1, '#fff']});");
    }

    function loginlogo_call_back(fileurl_tmp)
    {
        $("#web_loginlogo").val(fileurl_tmp);
        $("#img_a_web_loginlogo").attr('href', fileurl_tmp);
        $("#img_i_web_loginlogo").attr('onmouseover', "layer_tips=layer.tips('<img src="+fileurl_tmp+" class=\\'layer_tips_img\\'>',this,{tips: [1, '#fff']});");
    }

    function loginbgimg_call_back(fileurl_tmp)
    {
        $("#web_loginbgimg").val(fileurl_tmp);
        $("#img_a_web_loginbgimg").attr('href', fileurl_tmp);
        $("#img_i_web_loginbgimg").attr('onmouseover', "layer_tips=layer.tips('<img src="+fileurl_tmp+" class=\\'layer_tips_img\\'>',this,{tips: [1, '#fff']});");
    }
</script>

{include file="public/footer" /}