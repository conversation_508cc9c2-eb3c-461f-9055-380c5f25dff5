{include file="public/layout" /}
<body style="background-color: rgb(255, 255, 255); overflow: auto; cursor: default; -moz-user-select: inherit;min-width: auto;">
<div id="append_parent"></div>
<div id="ajaxwaitid"></div>
<div class="page" style="min-width: auto;box-shadow:none; padding-bottom: 0px;">
    <!-- 操作说明 -->
    <div id="explanation" class="explanation">
        <div id="checkZoom" class="title"><i class="fa fa-lightbulb-o"></i>
            <h4 title="提示相关设置操作时应注意的要点">提示</h4>
            <span title="收起提示" id="explanationZoom" style="display: block;"></span>
        </div>
        <ul>
            <li style="color: red;">注意：导入区域会自动过滤掉同级别的区域，保留原区域。</li>
        </ul>
    </div>
    <form class="form-horizontal" id="post_form" method="POST" action="{:url('Citysite/import_city')}" onsubmit="check_submit();">
        <div class="ncap-form-default">
            <dl class="row">
                <dt class="tit">
                    <label for="">导入区域</label>
                </dt>
                <dd class="opt" style="width: auto;">
                    <select name="province_id" id="province_id" style="margin:0px 5px;" onchange="set_city_list(0);">
                        <option value="0">全国</option>
                        {volist name='$province_list' id='vo'}
                        <option value="{$vo.id}">{$vo.name}</option>
                        {/volist}
                    </select>
                    <select name="city_id" id="city_id" style="margin:0px 5px;display: none;">
                        <option value="">--请选择--</option>
                    </select>
                    <span class="err"></span>
                    <p class="notic"></p>
                </dd>
            </dl>
            <dl class="row">
                <dt class="tit">
                    <label for="is_open">二级域名</label>
                </dt>
                <dd class="opt" style="width: auto;">
                    <label class="curpoin"><input type="radio" name="is_open" value="1">开启</label>&nbsp;&nbsp;
                    <label class="curpoin"><input type="radio" name="is_open" value="0" checked="checked">关闭</label>
                    <span class="err"></span>
                    <p class="notic2 none" id="is_open_tips">开启之前，建议先做好二级域名解析及绑定，避免访问不了</p>
                </dd>
            </dl>
            <dl class="row">
                <dt class="tit">
                    <label for="showall">主站文档</label>
                </dt>
                <dd class="opt" style="width: auto;">
                    <label class="curpoin"><input type="radio" name="showall" value="1" checked="checked">显示</label>&nbsp;&nbsp;
                    <label class="curpoin"><input type="radio" name="showall" value="0">隐藏</label>
                    <span class="err"></span>
                    <p class="notic">列表页面是否显示主站（所属区域为“全国”）的文档</p>
                </dd>
            </dl>
            <dl class="row">
                <dt class="tit">
                    <label for="sort_order">SEO设置</label>
                </dt>
                <dd class="opt" style="width: auto;">
                    <label class="curpoin"><input type="radio" name="seoset" value="1">自定义</label>&nbsp;&nbsp;
                    <label class="curpoin"><input type="radio" name="seoset" value="0" checked="checked">引用系统默认</label>
                    <span class="err"></span>
                    <p class="notic">系统默认分站SEO在功能配置里统一填写</p>
                </dd>
            </dl>
            <div class="none" id="div_seoset_html">
                <dl class="row">
                    <dt class="tit">
                        <label for="seo_title">SEO标题</label>
                    </dt>
                    <dd class="opt" style="width: auto;">
                        <input type="text" value="" name="seo_title" id="seo_title" class="input-txt" autocomplete="off">
                        <p class="notic"></p>
                    </dd>
                </dl>
                <dl class="row">
                    <dt class="tit">
                        <label>SEO关键词</label>
                    </dt>
                    <dd class="opt" style="width: auto;">          
                        <textarea rows="5" cols="60" id="seo_keywords" name="seo_keywords" style="height:20px;" autocomplete="off"></textarea>
                        <span class="err"></span>
                        <p class="notic"></p>
                    </dd>
                </dl>
                <dl class="row">
                    <dt class="tit">
                        <label>SEO描述</label>
                    </dt>
                    <dd class="opt" style="width: auto;">          
                        <textarea rows="5" cols="60" id="seo_description" name="seo_description" style="height:60px;" autocomplete="off"></textarea>
                        <span class="err"></span>
                        <p class="notic"></p>
                        <p class="notic2">{region}代表区域名称</p>
                    </dd>
                </dl>
            </div>
            <dl class="row">
                <dt class="tit">
                    <label for="status">区域状态</label>
                </dt>
                <dd class="opt" style="width: auto;">
                    <label class="curpoin"><input type="radio" name="status" value="1">启用</label>&nbsp;&nbsp;
                    <label class="curpoin"><input type="radio" name="status" value="0" checked="checked">禁用</label>
                    <p class="notic"></p>
                </dd>
            </dl>
            <div class="bot" style="padding-bottom:0px;">
                <input type="hidden" name="_ajax" value="1">
                <a href="JavaScript:void(0);" onclick="check_submit();" class="ncap-btn-big ncap-btn-green" id="submitBtn">确认提交</a>
            </div>
        </div>
    </form>
</div>
<script type="text/javascript">
    var parentObj = parent.layer.getFrameIndex(window.name); //先得到当前iframe层的索引

    $(function () {
        $('input[name=seoset]').click(function(){
            var seoset = $(this).val();
            if (0 == seoset) {
                $('#div_seoset_html').hide();
            } else {
                $('#div_seoset_html').show();
            }
        });

        $('input[name=is_open]').click(function(){
            var is_open = $(this).val();
            if (1 == is_open) {
                $('#is_open_tips').show();
            } else {
                $('#is_open_tips').hide();
            }
        });
    });

    function check_submit()
    {
        var province_id = $('#province_id').val();
        if (province_id > 0) {
            layer_loading('正在处理');
            $.ajax({
                url: "{:url('Citysite/import_city')}",
                type: 'POST',
                dataType: 'JSON',
                data: $('#post_form').serialize(),
                success: function(res){
                    layer.closeAll();
                    if (res.code == 1) {
                        parent.layer.msg(res.msg, {shade: layer_shade, time: 1000}, function(){
                            parent.window.location.reload();
                            parent.layer.close(parentObj);
                        });
                    } else {
                        showErrorMsg(res.msg);
                    }
                    return false;
                },
                error: function(e){
                    layer.closeAll();
                    showErrorAlert(e.responseText);
                    return false;
                }
            });
        } else {
            layer_loading('<font id="allcity_progress">正在处理</font>');
            callback_single_handle(1);
        }
    }

    function callback_single_handle(province_id)
    {
        var url = "{:url('Citysite/import_city')}";
        url += "&cur_province_id="+province_id;
        $.ajax({
            url: url,
            type: 'POST',
            dataType: 'JSON',
            data: $('#post_form').serialize(),
            success: function(res){
                if (res.code == 1) {
                    if (res.data.next_province_id == 0) {
                        $('#allcity_progress').html('进度'+res.data.progress+'%');
                        var timer = setTimeout(function(){
                            clearTimeout(timer); // 清理定时任务
                            layer.closeAll();
                            parent.layer.msg(res.msg, {shade: layer_shade, time: 1000}, function(){
                                parent.window.location.reload();
                                parent.layer.close(parentObj);
                            });
                        }, 1000);
                    } else {
                        $('#allcity_progress').html('进度'+res.data.progress+'%');
                        callback_single_handle(res.data.next_province_id);
                    }
                } else {
                    layer.closeAll();
                    showErrorAlert(res.msg);
                }
                return false;
            },
            error: function(e){
                layer.closeAll();
                showErrorAlert(e.responseText);
                return false;
            }
        });
    }

    //获取二级城市列表
    function set_city_list(cityid, siteid) {
        var pid =  $("#province_id").val();
        $.ajax({
            url: eyou_basefile + "?m=admin&c=Citysite&a=ajax_get_region_list&_ajax=1",
            type: 'POST',
            dataType: 'JSON',
            async: false,
            data: {pid:pid, level:2},
            success: function(res){
                if (res.code === 1){
                    if (1 == res.data.isempty) {
                        $("#city_id").hide();
                    } else {
                        $("#city_id").show();
                    }
                    $("#city_id").empty();
                    $("#city_id").prepend(res.msg);
                    if (cityid > 0) {
                        $("#city_id").val(cityid);
                    }
                }
            },
            error: function(e){
                showErrorMsg(e.responseText);
                return false;
            }
        });
    }
</script>
{include file="public/footer" /}