{include file="public/layout" /}

<link rel="stylesheet" type="text/css" href="__SKIN__/js/codemirror/codemirror.css?v={$version}">
<script type="text/javascript" src="__SKIN__/js/codemirror/codemirror.js?v={$version}"></script>
<script type="text/javascript" src="__SKIN__/js/codemirror/mode/xml/xml.js?v={$version}"></script>
<script type="text/javascript" src="__SKIN__/js/codemirror/mode/javascript/javascript.js?v={$version}"></script>
<script type="text/javascript" src="__SKIN__/js/codemirror/mode/css/css.js?v={$version}"></script>
<script type="text/javascript" src="__SKIN__/js/codemirror/mode/php/php.js?v={$version}"></script>
<script type="text/javascript" src="__SKIN__/js/codemirror/mode/clike/clike.js?v={$version}"></script>
<script type="text/javascript" src="__SKIN__/js/codemirror/mode/htmlmixed/htmlmixed.js?v={$version}"></script>

<body style="background-color: #FFF; overflow: auto;min-width:auto;">
<div id="toolTipLayer" style="position: absolute; z-index: 9999; display: none; visibility: visible; left: 95px; top: 573px;"></div>
<div id="append_parent"></div>
<div id="ajaxwaitid"></div>
<div class="page" style="min-width:auto;box-shadow:none;">
    <form class="form-horizontal" id="post_form" action="{:url('Ajax/new_appoint_tplfile')}" method="post">
        <div class="ncap-form-default">
            <dl class="row">
                <dt class="tit">模板目录</dt>
                <dd class="opt">
                    {$tpldirpath}
                    <span class="err"></span>
                    <p class="notic"></p>
                </dd>
            </dl>
            <dl class="row">
                <dt class="tit">
                    <label for="url"><em>*</em>文件名称</label>
                </dt>
                <dd class="opt">
                    {$type}_<input type="text" name="filename" value="" id="filename" placeholder="自定义" tabindex="2" onkeyup="this.value=this.value.replace(/[^\w\-]/g,'');" onpaste="this.value=this.value.replace(/[^\w\-]/g,'')" autocomplete="off">.htm
                    <span class="err"></span>
                    <p class="notic">只允许字母、数字、下划线、连接符的任意组合</p>
                    <p class="red" id="txt_filename"></p>
                </dd>
            </dl>
            <dl class="row">
                <dt class="tit">
                    <label for="content">HTML代码</label>
                </dt>
                <dd class="opt">
                    <textarea name='content' id="content" style='width:99%;height:500px;background:#ffffff;' tabindex="3"></textarea>
                    <span class="err"></span>
                    <p class="notic"></p>
                </dd>
            </dl>
            <div class="bot">
                <input type="hidden" id="type" name="type" value="{$type}" />
                <a href="JavaScript:void(0);" onclick="checkForm();" class="ncap-btn-big ncap-btn-green" id="submitBtn" tabindex="4">确认提交</a>
            </div>
        </div>
    </form>
</div>
<script type="text/javascript">
    var editor = '';

    $(function(){
      
        editor = CodeMirror.fromTextArea(document.getElementById('content'), {
            lineNumbers: true, // 显示行号
            lineWrapping: true, // 在行槽中添加行号显示器、折叠器、语法检测器`
            autofocus:true,  //自动聚焦
            mode: 'text/html'
        });
        editor.setSize('auto','500px');

        $('#filename').keyup(function(){
            $.ajax({
                type: "POST",
                url: "{:url('Ajax/new_appoint_tplfile', ['nosubmit'=>1,'_ajax'=>1])}",
                data: $('#post_form').serialize(),
                dataType: 'json',
                success: function (res) {
                    if(res.code == 1){
                        $('#txt_filename').html('');
                    } else {
                        $('#txt_filename').html(res.msg);
                    }
                }
            });
        });
    });

    // 判断输入框是否为空
    function checkForm(){
        if($('input[name=filename]').val() == ''){
            showErrorMsg('文件名称不能为空！');
            $('input[name=filename]').focus();
            return false;
        }
        var content = editor.getValue();
        if(content == ''){
            showErrorMsg('HTML代码不能为空！');
            $('#content').focus();
            return false;
        }
        layer_loading('正在处理');
        $.ajax({
            type: "POST",
            url: "{:url('Ajax/new_appoint_tplfile', ['_ajax'=>1])}",
            data: {filename:$('#filename').val(), content:content, type:$('#type').val()},
            dataType: 'json',
            success: function (res) {
                if(res.code == 1){
                    parent.layer.closeAll();
                    var id = res.data.select_input_id;
                    $('#'+id, window.parent.document).append('<option value="'+res.data.filename+'" selected="true">'+res.data.filename+'</option>');
                    parent.layer.msg(res.msg, {icon: 1, time: 1000});
                    return false;
                }else{
                    layer.closeAll();
                    if (res.data.focus) {
                        $('#'+res.data.focus).focus();
                    }
                    layer.msg(res.msg, {icon: 2});
                    return false;
                }
            },
            error:function(e){
                layer.closeAll();
                layer.alert(e.responseText, {icon: 2, title:false});
            }
        });
    }
</script>
{include file="public/footer" /}