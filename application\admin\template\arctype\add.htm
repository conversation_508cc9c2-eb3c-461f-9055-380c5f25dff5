{include file="public/layout" /}
<script type="text/javascript" src="__PUBLIC__/plugins/laydate/laydate.js"></script>

{eq name="$editor.editor_select" value="1"}
    {load href="__PUBLIC__/plugins/Ueditor/ueditor.config.js" /}
    {load href="__PUBLIC__/plugins/Ueditor/ueditor.all.min.js" /}
    {load href="__PUBLIC__/plugins/Ueditor/lang/zh-cn/zh-cn.js" /}
{else/}
    {load href="__PUBLIC__/plugins/ckeditor/ckeditor.js" /}
{/eq}

<body style="background-color: #FFF; overflow: auto;">
<div id="toolTipLayer" style="position: absolute; z-index: 9999; display: none; visibility: visible; left: 95px; top: 573px;"></div>
<div id="append_parent"></div>
<div id="ajaxwaitid"></div>
<div class="page" style="box-shadow:none;">
    <div class="fixed-bar">
        <div class="item-title">
            <a class="back_xin" href="javascript:history.back();" title="返回"><i class="iconfont e-fanhui"></i></a>
            <div class="subject">
                <h3>增加栏目</h3>
                <h5></h5>
            </div>
            <ul class="tab-base nc-row">
                <li><a href="javascript:void(0);" data-index='1' class="tab current"><span>常规选项</span></a></li>
                <li><a href="javascript:void(0);" data-index='2' class="tab"><span>高级选项</span></a></li>
            </ul>
        </div>
    </div>
    <form class="form-horizontal" id="post_form" action="{:url('Arctype/add')}" method="post">
        <!-- 常规选项 -->
        <div class="ncap-form-default tab_div_1">
            <dl class="row">
                <dt class="tit">
                    <label for="typename"><em>*</em>栏目名称</label>
                </dt>
                <dd class="opt">
                    <input type="text" value="" name="typename" id="typename" class="input-txt">
                    <p class="notic">保持唯一性，不可重复</p>
                </dd>
            </dl>
            <dl class="row {if $is_diyseo_htmlpath == 1} none {/if}" id="dl_dirname">
                <dt class="tit">
                    <label for="dirname">目录名称</label>
                </dt>
                <dd class="opt">
                    <input type="text" value="" name="dirname" id="dirname" class="input-txt" onkeyup="this.value=this.value.replace(/[^a-zA-Z0-9_-]/g,'');" onbeforepaste="clipboardData.setData('text',clipboardData.getData('text').replace(/\[^a-zA-Z0-9_-]/g,''));">
                    <p class="notic">用于伪静态和静态页面生成！</p>
                    <p class="">留空系统默认全拼音+随机数，仅支持字母、数字、下划线、连接符</p>
                </dd>
            </dl>
            <dl class="row">
                <dt class="tit">
                    <label for="current_channel">内容模型</label>
                </dt>
                <dd class="opt">
                    <select class="small form-control" id="current_channel" name="current_channel" onchange="ajax_get_template();">
                        {volist name="channeltype_list" id="vo"}
                        <option value="{$vo.id}" data-nid="{$vo.nid}" {eq name="$current_channel" value="$vo.id"}selected="true"{/eq}>{$vo.title}</option>
                        {/volist}
                    </select>
                    <span class="err"></span>
                    <p class="" id="notic_current_channel"></p>
                </dd>
            </dl>
            <dl class="row">
                <dt class="tit">
                    <label for="parent_id">所属栏目</label>
                </dt>
                <dd class="opt">
                    <select class="small form-control" id="parent_id" name="parent_id" onchange="set_grade(this);">
                        {$select_html}
                    </select>
                    <span class="err"></span>
                    <p class="notic">如果选择上级栏目，那么新增的栏目则为被选择上级栏目的子栏目</p>
                </dd>
            </dl>
            <!-- #weapp_UsersGroup_content# -->
            <dl class="row {if $is_diyseo_htmlpath == 0} none {/if}">
                <dt class="tit">
                    <label for="diy_dirpath">文件保存目录</label>
                </dt>
                <dd class="opt">
                    {$seo_html_arcdir}<input type="text" value="{$predirpath}" name="diy_dirpath" id="diy_dirpath" class="w350" onkeyup="this.value=this.value.replace(/[^0-9a-zA-Z\/-]/g,'');" onbeforepaste="clipboardData.setData('text',clipboardData.getData('text').replace(/[^0-9a-zA-Z\/-]/g,''));">
                    &nbsp;<a href="javascript:void(0);" onclick="get_dirpinyin(this);" class="ncap-btn ncap-btn-green">获取拼音</a>
                    <p class="notic">生成的文章HTML页面会存放在该目录下</p>
                </dd>
            </dl>
            <dl class="row none">
                <dt class="tit">
                    <label for="dirpath">文件保存目录</label>
                </dt>
                <dd class="opt">
                    {$seo_html_arcdir}<input type="text" value="{$predirpath}" name="dirpath" id="dirpath" class="w350" onkeyup="this.value=this.value.replace(/[^0-9a-zA-Z\/-]/g,'');" onbeforepaste="clipboardData.setData('text',clipboardData.getData('text').replace(/[^0-9a-zA-Z\/-]/g,''));">
                    <p class="notic">生成的文章HTML页面会存放在该目录下</p>
                </dd>
            </dl>
            <dl class="row">
                <dt class="tit">
                    <label>隐藏栏目</label>
                </dt>
                <dd class="opt">
                    <label class="curpoin"><input id="is_hidden1" name="is_hidden" value="1" type="radio">是</label>
                    &nbsp;
                    <label class="curpoin"><input id="is_hidden0" name="is_hidden" value="0" type="radio" checked="checked">否</label>
                    <p class="notic">隐藏之后，不显示在前台页面</p>
                </dd>
            </dl>
        </div>
        <!-- 常规选项 -->
        <!-- 高级选项 -->
        <div class="ncap-form-default tab_div_2" style="display:none;">
            <dl class="row" id="type_attr">
                <dt class="tit">
                    <label>外部链接</label>
                </dt>
                <dd class="opt">
                    <label class="curpoin"><input id="is_part1" name="is_part" value="1" type="radio">是</label>
                    &nbsp;
                    <label class="curpoin"><input id="is_part0" name="is_part" value="0" type="radio" checked="checked">否</label>
                    <p class="notic">外部连接（在"下方文本框"处填写网址）</p>
                </dd>
            </dl>
            <div class="none" id="row_url">
                <dl class="row">
                    <dt class="tit">
                        <label for="typelink"><em>*</em></label>
                    </dt>
                    <dd class="opt">
                        <input type="text" value="" name="typelink" id="typelink" class="input-txt" placeholder="请填写完整外部链接">
                        <p class="notic">请填写完整外部链接</p>
                    </dd>
                </dl>
                <dl class="row">
                    <dt class="tit">
                        <label>链接属性</label>
                    </dt>
                    <dd class="opt">
                        <label><input type="checkbox" name="target" value="1">新窗口打开</label>&nbsp;&nbsp;
                        <label><input type="checkbox" name="nofollow" value="1">nofollow</label>&nbsp;&nbsp;
                        <a href="javascript:void(0);" onclick="click_to_eyou_1575506523('https://www.eyoucms.com/plus/view.php?aid=17589&origin_eycms=1','链接属性使用教程')">查看标签使用教程</a>
                        <span class="err"></span>
                        <p class="notic"></p>
                    </dd>
                </dl>
            </div>
            <dl class="row">
                <dt class="tit">
                    <label for="englist_name">英文别名</label>
                </dt>
                <dd class="opt">
                    <input type="text" value="" name="englist_name" id="englist_name" class="input-txt">
                    <p class="notic">显示英文名栏目的网站</p>
                </dd>
            </dl>
            <dl class="row">
                <dt class="tit">
                  <label>栏目图片</label>
                </dt>
                <dd class="opt">
                    <div class="input-file-show div_litpic_local">
                        <span class="show">
                            <a id="img_a" target="_blank" class="nyroModal" rel="gal" href="javascript:void(0);">
                                <i id="img_i" class="fa fa-picture-o" onmouseover="" onmouseout="layer.close(layer_tips);"></i>
                            </a>
                        </span>
                        <span class="type-file-box">
                            <input type="text" id="litpic_local" name="litpic_local" value="" class="type-file-text" autocomplete="off">
                            <input type="button" name="button" id="button1" value="选择上传..." class="type-file-button">
                            <input class="type-file-file" onClick="GetUploadify(1,'','allimg','img_call_back')" size="30" hidefocus="true" nc_type="change_site_logo"
                                 title="点击前方预览图可查看大图，点击按钮选择文件并提交表单后上传生效">
                        </span>
                    </div>
                    <input type="text" id="litpic_remote" name="litpic_remote" value="" placeholder="http://" class="input-txt" style="display: none;">
                    &nbsp;
                    <label><input type="checkbox" name="is_remote" id="is_remote" value="1" onClick="clickRemote(this, 'litpic');">远程图片</label>
                    <span class="err"></span>
                    <p class="notic"></p>
                </dd>
            </dl>
            <dl class="row" id="dl_typearcrank">
                <dt class="tit">
                    <label>访问权限</label>
                </dt>
                <dd class="opt">
                    <input type="hidden" value="0" name="old_arcrank" class="input-txt">
                    <select name="typearcrank" id="typearcrank" onclick="change_typearcrank(this);">
                    {volist name="$arcrank_list" id="vo"}
                    <option value="{$vo.rank}">{$vo.name}</option>
                    {/volist}
                    </select>
                    <span class="err"></span>
                    <p class="notic">限制栏目和栏目里的文档阅读权限</p>
                </dd>
            </dl>
            <dl class="row none" id="dl_page_limit">
                <dt class="tit">
                    <label>限制页面</label>
                </dt>
                <dd class="opt">
                    <label><input type="checkbox" name="page_limit[]" value="1">栏目页面</label>&nbsp;
                    <label><input type="checkbox" name="page_limit[]" value="2" checked>文档页面</label>
                    <span class="err"></span>
                    <p class="notic">限制栏目页面后，栏目打开将受限制，限制文档页面后，则打开文档页面时受限制</p>
                </dd>
            </dl>
            
            <dl class="row" id="dl_templist">
                <dt class="tit">
                    <label for="templist"><em>*</em>栏目模板</label>
                </dt>
                <dd class="opt">
                    <select name="templist" id="templist">
                    </select>
                    <span class="err"></span>
                    <p class="notic">栏目模板命名规则：<br/>lists_<font class="font_nid">模型标识</font>.htm<br/>lists_<font class="font_nid">模型标识</font>_自定义.htm</p>
                    &nbsp;<a href="javascript:void(0);" onclick="newtpl('lists');" class="ncap-btn ncap-btn-green">新建模板</a>
                </dd>
            </dl>
            <dl class="row" id="dl_tempview">
                <dt class="tit">
                    <label for="tempview"><em>*</em>文档模板</label>
                </dt>
                <dd class="opt">
                    <select name="tempview" id="tempview">
                    </select>
                    <span class="err"></span>
                    <p class="notic">文档模板命名规则：<br/>view_<font class="font_nid">模型标识</font>.htm<br/>view_<font class="font_nid">模型标识</font>_自定义.htm</p>
                    &nbsp;<a href="javascript:void(0);" onclick="newtpl('view');" class="ncap-btn ncap-btn-green">新建模板</a>
                </dd>
            </dl>
            <dl class="row {if $is_diyseo_htmlpath == 0 || $seo_html_listname != 4} none {/if}" id="dl_rulelist">
                <dt class="tit">
                    <label for="dirpath">栏目名规则</label>
                </dt>
                <dd class="opt">
                    <input type="text" value="{$rulelist}" name="rulelist" id="rulelist" class="input-txt" >
                    <p class="notic2">1、{tid} 栏目ID<span id="rule_tips_2"><br/>2、{page} 栏目的页码，如果没有该变量，第1页以后的html页面将不存在</span></p>
                </dd>
            </dl>
            <dl class="row {if $is_diyseo_htmlpath == 0 || $seo_html_pagename != 4} none {/if}" id="dl_ruleview">
                <dt class="tit">
                    <label for="dirpath">文档名规则</label>
                </dt>
                <dd class="opt">
                    <input type="text" value="{$ruleview}" name="ruleview" id="ruleview" class="input-txt" >&nbsp;
                    <p class="notic2">1、{Y}、{M}、{D} 年月日<br/>2、{aid} 文档ID</p>
                </dd>
            </dl>
            <dl class="row">
                <dt class="tit">
                    <label for="seo_title">SEO标题</label>
                </dt>
                <dd class="opt">
                    <input type="text" value="" name="seo_title" id="seo_title" class="input-txt">
                    <p class="notic"></p>
                </dd>
            </dl>
            <dl class="row">
                <dt class="tit">
                    <label>SEO关键字</label>
                </dt>
                <dd class="opt">          
                    <textarea rows="5" cols="60" id="seo_keywords" name="seo_keywords" style="height:40px;"></textarea>
                    <span class="err"></span>
                    <p class="notic">多个关键词请用英文逗号（,）隔开，建议3到5个关键词。</p>
                </dd>
            </dl>
            <dl class="row">
                <dt class="tit">
                    <label>SEO描述</label>
                </dt>
                <dd class="opt">          
                    <textarea rows="5" cols="60" id="seo_description" name="seo_description" style="height:54px;" class="keywordsTextarea" onkeyup="monitorInputStr();" onkeypress="monitorInputStr();"></textarea>
                    <span class="err"></span>
                    <p class="notic">一般不超过100个字符，不填写时系统自动提取正文的前100个字符</p>
                    <p class="notic2 none" id="beenWritten">你已输入<span id="beenWrittenStr">0</span>个字符</p>
                </dd>
            </dl>
        </div>
        <!-- 高级选项 -->
        <div class="ncap-form-default">
            <div class="bot">
                <input type="hidden" name="grade" id="grade" value="{$grade|default='0'}">
                <input type="hidden" name="editor_addonFieldExt" id="editor_addonFieldExt" value="{$editor_addonFieldExt|default=''}">
                <a href="JavaScript:void(0);" onclick="check_submit();" class="ncap-btn-big ncap-btn-green" id="submitBtn">确认提交</a>
            </div>
        </div> 
    </form>
</div>
<script type="text/javascript">
    var templateList = {$templateList|json_encode};

    $(document).ready(function(){    
        //选项卡切换列表
        $('.tab-base').find('.tab').click(function(){
            $('.tab-base').find('.tab').each(function(){
                $(this).removeClass('current');
            });
            $(this).addClass('current');
            var tab_index = $(this).data('index');          
            $(".tab_div_1, .tab_div_2").hide();          
            $(".tab_div_"+tab_index).show();
        });

        $('input[name=is_part]').click(function(){
            var val = $(this).val();
            if (val == 1) {
                $('#row_url').show();
            } else {
                $('#row_url').hide();
            }
        });

        ajax_get_template();
    });

    /*根据模型ID获取模板文件名*/
    function ajax_get_template() {
        var is_diyseo_htmlpath = {$is_diyseo_htmlpath|default=0};
        var obj = $('#current_channel');
        var channel = parseInt($(obj).find('option:selected').val());
        var js_allow_channel_arr = {$js_allow_channel_arr};
        $('#notic_current_channel').html('');
        if (51 == channel){
            $("#type_attr").hide();
            $('#dl_dirname').hide();
        }else{
            $("#type_attr").show();
            if (is_diyseo_htmlpath == 1) {
                $('#dl_dirname').hide();
            } else {
                $('#dl_dirname').show();
            }
        }
        if (6 == channel){
            $('#dl_typearcrank').hide();
        }else{
            $('#dl_typearcrank').show();
        }
        // 重新定义模板变量，专用于新建模板功能
        $.ajax({
            url: "{:url('Arctype/ajax_getTemplateList')}",
            type: 'GET',
            dataType: 'JSON',
            data: {_ajax:1},
            success: function(res){
                if (res.code == 1) {
                    templateList = res.data.templateList;
                }
            }
        });
        // end

        if (templateList[channel] == undefined || templateList[channel] == '') {
            showErrorMsg('模板文件不存在！');
            return false;
        } else if (templateList[channel]['msg'] != '') {
            $('#notic_current_channel').html(templateList[channel]['msg']);
        }

        $('#templist').html(templateList[channel]['lists']);
        if ($.inArray(channel, js_allow_channel_arr) == -1) {
            if (channel == 6) {
                $('#dl_templist').find('label[for=templist]').html('<em>*</em>单页模板');
                var rulelistObj = $('input[name=rulelist]');
                if (rulelistObj.val() == '' || '{栏目目录}/list_{tid}_{page}.html' == rulelistObj.val() || '{typedir}/list_{tid}_{page}.html' == rulelistObj.val()) {
                    rulelistObj.val('{栏目目录}/index.html');
                }
                $('#rule_tips_2').hide();
            } else if (channel == 8) {
                $('#dl_templist').find('label[for=templist]').html('<em>*</em>留言模板');
                var rulelistObj = $('input[name=rulelist]');
                if (rulelistObj.val() == '' || '{栏目目录}/list_{tid}_{page}.html' == rulelistObj.val() || '{typedir}/list_{tid}_{page}.html' == rulelistObj.val()) {
                    rulelistObj.val('{栏目目录}/index.html');
                }
                $('#rule_tips_2').hide();
            }else if (51 == channel) {
                $('#dl_templist').hide();
                $('#dl_rulelist').hide();
            }
            $('#dl_tempview').hide();
            $('#dl_ruleview').hide();
        } else {
            $('#rule_tips_2').show();
            $('#dl_templist').find('label[for=templist]').html('<em>*</em>栏目模板');
            $('#dl_tempview').show();

            var is_diyseo_htmlpath = {$is_diyseo_htmlpath|default=0};
            var seo_html_pagename = {$seo_html_pagename|default=2};
            if (is_diyseo_htmlpath == 1 && seo_html_pagename == 4) {
                $('#dl_ruleview').show();
            }
        }
        $('#tempview').html(templateList[channel]['view']);

        $('.font_nid').html(templateList[channel]['nid']);

        return false;
    }
    /*--end*/

    function get_dirpinyin(obj)
    {
        var typename = $('input[name=typename]').val();
        if ($.trim(typename) == '') {
            showErrorMsg('先填写栏目名称！');
            $('input[name=typename]').focus();
            return false;
        }
        $(obj).html('正在处理');
        $.ajax({
            url: "{:url('Arctype/ajax_get_dirpinyin', ['_ajax'=>1])}",
            type: 'POST',
            dataType: 'JSON',
            data: {typename: typename},
            success: function(res){
                $(obj).html('获取拼音');
                if (res.code == 1) {
                    var diy_dirpath = $('#dirpath').val() + '/' + res.data.pinyin;
                    $('#diy_dirpath').val(diy_dirpath);
                    return true;
                } else {
                    showErrorMsg('操作失败');
                    return false;
                }
            },
            error: function(e){
                $(obj).html('获取拼音');
                showErrorMsg(e.responseText);
                return false;
            }
        });
    }

    function get_arctype(obj) {
        $('#parent_id').html('<option value="">加载中……</option>');
        var channeltype = parseInt($(obj).find("option:selected").val());
        $.ajax({
            url: "{:url('Arctype/ajax_get_arctype', ['_ajax'=>1])}",
            type: 'POST',
            dataType: 'JSON',
            data: {channeltype:channeltype},
            success: function(res){
                if (res.status == 1) {
                    $('#parent_id').html(res.select_html);
                } else {
                    showErrorMsg('操作失败');
                    return false;
                }
            },
            error: function(e){
                showErrorMsg(e.responseText);
                return false;
            }
        });
    }

    function set_grade(obj) {
        var grade = parseInt($(obj).find("option:selected").attr("data-grade"));
        $('#grade').val(grade + 1);
        var dirpath = $(obj).find("option:selected").attr("data-dirpath");
        $('#dirpath').val(dirpath);
    }

    function check_submit(){
        if($('input[name="typename"]').val() == ''){
            showErrorMsg('栏目名称不能为空！');
            $('input[name=typename]').focus();
            return false;
        }
        if ($('input[name=is_part]:checked').val() == 1) {
            if($('input[name=typelink]').val() == ''){
                showErrorMsg('外部链接不能为空！');
                $('input[name=typelink]').focus();
                return false;
            }
        }
        var dirname = $('input[name="dirname"]').val();
        var patrn = /^\d+$/;
        if (patrn.test(dirname) == true) {
            showErrorMsg('目录名称不能为纯数字！');
            $('input[name=dirname]').focus();
            return false;
        }

        var channel = parseInt($('#current_channel').find('option:selected').val());
        if($('#templist').val() == '' && 51 != channel){
            $('.tab-base').find('.tab').each(function(){
                $(this).removeClass('current');
            });
            $($('.tab-base').find('.tab').get(1)).addClass('current');
            $('.tab_div_1').hide();
            $('.tab_div_2').show();
            showErrorMsg('请选择栏目模板');
            $('#templist').focus();
            return false;
        }

        var js_allow_channel_arr = {$js_allow_channel_arr};
        if($('#tempview').val() == '' && $.inArray(channel, js_allow_channel_arr) != -1){
            $('.tab-base').find('.tab').each(function(){
                $(this).removeClass('current');
            });
            $($('.tab-base').find('.tab').get(1)).addClass('current');
            $('.tab_div_1').hide();
            $('.tab_div_2').show();
            showErrorMsg('请选择文档模板');
            $('#tempview').focus();
            return false;
        }
        
        layer_loading('正在处理');
        if(!ajax_check_dirpath())
        {
            layer.closeAll();
            showErrorMsg('文件保存目录已存在！');
            $('input[name=dirpath]').focus();
            return false;
        }

        setTimeout(function (){
            editor_auto_210607('type');
            $('#post_form').submit();
        }, 1);
    }

    function ajax_check_dirpath()
    {
        return true;
/*
        var flag = false;
        var dirpath = $.trim($('input[name=dirpath]').val());
        $.ajax({
            url: "{:url('Arctype/ajax_check_dirpath', ['_ajax'=>1])}",
            type: 'POST',
            async: false,
            dataType: 'JSON',
            data: {dirpath: dirpath, id: 0},
            success: function(res){
                if(res.status == 1){
                    flag = true;
                }
            },
            error: function(e){}
        });

        return flag;
        */
    }

    function img_call_back(fileurl_tmp)
    {
      $("#litpic_local").val(fileurl_tmp);
      $("#img_a").attr('href', fileurl_tmp);
      $("#img_i").attr('onmouseover', "layer_tips=layer.tips('<img src="+fileurl_tmp+" class=\\'layer_tips_img\\'>',this,{tips: [1, '#fff']});");
    }

    function newtpl(type)
    {
        $.ajax({
            url: "{:url('Security/ajax_security_ask_open', ['_ajax'=>1])}",
            type: 'GET',
            dataType: 'JSON',
            data: {},
            success: function(res){
                var security_ask_open = res.data.security_ask_open;
                if (0 == security_ask_open) {
                    showConfirm('需要设置密保问题验证才可以继续', {btn:['去设置', '取消']}, function(){
                        layer.closeAll();
                        var iframes = layer.open({
                            type: 2,
                            title: '安全验证中心',
                            fixed: true, //不固定
                            shadeClose: false,
                            shade: layer_shade,
                            offset: 'auto',
                            // maxmin: true, //开启最大化最小化按钮
                            area: ['100%', '100%'],
                            content: "{:url('Security/second_ask_init')}"+"&source=arctype_"+type,
                            success: function(layero, index){

                            }
                        });
                        layer.full(iframes);
                    });
                    return false;
                }

                var nid = $('#current_channel').find('option:selected').attr('data-nid');
                var url = "{:url('Arctype/ajax_newtpl')}";
                if (url.indexOf('?') > -1) {
                    url += '&';
                } else {
                    url += '?';
                }
                url += 'type='+type+'&nid='+nid;

                if ('lists' == type) {
                    var title = '新建栏目模板';
                } else {
                    var title = '新建文档模板';
                }
                //iframe窗
                layer.open({
                    type: 2,
                    title: title,
                    fixed: true, //不固定
                    shadeClose: false,
                    shade: layer_shade,
                    maxmin: true, //开启最大化最小化按钮
                    area: ['90%', '90%'],
                    content: url
                });
            },
            error: function(e){
                showErrorMsg(e.responseText);
                return false;
            }
        });
    }

    function change_typearcrank(obj) {
        var value = $(obj).val();
        if (value > 0 ){
            $("#dl_page_limit").show();
        }else{
            $("#dl_page_limit").hide();
        }
    }
</script>

{include file="public/footer" /}