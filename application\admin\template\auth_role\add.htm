{include file="public/layout" /}
<body class="rolecss bodystyle">
<div id="toolTipLayer" style="position: absolute; z-index: 9999; display: none; visibility: visible; left: 95px; top: 573px;"></div>
<div id="append_parent"></div>
<div id="ajaxwaitid"></div>
<div class="page" style=" min-width: auto;">
   
    <form class="form-horizontal" id="postForm" method="post" action="{:url('AuthRole/add')}">
        <div class="ncap-form-default">
            <dl class="row"><dt class="tit"><label><b>角色信息</b></label></dt></dl>
            <dl class="row">
                <dt class="tit">
                    <label for="name"><em>*</em>角色名称</label>
                </dt>
                <dd class="opt">
                    <input type="text" name="name" id="name" value="" class="input-txt" autocomplete="off">
                    <span class="err"></span>
                    <p class="notic"></p>
                </dd>
            </dl>
            
            <dl class="row">
                <dt class="tit">
                    <label for="name">角色模板</label>
                </dt>
                <dd class="opt">
                    <p><label><input type="radio" name="tmp_role_id" value="-1" />超级管理员</label></p>
                    {foreach name="admin_role_list" item="role" key="k"}
                    <p>
                        <label><input type="radio" name="tmp_role_id" value="{$role.id}" />{$role.name}</label>
                    </p>
                    {/foreach}
                </dd>
            </dl>
            <dl class="row"><dt class="tit"><label><b>权限设置</b></label></dt></dl>
            <dl class="row none">
                <dt class="tit">
                    <label for="name">语言权限</label>
                </dt>
                <dd class="opt">
                    <label><input type="checkbox" name="language[]" value="cn" checked="checked" />简体中文</label>
                    <span class="err"></span>
                    <p class="notic"></p>
                </dd>
            </dl>
            <dl class="row">
                <dt class="tit">
                    <label for="name">在线升级</label>
                </dt>
                <dd class="opt">
                    <label><input type="checkbox" name="online_update" value="1" />允许操作</label>
                    <span class="err"></span>
                    <p class="notic"></p>
                </dd>
            </dl>
            <dl class="row">
                <dt class="tit">
                    <label for="name">功能地图</label>
                </dt>
                <dd class="opt">
                    <label><input type="checkbox" name="switch_map" value="1" />允许操作</label>
                    <span class="err"></span>
                    <p class="notic"></p>
                </dd>
            </dl>
            <dl class="row" id="dl_only_oneself">
                <dt class="tit">
                    <label for="name">文档权限</label>
                </dt>
                <dd class="opt">
                    <label><input type="checkbox" name="only_oneself" value="1" />只允许查看自己发布的文档</label>
                    <span class="err"></span>
                    <p class="notic"></p>
                </dd>
            </dl>
            <dl class="row" id="dl_check_oneself">
                <dt class="tit">
                    <label for="name">文档审核</label>
                </dt>
                <dd class="opt">
                    <label><input type="checkbox" name="check_oneself" value="1" checked="checked" />发布文档自动通过审核</label>
                    <span class="err"></span>
                    <p class="notic"></p>
                </dd>
            </dl>
            <dl class="row">
                <dt class="tit">
                    <label for="name">操作权限</label>
                </dt>
                <dd class="opt">
                    <p><label><input type="checkbox" id="select_cud" checked="checked" />完全控制</label></p>
                    <p><label><input type="checkbox" name="cud[]" value="add" checked="checked" />新增信息</label></p>
                    <p><label><input type="checkbox" name="cud[]" value="edit" checked="checked" />编辑信息</label></p>
                    <p><label><input type="checkbox" name="cud[]" value="del" checked="checked" />删除信息</label></p>
                    <p><label><input type="checkbox" name="cud[]" value="changetableval" checked="checked" />审核信息</label></p>
                    <span class="err"></span>
                    <p class="notic"></p>
                </dd>
            </dl>
            <dl class="row">
                <dt class="tit">
                    <label for="name"><em>*</em>功能权限</label>
                </dt>
                <dd class="opt">
                    <p>
                        <label><input type="checkbox" id="select_all_permission" />全部选择</label>
                    </p>
                    {foreach name="modules" item="vo"}
                      {foreach name="vo.child" item="vo2"}
                        {if condition="1 == $vo2['is_modules'] AND ! empty($auth_rule_list[$vo2.id])"}
                          {if condition="1002 == $vo2['id']"}
                            <div class="admin_poplistdiv">
                                <h2>{$vo2.name}</h2>
                                {if condition="! empty($arctype_p_html)"}
                                <p>
                                    {$arctype_p_html}
                                </p>
                                {$arctype_child_html}
                                {/if}
                            </div>
                          {else /}
                            <div class="admin_poplistdiv">
                                <h2>{$vo2.name}</h2>
                                <p>
                                    {foreach name="auth_rule_list[$vo2.id]" item="rule"}
                                    <label><input type="checkbox" name="permission[rules][]" value="{$rule.id}" />{$rule.name}</label>
                                    {/foreach}
                                </p>
                            </div>
                          {/if}
                        {/if}
                      {/foreach}
                    {/foreach}

                    {if condition="! empty($plugins)"}
                    <div class="admin_poplistdiv">
                        <h2>插件应用</h2>
                        <ul>
                            {foreach name="plugins" item="plugin"}
                            <li>
                                <label><input type="checkbox" name="permission[plugins][{$plugin.code}][code]" value="{$plugin.code}" />{$plugin.name}</label>
                                {php}$config = json_decode($plugin['config'], true);{/php}
                                {if condition="! empty($config['permission'])"}
                                <p style="padding-left:10px;">
                                    <span class="button level1 switch center_docu"></span>
                                    {foreach $config['permission'] as $index => $text}
                                    <label><input type="checkbox" name="permission[plugins][{$plugin.code}][child][]" value="{$index}" />{$text}</label>
                                    {/foreach}
                                </p>
                                {/if}
                            </li>
                            {/foreach}
                        </ul>
                    </div>
                    {/if}
                </dd>
            </dl>

            <div class="bot2">
                <input type="hidden" name="arctype_str" id="arctype_str" value="">
                <a href="JavaScript:void(0);" onclick="postSubmit();" class="ncap-btn-big ncap-btn-green" id="submitBtn">确认提交</a>
            </div>
        </div>
    </form>
</div>
<script type="text/javascript">
    var admin_role_list = {$admin_role_list|json_encode};
    $(function(){
        $('#postForm input[name="tmp_role_id"]').bind('click', function(){
            changeRole($(this).val());
        });
        $(".rolecss").on("click",".arctype_bg",function(){
            var acid = $(this).next().find('input').val(), input = 'arctype_child_' + acid;
            $('.arctype_child').hide();
            if( $(this).attr('class').indexOf('expandable') == -1 ){
                $(this).removeClass('collapsable').addClass('expandable');
            }else{
                $('.arctype_bg').removeClass('collapsable').addClass('expandable');
                $(this).removeClass('expandable').addClass('collapsable');
                $('#'+input).show();
            }
        });
        //循环判断选中上级
        function checked_pid(pid) {
            if (0 < $('input[data-pid="'+pid+'"]:checked').length) {
                $('.arctype_id_'+pid).prop('checked', 'checked');
                var pid1 = $('.arctype_id_'+pid).data('pid');
                if (pid1){
                    checked_pid(pid1);
                }
            }
            return false;
        }
        //循环判断取消上级
        function cancel_pid(pid) {
            if (1 > $('#arctype_child_'+pid).find('input[type="checkbox"]:checked').length) {
                $('.arctype_id_'+pid).removeAttr('checked');
                var pid1 = $('.arctype_id_'+pid).data('pid');
                if (pid1){
                    cancel_pid(pid1);
                }
            }
            return false;
        }
        $(".rolecss").on("click",".arctype_cbox",function () {
            var acid = $(this).val(), input = 'arctype_child_' + acid;
            var pid = $(this).data('pid');
            var tpid = $(this).data('tpid');
            if($(this).prop('checked')){  //选中，所有直系上级全部选中,所有管辖下级选中
                checked_pid(pid);
                $('#'+input).find('input[type="checkbox"]').prop('checked', 'checked');  //选中所有下级
            }else{    //取消，判断同级是否全部为取消状态，取消直系上级
                $('#'+input).find('input[type="checkbox"]').removeAttr('checked');  //取消所有下级
                cancel_pid(pid);
            }
        });
        $('#select_cud').bind('click', function(){
            if($(this).prop('checked')){
                $('#postForm input[name^="cud"]').prop('checked', 'checked');
            }else{
                $('#postForm input[name^="cud"]').removeAttr('checked');
            }
        });

        $('#select_all_permission').bind('click', function(){
            if($(this).prop('checked')){
                $('#postForm input[name^="permission"]').prop('checked', 'checked');
                $('#postForm .arctype_cbox').prop('checked', 'checked');
            }else{
                $('#postForm input[name^="permission"]').removeAttr('checked');
                $('#postForm .arctype_cbox').removeAttr('checked');
            }
        });
        $('#postForm input[name^="permission"],#postForm .arctype_cbox,#postForm input[name^="cud"]').bind('click', function(){
            hasSelectAll();
        });
    });

    function hasSelectAll(){
        var c = true;
        $('#postForm input[name^="permission"],#postForm .arctype_cbox').each(function(idx, ele){
            if(! $(ele).prop('checked')){
                c = false;
                return;
            }
        });
        if(c){
            $('#select_all_permission').prop('checked', 'checked');
        }else{
            $('#select_all_permission').removeAttr('checked');
        }


        var c = true;
        $('#postForm input[name^="cud"]').each(function(idx, ele){
            if(! $(ele).prop('checked')){
                c = false;
                return;
            }
        });
        if(c){
            $('#select_cud').prop('checked', 'checked');
        }else{
            $('#select_cud').removeAttr('checked');
        }
    }

    function changeRole(value){
        if (-1 == value) {
            $('#postForm input[type="checkbox"]').attr("checked","checked");
            // $('#postForm input[name="only_oneself"]').removeAttr('checked');
            return;
        }
        $('#postForm input[type="checkbox"]').removeAttr('checked');
        for(var i in admin_role_list){
            var item = admin_role_list[i];
            if(item.id == value){
                if(item.language){
                    item.language.map(function(row){
                        $('#postForm input[name^="language"][value="'+row+'"]').prop('checked', 'checked');
                    });
                }

                if(item.online_update){
                    $('#postForm input[name="online_update"]').prop('checked', 'checked');
                };
                if(item.switch_map){
                    $('#postForm input[name="switch_map"]').prop('checked', 'checked');
                };
                // if(item.editor_visual){
                //     $('#postForm input[name="editor_visual"]').prop('checked', 'checked');
                // };
                if(item.only_oneself){
                    $('#postForm input[name="only_oneself"]').prop('checked', 'checked');
                };
                if(item.check_oneself){
                    $('#postForm input[name="check_oneself"]').prop('checked', 'checked');
                };
                if(item.cud){
                    item.cud.map(function(row){
                        $('#postForm input[name^="cud"][value="'+row+'"]').prop('checked', 'checked');
                    });
                }

                if(item.permission){
                    for(var p in item.permission){
                        if(p == 'plugins'){
                            if(item.permission[p]){
                                for(var pluginId in item.permission[p]){
                                    $('#postForm input[name="permission['+p+']['+pluginId+'][code]"][value="'+pluginId+'"]').prop('checked', 'checked');
                                    if(item.permission[p][pluginId].child){
                                        item.permission[p][pluginId].child.map(function(row){
                                            $('#postForm input[name="permission['+p+']['+pluginId+'][child][]"][value="'+row+'"]').prop('checked', 'checked');
                                        });
                                    }
                                }
                            }
                        } else if (p == 'arctype') {
                            item.permission[p].map(function(row){
                                $('#postForm .arctype_id_'+row).prop('checked', 'checked');
                            });
                        }else{
                            item.permission[p].map(function(row){
                                $('#postForm input[name="permission['+p+'][]"][value="'+row+'"]').prop('checked', 'checked');
                            });
                        }
                        
                    }
                }

                hasSelectAll();
                break;
            }
        }
    }

    function postSubmit(){
        if($('input[name=name]').val() == '' ){
            showErrorMsg('角色名称不能为空！');
            $('input[name=name]').focus();
            return false;
        }

        var a = [];
        $('input[name^=permission],#postForm .arctype_cbox').each(function(i,o){
            if($(o).is(':checked')){
                a.push($(o).val());
            }
        })
        if(a.length == 0){
            showErrorMsg('请具体分配权限！');
            return false;
        }

        var arctype_arr = new Array();
        $(".arctype_cbox:checked").each(function(i){
            arctype_arr[i] = $(this).val();
        });
        var arctype_str = arctype_arr.join(",");
        $('#arctype_str').val(arctype_str);

        var parentObj = parent.layer.getFrameIndex(window.name); //先得到当前iframe层的索引

        layer_loading('正在处理');
        {lt name="$Request.param.iframe" value="1"}
            $.ajax({
                type : 'post',
                url : "{:url('AuthRole/add', ['_ajax'=>1])}",
                data : $('#postForm').serialize(),
                dataType : 'json',
                success : function(res){
                    layer.closeAll();
                    if(res.code == 1){
                        var _parent = parent;
                        _parent.layer.close(parentObj);
                        _parent.layer.msg(res.msg, {shade: layer_shade, time: 1000}, function(){
                            _parent.window.location.reload();
                        });
                    }else{
                        showErrorMsg(res.msg);
                    }
                },
                error: function(e){
                    layer.closeAll();
                    layer.alert(e.responseText, {icon: 5, title:false});
                }
            });
        {else /}
            $.ajax({
                url: "{:url('AuthRole/add', ['_ajax'=>1])}",
                type: 'POST',
                dataType: 'JSON',
                data: $('#postForm').serialize(),
                success: function(res){
                    layer.closeAll();
                    if (res.code == 1) {
                        var str = '<p><label><input type="radio" name="role_id" value="'+res.data.role_id+'" onclick="changeRole('+res.data.role_id+');" checked="checked" />'+res.data.role_name+'</label></p>';
                        parent.custom_role(str, res.data.role_id, res.data.admin_role_list);
                        parent.layer.close(parentObj);
                        parent.layer.msg(res.msg, {shade: layer_shade, time: 1000});
                    } else {
                        parent.showErrorMsg(res.msg);
                    }
                    return false;
                },
                error: function(e){
                    layer.closeAll();
                    layer.msg('操作失败', {icon:5, time:1500});
                    return false;
                }
            });
        {/lt}
    }
</script>

{include file="public/footer" /}