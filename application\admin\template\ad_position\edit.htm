{include file="public/layout" /}

<body class="ad_postion bodystyle" style="overflow-y: scroll;">
<div id="toolTipLayer" style="position: absolute; z-index: 9999; display: none; visibility: visible; left: 95px; top: 573px;"></div>
<div id="append_parent"></div>
<div id="ajaxwaitid"></div>
<div class="page min-hg-c-10">
    <form class="form-horizontal" id="post_form" action="{:url('AdPosition/edit')}" method="post">
        <div class="ncap-form-default">
            <dl class="row">
                <dt class="tit"> <label for="title"><em>*</em>广告名称</label> </dt>
                <dd class="opt">
                    <input type="text" name="title" value="{$field.title}" id="title" onkeyup="DetectionTitleRepeat(this);" class="input-txt" autocomplete="off">
                    <span class="err"></span>
                    <p class="notic2 red" id="title_tips"></p>
                </dd>
            </dl>

            <dl class="row">
                <dt class="tit"> <label for="type">广告类型</label> </dt>
                <dd class="opt">
                    <input type="hidden" name="type" value="{$field.type}"> {$field.type_name} 
                </dd>
            </dl>

            <dl class="row 1615775137_dl" style="display: none;" id="1615775137_1" style="z-index: 2">
                <dt class="tit"> <label>广告内容</label> </dt>
                <dd class="opt">
                    <div class="tab-pane" id="tab_imgupload">
                        <table class="table table-bordered">
                            <tbody>
                            <tr>
                                <td>
                                    <div class="sort-list">
                                        {volist name="ad_data" id="vo" key="k"}
                                            {eq name="$vo.media_type" value="1"}
                                                <div class="images_upload">
                                                    <div class="ic">
                                                        <div class='upimg' title="拖动修改排序" onmouseover="upimgMouseover(this);" onmouseout="upimgMouseout(this);">
                                                            <div class='icaction' style="display: none" >
                                                                <span class="load_images" onclick="Images('{$vo['litpic']}', 900, 600);">
                                                                    <a href="javascript:void(0);" style="color:white">
                                                                        <i class='fa fa-search-plus'></i>大图
                                                                    </a>
                                                                </span>
                                                                <span class="load_images" onclick="LoadImagesId('{$vo['id']}');">
                                                                    <i class='fa fa-photo'></i>更换
                                                                </span>
                                                                <span class="load_images" onclick="ShowInput('{$vo['id']}');">
                                                                    <i class='fa fa-file-text-o'></i>信息
                                                                </span>
                                                            </div>
                                                            <div class='cover-bg' style="display: none" ></div>
                                                            <img id="{$vo['id']}_Id" src="{$vo['litpic']}"/>
                                                            {if condition="$main_lang == $admin_lang || !empty($global['language_split'])"}
                                                                <a class="delect" href="javascript:void(0);" onclick="ClearPicArr(this,'{$vo['litpic']}','{$vo['id']}');" title="删除">
                                                                </a>
                                                            {/if}
                                                        </div>
                                                        
                                                        <div class="load_input" id="{$vo['id']}_ShowInput" data-showOrHide="hide">
                                                            <input type="hidden" name="img_id[]" value="{$vo['id']}"/>
                                                            <span class="span_input">
                                                                <input type="hidden" id="{$vo['id']}_Litpic" name="img_litpic[]" value="{$vo['litpic']}"/>
                                                            </span>
                                                            <textarea name="img_title[]" placeholder="请输入标题..." style="height: 28px;">{$vo.title}</textarea>
                                                            <textarea name="img_links[]" placeholder="请输入链接网址..." style="height: 28px;">{$vo.links}</textarea>
                                                            <textarea name="img_intro[]" placeholder="广告注释：支持HTML代码" style="height: 64px;">{$vo.intro}</textarea>
                                                            <div class="operation">
                                                                <a href="javascript:void(0);">
                                                                    <label>
                                                                        <input type="checkbox" {eq name="$vo['target']" value="1"} checked="checked" {/eq} title="在新窗口打开" onclick="CheckedTarget(this)"/>新窗口
                                                                        <input type="hidden" name="img_target[]" value="{$vo['target']}">
                                                                    </label>
                                                                </a>
                                                                <a href="javascript:void(0);" onclick="copyToClipBoard({$vo['id']});">
                                                                    <i class="fa fa-file-code-o"></i>标签调用
                                                                </a>
                                                                <a onclick="HideInput('{$vo['id']}');" style="width: 32%;" href="javascript:void(0);" title="收回"><i style="font-size:16px;" class="fa fa-angle-double-up"></i></a>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            {/eq}
                                        {/volist}
                                        <div class="images_upload"></div>
                                    </div> 
                                    {if condition="$main_lang == $admin_lang || !empty($global['language_split'])"}
                                        <a href="javascript:void(0);" onClick="GetUploadify(30,'','allimg','imgupload_call_back');" class="img-upload b-img-upload mb15" title="点击上传">
                                            <div class="y-line"></div>
                                            <div class="x-line"></div>
                                        </a>
                                    {/if}
                                    <input type="hidden" id="ImagesId">
                                </td>
                            </tr>
                            </tbody>
                        </table>
                    </div>
                    <!-- 上传图片显示的样板 start -->
                    <div class="images_upload_tpl none">
                        <div class="images_upload ic">
                            <div class="ic">
                                <div class='upimg' title="拖动修改排序" onmouseover="upimgMouseover(this);" onmouseout="upimgMouseout(this);">
                                    <div class='icaction' style="display: none">
                                        <span class="load_images" onclick="">
                                            <a href="javascript:void(0);" style="color: white">
                                                <i class='fa fa-search-plus'></i>大图
                                            </a>
                                        </span>
                                        <span class="load_images" onclick="">
                                            <i class='fa fa-file-text-o'></i>信息
                                        </span>
                                    </div>
                                    <div class='cover-bg' style="display: none"></div>
                                    <img src="__STATIC__/admin/images/add-button.jpg"/>
                                    <a class="delect" href="javascript:void(0);" title="删除"></a>
                                </div>

                                <div class="load_input" data-showOrHide="hide">
                                    <input type="hidden"/>
                                    <span class="span_input"> <input type="hidden"/> </span>
                                    <textarea placeholder="请输入标题..." style="height: 28px;"></textarea>
                                    <textarea placeholder="请输入链接网址..." style="height: 28px;"></textarea>
                                    <textarea placeholder="广告注释：支持HTML代码" style="height: 64px;"></textarea>
                                    <div class="operation">
                                        <a href="javascript:void(0);">&nbsp;&nbsp;</a>
                                        <a href="javascript:void(0);">&nbsp;&nbsp;</a>
                                        <a style="width: 32%;" href="javascript:void(0);">&nbsp;&nbsp;</a>
                                    </div> 
                                </div>
                            </div>
                        </div>
                    </div>
                    <!-- 上传图片显示的样板 end -->
                </dd>
            </dl>

            <dl class="row 1615775137_dl" style="display: none;" id="1615775137_2" style="z-index: 2">
                <dt class="tit"> <label for="type">广告内容</label> </dt>
                <dd class="opt">
                    <input type="hidden" name="video_id" {eq name="$ad_data.0.media_type" value="2"} value="{$ad_data.0.id}" {/eq} class="input-txt">
                    <input type="text" name="video_litpic" id="video_litpic" {eq name="$ad_data.0.media_type" value="2"} value="{$ad_data.0.litpic}" {/eq} class="input-txt">
                    <input type="file" id="courseware_file" data-type='local' onchange="upload_video_litpic_1615775137(this)" style="display: none;">
                    {eq name="$WeappOpen.qny_open" value="1"}
                    &nbsp;<a href="javascript:void(0);" class="ncap-btn ncap-btn-green" id="upload_video_litpic_qiniu" onclick="$('#courseware_file').attr('data-type', 'qiniu').trigger('click');">七牛云上传</a>
                    {/eq}
                    {eq name="$WeappOpen.oss_open" value="1"}
                    &nbsp;<a href="javascript:void(0);" class="ncap-btn ncap-btn-green" id="upload_video_litpic_oss" onclick="$('#courseware_file').attr('data-type', 'oss').trigger('click');">oss上传</a>
                    {/eq}
                    {eq name="$WeappOpen.cos_open" value="1"}
                    &nbsp;<a href="javascript:void(0);" class="ncap-btn ncap-btn-green" id="upload_video_litpic_cos" onclick="$('#courseware_file').attr('data-type', 'cos').trigger('click');">cos上传</a>
                    {/eq}
                    &nbsp;<a href="javascript:void(0);" class="ncap-btn ncap-btn-green " id="upload_video_litpic_local" onclick="$('#courseware_file').attr('data-type', 'local').trigger('click');">本地上传</a>
                    <input type="hidden" id="OpenPreviewVideoUrl" value="{:url('AdPosition/open_preview_video')}">
                    &nbsp;<a href="javascript:void(0);" class="ncap-btn ncap-btn-green" id="PreviewVideo" onclick="OpenPreviewVideo();">预览</a>
                </dd>
            </dl>

            <dl class="row 1615775137_dl" style="display: none;" id="1615775137_3" style="z-index: 2">
                <dt class="tit"> <label for="type">广告内容</label> </dt>
                <dd class="opt">
                    <input type="hidden" name="html_id" {eq name="$ad_data.0.media_type" value="3"} value="{$ad_data.0.id}" {/eq} class="input-txt">
                    <textarea rows="5" cols="60" id="html_intro" name="html_intro" style="height: 200px;">{eq name="$ad_data.0.media_type" value="3"}{$ad_data.0.intro}{/eq}</textarea>
                    <span class="err"></span>
                    <p class="notic"></p>
                </dd>
            </dl>

            <dl class="row">
                <dt class="tit">
                    <label>备注信息</label>
                </dt>
                <dd class="opt">          
                    <textarea rows="5" cols="60" id="intro" name="intro" style="height:60px;">{$field.intro}</textarea>
                    <span class="err"></span>
                    <p class="notic"></p>
                </dd>
            </dl>

            <div class="bot">
                <input type="hidden" name="id" id="1615775137_id" value="{$field.id}">
                <a href="JavaScript:void(0);" onclick="checkForm();" class="ncap-btn-big ncap-btn-green" id="submitBtn">确认提交</a>
            </div>
        </div>
    </form>
</div>

<script type="text/javascript">
    var SelectedType = '{$field.type}';
    $(function() {
        // 新增时，广告类型默认选中图片类型
        // $('#type'+SelectedType).prop('checked', 'checked');
        // 新增时，默认显示图片上传
        $('#1615775137_'+SelectedType).show();
        // 多媒体类型 && 广告内容不为空 则执行显示 否则 执行隐藏
        VideoLitpicValue('#video_litpic');
    });

    // 切换广告类型
    function TypeSwitch(typeValue) {
        // 隐藏全部类型内容
        $('.1615775137_dl').hide();
        // 显示指定类型内容
        $('#1615775137_'+typeValue).show();
        // 切换类型后设置为当前切换的类型
        SelectedType = typeValue;
        
        // var ad_type = '{$field.type}';
        // if (typeValue != ad_type) {
        //     // 切换至不同类型则显示
        //     $('.notic2').show();
        // } else {
        //     // 切换至相同类型则隐藏
        //     $('.notic2').hide();
        // }
    }

    // 多媒体类型 && 广告内容不为空 则执行显示 否则 执行隐藏
    function VideoLitpicValue(obj) {
        // 获取广告内容(多媒体为播放链接)
        // var video_litpic = $(obj).val();
        // 判断隐藏显示预览按钮if (2 == SelectedType && video_litpic)
        if (2 == SelectedType) {
            $('#PreviewVideo').show();
        } else {
            $('#PreviewVideo').hide();
        }
    }

    // 打开预览视频
    function OpenPreviewVideo() {
        // 获取视频链接
        var video_litpic = $('#video_litpic').val();
        if (video_litpic == '') {
            $('#video_litpic').focus();
            showErrorMsg('请先上传视频');
            return false;
        }

        /* 视频链接后缀验证处理 */
        var fileExt = video_litpic.substr(video_litpic.lastIndexOf('.')).toLowerCase();
        var fileExt = judgeExt(fileExt);
        if (fileExt == -1) {
            $('#video_litpic').focus();
            showErrorMsg('不支持的视频格式，可在附件设置中修改');
            return false;
        }
        /* END */

        /* 视频链接是否存在斜杠处理 */
        var fileInfo = video_litpic.lastIndexOf('/');
        if (fileInfo == -1) {
            $('#video_litpic').focus();
            showErrorMsg('视频链接不完整，无法正常预览');
            return false;
        }
        /* END */

        // 打开视频播放
        $.ajax({
            type : 'post',
            url  : $('#OpenPreviewVideoUrl').val(),
            data : {video_litpic: video_litpic},
            dataType : 'json',
            success : function(res) {
                if (1 == res.code) {
                    var PreviewVideo = "<video style='width:100%; height:100%;' src='"+res.url+"' controls preload='auto' oncontextmenu='return fase' autoplay></video>";
                    layer.open({
                        type: 1,
                        title: false,
                        fixed: true, //不固定
                        shadeClose: false,
                        shade: layer_shade,
                        area: ['80%', '80%'],
                        content: PreviewVideo
                    });
                } else {
                    showErrorMsg(res.msg);
                }
            },
            error: function(e) {
                showErrorAlert(e.responseText);
            }
        });
    }

    // 上传媒体文件
    function upload_video_litpic_1615775137(e) {
        var data_type = $(e).attr('data-type');
        if ('qiniu' == data_type) {
            // 七牛云上传
            upload_video_litpic_qiniu(e);
        } else if ('oss' == data_type) {
             // OSS上传
            upload_video_litpic_oss(e);
        } else if ('cos' == data_type) {
             // COS上传
            upload_video_litpic_cos(e);
        } else {
            // 本地上传
            upload_video_litpic_local(e);
        }
    }

    // 七牛云上传
    function upload_video_litpic_qiniu(e) {
        // 获取文件路径名
        var file = $(e)[0].files[0];

        // 验证上传格式
        var fileName = file.name;
        var fileExt = fileName.substr(fileName.lastIndexOf('.')).toLowerCase();
        var ext = judgeExt(fileExt);
        if (ext == -1) {
            showErrorMsg('不支持选中的视频格式,可在附件设置中修改');
            return false;
        }

        // 验证上传大小
        var size = "{$upload_max_filesize}";
        if (file.size > size) {
            showErrorMsg('视频大小超过限制,可在附件设置中修改');
            return false;
        }

        // 执行上传
        layer_loading('上传七牛云');
        $.ajax({
            type: 'POST',
            url: '__ROOT_DIR__/index.php?m=plugins&c=Qiniuyun&a=qiniu_upload',
            data: {_ajax: 1},
            dataType: "JSON",
            success: function(res1) {
                if (1 == res1.code) {
                    var token  = res1.data.token;
                    var formData = new FormData();
                    formData.append('file', file);
                    formData.append('token', token);

                    fileName = res1.data.filePath + fileExt;
                    formData.append('key', fileName);
                    $.ajax({
                        url: res1.data.uphost,
                        type: 'POST',
                        dataType: 'JSON',
                        data: formData,
                        timeout: 1200000,
                        cache: false,
                        processData: false,
                        contentType: false,
                        xhr: function () {
                            myXhr = $.ajaxSettings.xhr();
                            if (myXhr.upload) {
                                myXhr.upload.addEventListener('progress', function(e){
                                    var curr = e.loaded;
                                    var total = e.total;
                                    process = parseInt(curr / total * 100);
                                    $("#upload_video_litpic_qiniu").text('上传中...'+process+"%");
                                });
                            }
                            return myXhr;
                        },
                        success: function(res2) {
                            layer.closeAll();
                            $("#upload_video_litpic_qiniu").text('上传成功');
                            setTimeout(function() {
                                $('#upload_video_litpic_qiniu').text('七牛云上传');
                            }, 2000);
                            var video_url = res1.data.domain + "/" + res2.key;
                            $("#video_litpic").empty().val(video_url);
                        },
                        error: function(e) {
                            layer.closeAll();
                            showErrorMsg(e.responseText);
                            return false;
                        }
                    });
                } else {
                    layer.closeAll();
                    showErrorMsg(res1.msg);
                }
            },
            error: function(e) {
                layer.closeAll();
                showErrorMsg(e.responseText);
            }
        });
    }

    // 阿里云OSS上传
    function upload_video_litpic_oss(e) {
        // 获取文件路径名
        var file = $(e)[0].files[0];
        
        // 验证上传格式
        var fileName = file.name;
        var fileExt = fileName.substr(fileName.lastIndexOf('.')).toLowerCase();
        var ext = judgeExt(fileExt);
        if (ext == -1) {
            showErrorMsg('不支持选中的视频格式,可在附件设置中修改');
            return false;
        }

        // 验证上传大小
        var size = "{$upload_max_filesize}";
        if (file.size > size) {
            showErrorMsg('视频大小超过限制,可在附件设置中修改');
            return false;
        }

        // 执行上传
        layer_loading('上传阿里云OSS');
        $.ajax({
            type: 'POST',
            url: '__ROOT_DIR__/index.php?m=plugins&c=AliyunOss&a=oss_upload',
            data: {_ajax: 1},
            dataType: "JSON",
            success: function(res1){
                if (1 == res1.code){
                    fileName = res1.data.filePath + fileExt;
                    //组装发送数据
                    var request = new FormData();
                    request.append("OSSAccessKeyId",res1.data.accessid);
                    request.append("policy",res1.data.policy);
                    request.append("Signature",res1.data.signature);
                    request.append("key",fileName);
                    request.append("success_action_status",201);
                    request.append('file', file);

                    $.ajax({
                        url : res1.data.host,
                        data : request,
                        processData: false,
                        cache: false,
                        contentType: false,
                        dataType: 'xml',
                        type : 'post',
                        xhr: function () {
                            myXhr = $.ajaxSettings.xhr();
                            if (myXhr.upload) {
                                myXhr.upload.addEventListener('progress', function(e){
                                    var curr = e.loaded;
                                    var total = e.total;
                                    var process = parseInt(curr / total * 100);
                                    $("#upload_video_litpic_oss").text('上传中...'+process+"%");
                                });
                            }
                            return myXhr;
                        },
                        success : function(data) {
                            layer.closeAll();
                            var res = $(data).find('PostResponse');
                            if (res) {
                                var key = res.find('Key').text();
                                $("#upload_video_litpic_oss").text('上传成功');
                                setTimeout(function() {
                                    $('#upload_video_litpic_oss').text('oss上传');
                                }, 2000);
                                var video_url = res1.data.domain + "/" + key;
                                $("#video_litpic").val(video_url);
                            } else {
                                $("#upload_video_litpic_oss").text('上传失败');
                                setTimeout(function() {
                                    $('#upload_video_litpic_oss').text('oss上传');
                                }, 2000);
                            }
                        },
                        error : function(e) {
                            layer.closeAll();
                            console.log(e.responseText);
                        }
                    });
                } else {
                    layer.closeAll();
                    showErrorMsg(res1.msg);
                }
            },
            error: function(e) {
                layer.closeAll();
                showErrorMsg(e.responseText);
            }
        });
    }

    // 腾讯云COS上传
    function upload_video_litpic_cos(e) {
        // 获取文件路径名
        var file = $(e)[0].files[0];
        
        // 验证上传格式
        var fileName = file.name;
        var fileExt = fileName.substr(fileName.lastIndexOf('.')).toLowerCase();
        var ext = judgeExt(fileExt);
        if (ext == -1) {
            showErrorMsg('不支持选中的视频格式，可在附件设置中修改');
            return false;
        }

        // 验证上传大小
        var size = "{$upload_max_filesize}";
        if (file.size > size) {
            showErrorMsg('视频大小超过限制，可在附件设置中修改');
            return false;
        }

        // 上传参数
        var formData = new FormData();
            formData.append('file', file);
            formData.append('file_ext', fileExt);

        // 执行上传
        $.ajax({
            type: 'post',
            url: '__ROOT_DIR__/index.php?m=plugins&c=Cos&a=cos_upload&_ajax=1',
            data: formData,
            contentType: false,
            processData: false,
            dataType: 'json',
            xhr: function () {
                myXhr = $.ajaxSettings.xhr();
                if (myXhr.upload) {
                    myXhr.upload.addEventListener('progress', function(e){
                        var curr = e.loaded;
                        var total = e.total;
                        process = parseInt(curr / total * 100);
                        if (100 == process) {
                            process = 99;
                            layer_loading('上传腾讯云COS');
                        }
                        $("#upload_video_litpic_cos").text('上传中...'+process+"%");
                        
                    });
                }
                return myXhr;
            },
            success: function(res) {
                layer.closeAll();
                if (1 == res.code) {
                    $("#video_litpic").val(res.data.url);
                    $("#upload_video_litpic_cos").text('上传成功');
                } else {
                    $("#upload_video_litpic_cos").text('上传失败');
                }
                setTimeout(function() {
                    $('#upload_video_litpic_cos').text('cos上传');
                }, 2000);
            },
            error: function(e) {
                layer.closeAll();
                showErrorMsg(e.responseText);
            }
        });
    }

    // 本地上传
    function upload_video_litpic_local(e) {
        // 获取文件路径名
        var file = $(e)[0].files[0];

        // 验证上传格式
        var fileName = file.name;
        var fileExt = fileName.substr(fileName.lastIndexOf('.')).toLowerCase();
        var ext = judgeExt(fileExt);
        if (ext == -1) {
            showErrorMsg('不支持选中的视频格式,可在附件设置中修改');
            return false;
        }

        // 验证上传大小
        var size = "{$upload_max_filesize}";
        if (file.size > size) {
            showErrorMsg('视频大小超过限制,可在附件设置中修改');
            return false;
        }

        // 定义上传数据
        var formData = new FormData();
        formData.append('file', file);

        // 执行上传
        layer_loading('上传本地');
        $.ajax({
            type: 'post',
            url : "{:url('Ueditor/upVideo', ['savepath'=>'media'])}",
            data: formData,
            contentType: false,
            processData: false,
            dataType: 'json',
            xhr: function () {
                myXhr = $.ajaxSettings.xhr();
                if (myXhr.upload) {
                    myXhr.upload.addEventListener('progress', function(e){
                        var curr = e.loaded;
                        var total = e.total;
                        process = parseInt(curr / total * 100);
                        $("#upload_video_litpic_local").html('上传中...'+process+"%");
                    });
                }
                return myXhr;
            },
            success: function (res) {
                layer.closeAll();
                $("#upload_video_litpic_local").html('上传成功');
                setTimeout(function() {
                    $('#upload_video_litpic_local').html('本地上传');
                }, 2000);
                if ("SUCCESS" == res.state) {
                    $("#video_litpic").val(res.url);
                } else {
                    showErrorMsg(res.msg);
                }
            }
        });
    }

    // 验证格式
    function judgeExt(ext) {
        var type = "{$media_type}";
        var extArr = [];
        extArr = type.split("|");
        var ext = ext.replace(".","");
        return extArr.indexOf(ext);
    }
</script>

<script type="text/javascript">
    // 勾选新窗口打开链接
    function CheckedTarget(t){
        if ($(t).is(':checked')) {
            $(t).parent().find('input[name="img_target[]"]').val(1);
        }else{
            $(t).parent().find('input[name="img_target[]"]').val(0);
        }
    }
    // 鼠标事件，加载查看大图和更新图片
    function upimgMouseover(obj)
    {
        $(obj).find('div.icaction').show();
        $(obj).find('div.cover-bg').show();
    }
    function upimgMouseout(obj)
    {
        $(obj).find('div.icaction').hide();
        $(obj).find('div.cover-bg').hide();
    }

    // 检测广告名称是否存在重复
    function DetectionTitleRepeat(obj) {
        // 当前广告ID
        var id = $('#1615775137_id').val();
        // 传入的广告名称
        var title = $(obj).val();
        // 执行检测
        $.ajax({
            type: 'post',
            url : "{:url('AdPosition/detection_title_repeat', ['_ajax'=>1])}",
            data: {id: id, title: title},
            dataType: 'json',
            success: function(res) {
                if (0 == res.code) {
                    $('#title_tips').show().html(res.msg);
                } else {
                    $('#title_tips').hide().html('');
                }
            },
            error: function(e){
                layer.closeAll();
                showErrorAlert(e.responseText);
            }
        });
    }

    // 判断输入框是否为空
    function checkForm(){
        if($('input[name=title]').val() == ''){
            layer.msg('广告位名称不能为空！', {icon: 2,time: 1000});
            return false;
        }
        layer_loading('正在处理');
        $.ajax({
            type : 'post',
            url : "{:url('AdPosition/edit', ['_ajax'=>1])}",
            data : $('#post_form').serialize(),
            dataType : 'json',
            success : function(res){
                layer.closeAll();
                if (res.code == 1) {
                    layer.msg(res.msg, {icon: 1, shade: layer_shade, time: 1000}, function(){
                        window.location.reload();
                    });
                } else {
                    showErrorMsg(res.msg);
                }
            },
            error: function(e){
                layer.closeAll();
                showErrorAlert(e.responseText);
            }
        });
    }

    // 获取点击更新图片的ID并加载隐藏域
    function LoadImagesId(id){
        // 加载ID到隐藏域
        $('#ImagesId').val(id);
        // 调用图片上传JS
        GetUploadify(1,'','allimg','UpdataImages');
    }

    // 更新图片
    function UpdataImages(path){
        // 获取点击的ID
        var id = $('#ImagesId').val();
        // 加载图片到显示层
        $("#"+id+"_Id").attr('src', path);
        // 加载图片到提交的隐藏域
        $("#"+id+"_Litpic").val(path);
    }

    // 显示信息输入框
    function ShowInput(id) {
        var obj = $("#"+id+"_ShowInput");
        var showOrHide = obj.attr('data-showOrHide');
        if ('hide' == showOrHide) {
            obj.show().attr('data-showOrHide', 'show');
        } else {
            obj.hide().attr('data-showOrHide', 'hide');
        }
    }

    // 隐藏信息输入
    function HideInput(id) {
        $("#"+id+"_ShowInput").hide().attr('data-showOrHide', 'hide');
    }

    // 上传图集相册回调函数
    function imgupload_call_back(paths){
        var last_div = $(".images_upload_tpl").html();
        var inputs   = $('.span_input input');
        var timestamp = 0;

        // 图片数据处理
        for (var i=0; i<paths.length; i++) {
            // 生成新的时间戳
            timestamp = (new Date()).getTime();
            // 插入一个 新图片
            $(".images_upload:eq(0)").before(last_div);
            // 处理他的链接地址
            $(".images_upload:eq(0)").find('span:eq(0)').attr('onclick', "Images('"+paths[i]+"', 900, 600);");
            // 处理信息显示隐藏按钮
            $(".images_upload:eq(0)").find('span:eq(1)').attr('onclick', "ShowInput('"+timestamp+"');");
            // 处理他的图片路径
            $(".images_upload:eq(0)").find('img').attr('src', paths[i]);
            // 处理删除按钮
            $(".images_upload:eq(0)").find('a:eq(1)').attr('onclick', "ClearPicArr(this, '"+paths[i]+"', 0);");
            // 处理图片信息框ID
            $(".images_upload:eq(0)").find('.load_input:eq(0)').attr('id', timestamp+'_ShowInput');
            
            // 处理图片路径及隐藏域
            if (inputs.length > 0) {
                // 修改隐藏域
                $(".images_upload:eq(0)").find('input:eq(0)').attr('name', 'img_id[]').attr('value', '');
                $(".span_input:eq(0)").find('input:eq(0)').attr('name', 'img_litpic[]').attr('value', paths[i]);
                // 处理标题
                $(".images_upload:eq(0)").find('textarea:eq(0)').attr('name', 'img_title[]');
                // 处理跳转链接
                $(".images_upload:eq(0)").find('textarea:eq(1)').attr('name', 'img_links[]');
                // 处理内容描述
                $(".images_upload:eq(0)").find('textarea:eq(2)').attr('name', 'img_intro[]');
                // 处理新窗口选项
                $(".images_upload:eq(0)").find('div.operation a:eq(0)').html("<label><input type='checkbox' title='在新窗口打开' onclick='CheckedTarget(this);'/>新窗口<input type='hidden' name='img_target[]' value='0'></label>");
                // 处理收回按钮
                $(".images_upload:eq(0)").find('div.operation a:eq(2)').attr('onclick', "HideInput('"+timestamp+"');").attr('title', "收回").html('<i style="font-size:16px;" class="fa fa-angle-double-up"></i>');
            }
        }
    }

    // 上传之后删除组图input
    function ClearPicArr(obj, path, id) {
        // 删除数据库记录
        $.ajax({
            type:'POST',
            url:"{:url('AdPosition/del_imgupload')}",
            data:{del_id:id,_ajax:1},
            success:function(){
                $(obj).parent().parent().parent().remove(); // 删除完服务器的, 再删除 html上的图片
                $.ajax({
                    type:'POST',
                    url:"{:url('Uploadimgnew/delupload')}",
                    data:{action:"del", filename:path,_ajax:1},
                    success:function(){}
                }); 
            }
        });
    }

    // 代码调用js
    function copyToClipBoard(id) {
        var adstr = "{eyou:ad aid='" + id + "'}\r\n   <a href='{$"+"field.links}' {$"+"field.target}><img src='{$"+"field.litpic}' alt='{$"+"field.title}' /></a>\r\n   广告注释：{$"+"field.intro}\r\n{/eyou:ad";
        var contentdiv = '<div class="dialog_content" style="margin: 0px; padding: 0px;"><dl style="padding:10px 30px;line-height:30px"><dd>标签 ad 调用:</dd>';
        contentdiv += '<textarea rows="4" cols="60" style="width:400px;height:100px;">' + adstr + '}</textarea>';
        contentdiv += '<dd style="border-top: dotted 1px #E7E7E7; color: #F60;">请将对应标签代码复制并粘贴到对应模板文件中！</dd></dl></div>';
        layer.open({
            title: '代码调用',
            type: 1,
            shade: layer_shade,
            skin: 'layui-layer-demo',
            area: ['480px', '260px'], //宽高
            content: contentdiv
        });
    }

    // 图集相册的拖动排序相关 js
    $( ".sort-list" ).sortable({
        start: function( event, ui) {
        
        }
        ,stop: function( event, ui ) {

        }
    });
    //因为他们要拖动，所以尽量设置他们的文字不能选择。  
    // $( ".sort-list" ).disableSelection();
</script>

{include file="public/footer" /}