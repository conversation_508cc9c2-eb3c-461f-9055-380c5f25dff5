<!doctype html>
<html>
<head>
    <meta http-equiv="content-type" content="text/html; charset=UTF-8">
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <!-- Apple devices fullscreen -->
    <meta name="apple-mobile-web-app-capable" content="yes">
    <!-- Apple devices fullscreen -->
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">
    <link href="__SKIN__/css/main.css?v={$version}" rel="stylesheet" type="text/css">
    <link href="__SKIN__/font/css/font-awesome.min.css?v={$version}" rel="stylesheet"/>
    <link href="__SKIN__/font/css/iconfont.css?v={$version}" rel="stylesheet"/>
    <link href="__SKIN__/css/index.css?v={$version}" rel="stylesheet" type="text/css">
    <!--[if IE 7]>
    <link rel="stylesheet" href="__SKIN__/font/css/font-awesome-ie7.min.css?v={$version}">
    <![endif]-->
    <link href="__SKIN__/css/diy_style.css?v={$version}" rel="stylesheet" type="text/css"/>
    {include file="public/theme_css" /}
    <script type="text/javascript">
        var eyou_basefile = "{$Request.baseFile}";
        var module_name = "{$Think.const.MODULE_NAME}";
        var __root_dir__ = "__ROOT_DIR__";
        var __lang__ = "{$admin_lang}";
        var __main_lang__ = "{$main_lang}";
        var VarSecurityPatch = {$security_patch|default=0};
    </script>
    <script type="text/javascript" src="__STATIC__/common/js/jquery.min.js?v={$version}"></script>
    <script type="text/javascript" src="__PUBLIC__/plugins/layer-v3.1.0/layer.js?v={$version}"></script>
    <script src="__SKIN__/js/upgrade.js?v={$version}"></script>
    <script src="__SKIN__/js/global.js?v={$version}"></script>
</head>
<body style="background-color:#F4F4F4;padding:0px; overflow: auto;">
<div id="toolTipLayer" style="position: absolute; z-index: 9999; display: none; visibility: visible; left: 95px; top: 573px;"></div>
<div id="append_parent"></div>
<div id="ajaxwaitid"></div>
{if condition="empty($system_explanation_welcome_2)"}
<div id="explanation_welcome" style="margin:10px 10px 0px 10px;">
    {if condition='empty($system_explanation_welcome_2)'}
    <div class="explanation" style="color: rgb(44, 188, 163); background-color: #fff!important;  margin-top: 10px;">
        <div class="title checkZoom" data-type="2">
            <span title="不再提示" style="display: block;"></span>
        </div>
        <ul>
            <li style="color: red;">后台登录密码强度：{$admin_login_pwdlevel|getPasswordLevelTitle}，容易被暴力破解，请及时
            <a href="javascript:void(0);" data-href="{:url('Admin/admin_edit', ['id'=>$Think.session.admin_info.admin_id,'iframe'=>1])}" onclick="openFullframe(this, '管理员-修改密码');">【修改密码】</a>提高安全性。</li>
        </ul>
    </div>
    {/if}
</div>
{/if}
<div class="warpper">
    <div class="content start_content">
        <div class="contentWarp">
            <div class="index_box" id="quick_menu_box" style="display: none;">
                <div class="info_count">
                     <h3><i class="iconfont e-kuaijiedaohang"></i>快捷导航</h3>
                     <div class="container-fluid">
                         <ul>
                            {volist name='$quickMenu' id='vo'}
                                {if condition="is_check_access($vo['controller'].'@'.$vo['action'])"}
                                <li class="quick_menu">
                                    <a href="javascript:void(0);" onclick="GoLocation(this);" data-href="{:url($vo.controller.'/'.$vo.action, $vo.vars)}"><p class="navs">{$vo.title}</p></a>
                                </li>
                                {/if}
                            {/volist}
                            {eq name="'Index@ajax_quickmenu'|is_check_access" value="1"}
                            <li>
                               <a href="javascript:void(0);" id="quickMenuAdd"><p class="navs"><i style="font-size: 20px;" class="iconfont e-tianjia"></i></p></a>
                            </li>
                            {/eq}
                         </ul>
                     </div>
                </div>
            </div>
            <script type="text/javascript">
                $(function(){
                    if ($('li.quick_menu').length > 0) {
                        $('#quick_menu_box').show();
                    }
                });
            </script>
            <div class="index_box" id="content_total_box" style="display: none;">
                <div class="info_count">
                     <h3><i class="iconfont e-neirongtongji"></i>内容统计</h3>
                     <div class="container-fluid">
                         <ul>
                            {volist name='$contentTotal' id='vo' length='9'}
                                {if condition="is_check_access($vo['controller'].'@'.$vo['action'])"}
                                <li class="content_total">
                                   <a href="javascript:void(0);" onclick="GoLocation(this);" data-href="{:url($vo.controller.'/'.$vo.action, $vo.vars)}">
                                       <h2>{$vo.title}</h2>
                                       <p title="{$vo.tips|default=''}"><cite>{$vo.total|default='0'}</cite></p>
                                   </a>
                                </li>
                                {/if}
                            {/volist}
                            {eq name="'Index@ajax_content_total'|is_check_access" value="1"}
                            <li>
                               <a href="javascript:void(0);" id="contentTotalAdd">
                                   <h2>添加统计</h2>
                                   <p><cite><i class="iconfont e-tianjia"></i></cite></p>
                               </a>
                            </li>
                            {/eq}
                         </ul>
                     </div>
                </div>
            </div>
            <script type="text/javascript">
                $(function(){
                    if ($('li.content_total').length > 0) {
                        $('#content_total_box').show();
                    }
                });
                function GoLocation(obj) {
                    layer_loading('正在加载');
                    var url = $(obj).data('href');
                    if (url.indexOf('&c=ShopProduct&a=index') > -1) {
                        top.$('#Shop_home').attr('data-click', true).attr('data-click_url', url).click();
                    } else {
                        window.location.href = $(obj).data('href');
                    }
                }
            </script>
            <div class="section system_section" style="float: none;width: inherit;">
                <div class="system_section_con">
                    <div class="sc_title" style="padding: 26px 0 14px;">

                        <h3><i class="iconfont e-xitongxinxi"></i>系统信息</h3>
                    </div>
                    <div class="sc_warp" id="system_warp" style="display: block;">
                        <table cellpadding="0" cellspacing="0" class="system_table">
                            <tbody>
                                <tr>
                                    <td class="gray_bg">系统更新：</td>
                                    <td id="td_upgrade_msg">
                                        <div id="upgrade_filelist" style="display:none;"></div>
                                        <div id="upgrade_intro" style="display:none;"></div>
                                        <div id="upgrade_notice" style="display:none;"></div>
                                        <a href="javascript:void(0);" id="a_upgrade" data-version="" data-max_version="" data-curent_version="{$sys_info.curent_version|default='v1.0'}" data-iframe="workspace" title="" data-tips_url="{:url('Upgrade/setPopupUpgrade')}" data-upgrade_url="{:url('Upgrade/OneKeyUpgrade')}" data-check_authority="{:url('Upgrade/check_authority')}">{if condition='!empty($security_patch)'}正在版本检测中……{else/}{eq name="upgrade" value="true"}正在版本检测中……{else/}已是最新版{/eq}{/if}</a>
                                    </td>
                                    <td class="gray_bg">当前版本：</td>
                                    <td>{$sys_info.curent_version|default='v1.0'}</td>
                                </tr>
                                <tr>
                                    <td class="gray_bg">网站名称：</td>
                                    <td>{$sys_info.web_name|default='Eyoucms企业网站管理系统'}</td>
                                    <td class="gray_bg">版权所有：</td>
                                    <td>{notempty name="$is_eyou_authortoken"}<a href="https://www.eyoucms.com/buy/" target="_blank" style="color: #454545;">盗版必究</a>{else /}正版软件{/notempty}&nbsp;<a href="javascript:void(0);" onclick="top.openItem('Index|authortoken|is_force|1');">校验</a></td>
                                </tr>
                                {notempty name="$is_eyou_authortoken"}
                                <tr>
                                    <td class="gray_bg">更新日志：</td>
                                    <td><a href="https://www.eyoucms.com/rizhi/" target="_blank">查看</a></td>
                                    <td class="gray_bg">帮助中心:</td>
                                    <td><a href="https://www.eyoucms.com/ask/" target="_blank">查看</a></td>
                                </tr>
                                {/notempty}
                            </tbody>
                        </table>
                    </div>
                </div>
                <div class="system_section_con">
                    <div class="sc_title" style="padding: 26px 0 14px;">

                        <h3><i class="iconfont e-fuwuqixinxi"></i>服务器信息</h3>
                    </div>
                    <div class="sc_warp" id="system_warp" style="display: block;padding-bottom: 20px;">
                        <table cellpadding="0" cellspacing="0" class="system_table">
                            <tbody><tr>
                                <td class="gray_bg">服务器系统：</td>
                                <td>{$sys_info.os}</td>
                                <td class="gray_bg">网站域名/IP：</td>
                                <td>{$sys_info.domain} [ {$sys_info.ip} ]</td>
                            </tr>
                            <tr>
                                <td class="gray_bg">服务器环境：</td>
                                <td style="line-height: 28px;padding-right: 20px;">{$sys_info.web_server}</td>
                                <td class="gray_bg">PHP 版本：</td>
                                <td>{$sys_info.phpv}</td>
                            </tr>
                            <tr>
                                <td class="gray_bg">Mysql 版本：</td>
                                <td>{$sys_info.mysql_version}</td>
                                <td class="gray_bg">GD 版本：</td>
                                <td>{$sys_info.gdinfo}</td>
                            </tr>
                            <tr>
                                <td class="gray_bg">文件上传限制：</td>
                                <td>{$sys_info.fileupload}</td>
                                <td class="gray_bg">最大占用内存：</td>
                                <td>{$sys_info.memory_limit}</td>
                            </tr>
                            <tr>
                                <td class="gray_bg">POST限制：</td>
                                <td>{$sys_info.postsize|default='unknown'}</td>
                                <td class="gray_bg">最大执行时间：</td>
                                <td>{$sys_info.max_ex_time}</td>
                            </tr>
                            <tr>
                                <td class="gray_bg">Zip支持：</td>
                                <td>{$sys_info.zip}</td>
                                <td class="gray_bg">Zlib支持：</td>
                                <td>{$sys_info.zlib}</td>
                            </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<div id="footer" style="position: static; bottom: 0px; font-size:14px;">
    <p>
        <b>{$global.web_copyright|htmlspecialchars_decode}</b>
    </p>
</div>
<script type="text/javascript">
    $(function () {
        if (1 == VarSecurityPatch) {
            checkUpgradeSecurityVersion();
        } else {
            {eq name="upgrade" value="true"}
            check_upgrade_version();
            {/eq}
        }

        $.get("{:url('Ajax/welcome_handle', ['_ajax'=>1])}"); // 进入欢迎页面需要异步处理的业务
        check_language_tips();

        // 检测语言版本
        function check_language_tips()
        {
            if (__main_lang__ != __lang__) {
                var language_title = $('#language_title', window.parent.document).html();
                layer.msg('当前后台已切换至【'+language_title+'】编辑状态！', {time:3000});
            }
        }

        // 检测系统安全补丁更新弹窗
        function checkUpgradeSecurityVersion() {
            $.ajax({
                type : "GET",
                url  : "{:url('Ajax/check_upgrade_version', ['_ajax'=>1])}",
                data : {},
                dataType : "JSON",
                success: function(res) {
                    if (1 == res.code) {
                        if (2 == res.data.code) {
                            /*显示顶部导航更新提示*/
                            try {
                                $("#upgrade_filelist", window.parent.document).html(res.data.msg.upgrade);
                                $("#upgrade_intro", window.parent.document).html(res.data.msg.intro);
                                $("#upgrade_notice", window.parent.document).html(res.data.msg.notice);
                                $('#a_upgrade', window.parent.document).attr('data-version',res.data.msg.key_num).attr('data-max_version',res.data.msg.max_version).show();
                            } catch(e) {}

                            $('#upgrade_filelist').html(res.data.msg.upgrade);
                            $('#upgrade_intro').html(res.data.msg.intro);
                            $('#upgrade_notice').html(res.data.msg.notice);
                            $('#a_upgrade').attr('data-version', res.data.msg.key_num).attr('data-max_version', res.data.msg.max_version).attr('title', res.data.msg.tips);
                            $('#a_upgrade').html('检测到安全补丁包'+res.data.msg.key_num+'[点击查看]').css('color', '#F00');
                            /* END */

                            var webShowPopupUpgrade = {$web_show_popup_upgrade|default=1};
                            var adminInfoRoleID = {$Think.session.admin_info.role_id|default=0};
                            var adminInfoAuthRoleInfoOnlineUpdate = {$Think.session.admin_info.auth_role_info.online_update|default=0};
                            if (-1 != webShowPopupUpgrade && (0 >= adminInfoRoleID || 1 == adminInfoAuthRoleInfoOnlineUpdate)) {
                                btn_upgrade($("#a_upgrade"), 1);
                            }
                        } else if (0 == res.data.code) {
                            layer.alert(res.data.msg, {title:false, closeBtn:0});
                        } else {
                            $('#td_upgrade_msg').html(res.data.msg);
                        }
                    }
                }
            });
        }

        // 版本检测更新弹窗
        function check_upgrade_version() {
            $.ajax({
                type : "GET",
                url  : "{:url('Ajax/check_upgrade_version', ['_ajax'=>1])}",
                data : {},
                dataType : "JSON",
                success: function(res) {
                    if (1 == res.code) {
                        if (2 == res.data.code) {
                            /*显示顶部导航更新提示*/
                            try {
                                $("#upgrade_filelist", window.parent.document).html(res.data.msg.upgrade);
                                $("#upgrade_intro", window.parent.document).html(res.data.msg.intro);
                                $("#upgrade_notice", window.parent.document).html(res.data.msg.notice);
                                $('#a_upgrade', window.parent.document).attr('data-version',res.data.msg.key_num).attr('data-max_version',res.data.msg.max_version).show();
                            } catch(e) {}

                            $('#upgrade_filelist').html(res.data.msg.upgrade);
                            $('#upgrade_intro').html(res.data.msg.intro);
                            $('#upgrade_notice').html(res.data.msg.notice);
                            $('#a_upgrade').attr('data-version', res.data.msg.key_num).attr('data-max_version', res.data.msg.max_version).attr('title', res.data.msg.tips);
                            $('#a_upgrade').html('检测到新版本'+res.data.msg.key_num+'[点击查看]').css('color', '#F00');
                            /* END */

                            {if condition="-1 != $web_show_popup_upgrade AND (0 >= $Think.session.admin_info.role_id OR 1 == $Think.session.admin_info.auth_role_info.online_update)"}
                                btn_upgrade($("#a_upgrade"), 1);
                            {/if}
                        } else {
                            $('#td_upgrade_msg').html(res.data.msg);
                        }
                    }
                }
            });
        }
    });

    $(function() {
        //操作提示缩放动画
        $(".checkZoom").click(function(){
            $(this).parent().animate({
                color: "#FFF",
                backgroundColor: "#4FD6BE",
                width: "0",
                height: "0",
            },300,function(){
                $(this).remove();
            });
            if(1 >= $('#explanation_welcome').find('div.explanation').length) {
                $('#explanation_welcome').remove();
            }
            var url = eyou_basefile+"?m=admin&c=Ajax&a=explanation_welcome&type="+$(this).attr('data-type')+"&lang="+__lang__+"&_ajax=1";
            $.get(url);
        });

        checkInlet(); // 自动检测隐藏index.php
    });

    // 自动检测隐藏index.php
    function checkInlet() {
        layer.open({
            type: 2,
            title: false,
            area: ['0px', '0px'],
            shade: 0.0,
            closeBtn: 0,
            shadeClose: true,
            content: '//{$website_host}__ROOT_DIR__/api/Rewrite/setInlet.html',
            success: function(layero, index){
                layer.close(index);
                var body = layer.getChildFrame('body', index);
                var content = body.html();
                if (content.indexOf("Congratulations on passing") == -1)
                {
                    $.ajax({
                        type : "POST",
                        url  : "__ROOT_DIR__/index.php?m=api&c=Rewrite&a=setInlet&_ajax=1",
                        data : {seo_inlet:0},
                        dataType : "JSON",
                        success: function(res) {

                        }
                    });
                }
            }
        });
    }

    // 新增内容统计
    $('#contentTotalAdd').click(function(){
        //iframe窗
        var iframes = layer.open({
            type: 2,
            title: '内容统计管理',
            fixed: true, //不固定
            shadeClose: false,
            shade: layer_shade,
            // maxmin: false, //开启最大化最小化按钮
            area: ['550px', '320px'],
            content: "{:url('Index/ajax_content_total')}"
        });
    });

    // 新增快捷导航
    $('#quickMenuAdd').click(function(){
        //iframe窗
        var iframes = layer.open({
            type: 2,
            title: '快捷导航管理',
            fixed: true, //不固定
            shadeClose: false,
            shade: layer_shade,
            // maxmin: false, //开启最大化最小化按钮
            area: ['550px', '320px'],
            content: "{:url('Index/ajax_quickmenu')}"
        });
    });

    /**
     * 更新组件库
     * @return {[type]} [description]
     */
    var is_update_component_access = {$is_update_component_access|default=0};
    function update_component_access()
    {
        if (1 == is_update_component_access) {
            $.ajax({
                type : 'post',
                url : eyou_basefile+'?m=admin&c=Diyminipro&a=ajax_syn_component_access&lang='+__lang__,
                data : {mini_id:0, _ajax:1},
                dataType : 'json',
                success : function(res){
                    if(res.code == 1) {
                        console.log(res.msg);
                    }
                }
            });
        }
    }
    update_component_access();
</script>
{include file="public/footer" /}