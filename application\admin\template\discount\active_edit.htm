{include file="public/layout" /}
<div class="bodystyle">
<div id="append_parent"></div>
<div id="ajaxwaitid"></div>
<div class="page">
    <div class="flexigrid">
        <div class="ncap-form-default">
            <form class="form-horizontal" id="post_form" action="{:url('Discount/active_edit')}" method="post">
                <dl class="row">
                    <dt class="tit">
                        <label for="active_name"><em>*</em>活动名称</label>
                    </dt>
                    <dd class="opt">
                        <input type="text" class="input-txt" id="active_name" name="active_name" value="{$info.active_name}" placeholder="请输入活动名称" autocomplete="off">
                    </dd>
                </dl>
                <dl class="row">
                    <dt class="tit">
                        <label for="limit"><em>*</em>活动日期</label>
                    </dt>
                    <dd class="opt">
                        <input type="text" class="input-txt" id="start_date" name="start_date" value="{$info.start_date|date='Y-m-d H:i:s',###}" autocomplete="off" style="width: 150px !important;" readonly> 至
                        <input type="text" class="input-txt" id="end_date" name="end_date" value="{$info.end_date|date='Y-m-d H:i:s',###}" autocomplete="off" style="width: 150px !important;">
                    </dd>
                </dl>

                <dl class="row">
                    <dt class="tit">
                        <label for="total_stock"><em>*</em>选择商品 </label>
                    </dt>
                    <dd class="opt">
                        <a href="javascript:void(0);" onclick="choGoods();" class="ncap-btn ncap-btn-green"><i class="layui-icon">&#xe608;</i>选择限时折扣商品</a>
                        <p class="notic2">注：每个活动场次中出售的限时折扣商品，此处非必填，可在场次管理中单独设置</p>
                        <div class="{empty name='$goods'} none {/empty}" id="goods_show_list" style="margin-top: 15px;width: 850px;">
                            <div class="flexigrid">
                                <div class="hDiv">
                                    <div class="hDivBox">
                                        <table cellspacing="0" cellpadding="0" style="width: 100%;">
                                            <thead>
                                                <tr>
                                                    <th abbr="id" axis="col5" class="w60">
                                                        <div class="tc">商品ID</div>
                                                    </th>
                                                    <th align="center" abbr="article_title" axis="col3" class="w40">
                                                        <div class="tc">封面图</div>
                                                    </th>
                                                    <th align="left" abbr="article_title" axis="col3" class="">
                                                        <div style="text-align: left; padding-left: 10px;" class="">商品标题</div>
                                                    </th>
                                                    <th abbr="article_time" axis="col6" class="w60">
                                                        <div class="tc">操作</div>
                                                    </th>
                                                </tr>
                                            </thead>
                                        </table>
                                    </div>
                                    <div class="bDiv" style="height: auto;">
                                        <div id="flexigrid" cellpadding="0" cellspacing="0" border="0">
                                            <table style="width: 100%;">
                                                <tbody id="Template">
                                                {empty name="$goods"}
                                                    <tr id="noneGoods">
                                                        <td class="no-data" align="center" axis="col0" colspan="50">
                                                            <i class="fa fa-exclamation-circle"></i>没有选择限时折扣商品
                                                        </td>
                                                    </tr>
                                                {else/}
                                                    {foreach name="$goods" item="vo" key="k"}
                                                        <tr class="tr goods_show_list_div goods_show_list_div_{$vo.discount_goods_id}">
                                                            <td class="sort">
                                                                <div class="w60 tc">
                                                                    {$vo.discount_goods_id}
                                                                    <input type="hidden" name="goods[discount_gid][]" value="{$vo.discount_goods_id}">
                                                                    <input type="hidden" name="goods[aid][]" value="{$vo.aid}">
                                                                </div>
                                                            </td>
                                                            <td class="w40">
                                                                <div class="tc">
                                                                    <img width="40" height="40" src="{$vo.litpic|get_default_pic=###}">
                                                                </div>
                                                            </td>
                                                            <td class="" style="width: 100%;">
                                                                <div class="tl" style="padding-left: 10px;">
                                                                    {$vo.title}
                                                                </div>
                                                            </td>
                                                            <td class="">
                                                                <div class="w60 tc">
                                                                    <a class="btn red" href="javascript:void(0);" onclick="del('{$vo.discount_goods_id}');">删除</a>
                                                                </div>
                                                            </td>
                                                        </tr>
                                                    {/foreach}
                                                {/empty}
                                                </tbody>
                                            </table>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                     </dd>
                </dl>
                <dl class="row">
                    <dt class="tit">
                        <label for="buy_type">限购次数</label>
                    </dt>
                    <dd class="opt">
                        <label><input type="radio" name="limit_type" value="1" {eq name='$info.limit_type' value='1'}checked="checked"{/eq} onclick="chooseUseType(this);" />&nbsp;不限购</label>
                        <br>
                        <label><input type="radio" name="limit_type" value="2" {eq name='$info.limit_type' value='2'}checked="checked"{/eq} onclick="chooseUseType(this);" />&nbsp;活动期内每人最多购买</label>
                        <input type="text" id="num_2" {neq name='$info.limit_type' value='2'} value="1" disabled="disabled"{else/}value="{$info.limit}"{/neq} class="input-txt" onpaste="this.value=this.value.replace(/[^\d.]/g, '');" onkeyup="this.value=this.value.replace(/[^\d.]/g, '');" style="width: 50px !important;">&nbsp;件
                        <br>
                        <label><input type="radio" name="limit_type" value="3" {eq name='$info.limit_type' value='3'}checked="checked"{/eq} onclick="chooseUseType(this);" />&nbsp;活动期内每人每天最多购买</label>
                        <input type="text" id="num_3" {neq name='$info.limit_type' value='3'} value="1" disabled="disabled"{else/}value="{$info.limit}"{/neq}  class="input-txt" onpaste="this.value=this.value.replace(/[^\d.]/g, '');" onkeyup="this.value=this.value.replace(/[^\d.]/g, '');" style="width: 50px !important;">&nbsp;件
                        <p class="notic"></p>
                    </dd>
                </dl>
                <dl class="row">
                    <dt class="tit">
                        <label for="preheat">活动预热</label>
                    </dt>
                    <dd class="opt">
                        <div class="onoff">
                            <label for="preheat1" class="cb-enable {eq name='$info.preheat' value='1'}selected{/eq}">开启</label>
                            <label for="preheat0" class="cb-disable {eq name='$info.preheat' value='0'}selected{/eq}">关闭</label>
                            <input id="preheat1" name="preheat" value="1" type="radio" onclick="$('#preheat_time_dl').show();" {eq name='$info.preheat' value='1'}checked="checked"{/eq}>
                            <input id="preheat0" name="preheat" value="0" type="radio" onclick="$('#preheat_time_dl').hide();" {eq name='$info.preheat' value='0'}checked="checked"{/eq}>
                        </div>
                        <span class="err"></span>
                        <p class="notic">未达到活动开始时间的限时折扣抢购将以倒计时形式展示，此时只可浏览不可购买，未到预热时间不展示</p>
                    </dd>
                </dl>
                <dl class="row {eq name='$info.preheat' value='0'}none{/eq}" id="preheat_time_dl">
                    <dt class="tit">
                        <label for="preheat_time"><em>*</em>预热时间</label>
                    </dt>
                    <dd class="opt">
                        <div class="onoff">
                            <input type="text" class="input-txt" id="preheat_time" name="preheat_time" value="{$info.preheat_time|date='Y-m-d H:i:s',###}" autocomplete="off">
                        </div>
                        <span class="err"></span>
                        <p class="notic">预热时间不能大于开始时间</p>
                    </dd>
                </dl>
                <div class="bot">
                    <input type="hidden" name="active_id" value="{$info.active_id}">
                    <a href="JavaScript:void(0);" onclick="checkForm();" class="ncap-btn-big ncap-btn-green"
                       id="submitBtn">确认提交</a>
                </div>
            </form>
        </div>
    </div>
</div>
<script>
    function chooseUseType(obj){
        var val = $(obj).val();
        if ( 1 == val ){
            $("#num_2").attr('disabled',true);
            $("#num_3").attr('disabled',true);
        }else if( 2 == val ){
            $("#num_2").attr('disabled',false);
            $("#num_3").attr('disabled',true);
        }else if( 3 == val ){
            $("#num_3").attr('disabled',false);
            $("#num_2").attr('disabled',true);
        }
    }
    function reback(info) {
        layer.closeAll();
        if (info) {
            $("#goods_show_list").show();
            info.forEach(function (v) {
                v = JSON.parse(v, true)
                if ($('div').is('.goods_show_list_div_' + v.discount_gid) == false) {
                    var html = '';
                    html += '<tr class="tr goods_show_list_div goods_show_list_div_' + v.discount_gid + '">';
                    html += '    <td class="sort">';
                    html += '        <div class="w60 tc">';
                    html += '            ' + v.discount_gid;
                    html += '            <input type="hidden" name="goods[discount_gid][]" value="' + v.discount_gid + '">';
                    html += '            <input type="hidden" name="goods[aid][]" value="' + v.aid + '">';
                    html += '        </div>';
                    html += '    </td>';
                    html += '    <td class="w40">';
                    html += '        <div class="tc">';
                    html += '            <img width="40" height="40" src="' + v.litpic + '">';
                    html += '        </div>';
                    html += '    </td>';
                    html += '    <td class="" style="width: 100%;">';
                    html += '        <div class="tl" style="padding-left: 10px;">';
                    html += '            ' + v.title;
                    html += '        </div>';
                    html += '    </td>';
                    html += '    <td class="">';
                    html += '        <div class="w60 tc">';
                    html += '            <a class="btn red" href="javascript:void(0);" onclick="del(' + v.discount_gid + ');">删除</a>';
                    html += '        </div>';
                    html += '    </td>';
                    html += '</tr>';
                    $("#Template").append(html);
                    $("#noneGoods").hide();
                }
            })
        }
    }

    function del(sid) {
        $(".goods_show_list_div_" + sid).remove();
        if ($('.goods_show_list_div').length <= 0) {
            $("#goods_show_list").hide();
        }
    }

    function choGoods() {
        // var arr = [];
        // $('input[name^=goods]').each(function () {
        //     if ('goods[discount_gid][]' == $(this).attr('name')){
        //         arr.push($(this).val());
        //     }
        // })
        layer.open({
            type: 2,
            title: '选择限时折扣商品',
            fixed: true, //不固定
            shadeClose: false,
            shade: layer_shade,
            closeBtn: 3,
            maxmin: true, //开启最大化最小化按钮
            area: ['80%', '90%'],
            content: "{:url('Discount/goods_list')}"
        });
    }
</script>
<script type="text/javascript">
    layui.use(['form', 'laydate'], function(){
        var form = layui.form,
            laydate = layui.laydate;

        laydate.render({
            elem: '#end_date'
            ,type: 'datetime'
        });

        laydate.render({
            elem: '#preheat_time'
            ,type: 'datetime'
        });

        form.render();
    });

    var parentObj = parent.layer.getFrameIndex(window.name); //先得到当前iframe层的索引
    // 判断输入框是否为空
    function checkForm() {
        if($('.goods_show_list_div').length == 0){
            showErrorMsg('请先选择限时折扣商品！');
            $('input[name=seckill_price]').focus();
            return false;
        }
        layer_loading('正在处理');
        $.ajax({
            url : "{:url('Discount/active_edit')}",
            data: $('#post_form').serialize(),
            type: 'post',
            dataType: 'json',
            success: function(res) {
                layer.closeAll();
                if (res.code == 1) {
                    layer.msg(res.msg, {icon: 1, shade: layer_shade, time: 1000}, function(){
                        window.location.reload();
                    });
                } else {
                    showErrorAlert(res.msg);
                }
            },
            error: function(e) {
                layer.closeAll();
                showErrorAlert(e.responseText);
            }
        })
    }
</script>
{include file="public/footer" /}