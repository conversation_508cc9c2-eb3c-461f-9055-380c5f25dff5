{include file="public/layout" /}
<body style="background-color: rgb(255, 255, 255); overflow: auto; cursor: default; -moz-user-select: inherit;min-width: auto;">
<div id="append_parent"></div>
<div id="ajaxwaitid"></div>
<div class="page" style="min-width: auto;box-shadow:none; padding-bottom: 0px;">
    <!-- 操作说明 -->
    <div id="explanation" class="explanation">
        <div id="checkZoom" class="title"><i class="fa fa-lightbulb-o"></i>
            <h4 title="提示相关设置操作时应注意的要点">提示</h4>
            <span title="收起提示" id="explanationZoom" style="display: block;"></span>
        </div>
        <ul>
            <li>请每一行设置一个服务器名称。</li>
        </ul>
    </div>
    <form class="form-horizontal" id="post_form" method="post" onsubmit="return false;">
        <div class="ncap-form-default">
            <div class="ncap-form-default">
                <dl class="row">
                    <dt class="tit">
                        <label for="2">服务器列表</label>
                    </dt>
                    <dd class="opt" style="width: auto;">
                        <textarea name="servername" style="height: 150px;" id="2" cols="45" rows="20">{$servernames}</textarea>
                        <span class="err"></span>
                        <p class="notic"></p>
                    </dd>
                </dl>
                <div class="bot" style="padding-bottom:0px;">
                    <a href="JavaScript:void(0);" data-url="{:url('Download/set_servername')}" onclick="checkForm(this);" class="ncap-btn-big ncap-btn-green" id="submitBtn">确认提交</a>
                    <a href="javascript:history.go(-1)" style="font-size: 12px;padding-left: 10px;top: 30px">[返回]</a>
                </div>
            </div>
        </div>
    </form>
</div>
<script type="text/javascript">

    var parentObj = parent.layer.getFrameIndex(window.name); //先得到当前iframe层的索引

    function checkForm(th)
    {
        var url = $(th).attr('data-url');
        layer_loading('正在处理');
        $.ajax({
            url: url,
            type: 'POST',
            dataType: 'JSON',
            data: $('#post_form').serialize(),
            success: function(res){
                layer.closeAll();
                if (res.code == 1) {
                    showSuccessMsg(res.msg, 1000, function(){
                        window.history.back();
                    });
                } else {
                    showErrorAlert(res.msg);
                }
                return false;
            },
            error: function(e){
                layer.closeAll();
                showErrorAlert(e.responseText);
                return false;
            }
        });
    }

</script>
{include file="public/footer" /}