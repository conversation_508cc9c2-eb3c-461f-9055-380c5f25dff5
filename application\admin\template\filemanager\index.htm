{include file="public/layout" /}

<body class="bodystyle" style="cursor: default; -moz-user-select: inherit;">
<div id="append_parent"></div>
<div id="ajaxwaitid"></div>
<div class="page">
    {include file="filemanager/bar" /}
    <div class="flexigrid">
        
        <div class="hDiv">
            <div class="hDivBox">
                <table cellspacing="0" cellpadding="0" style="width: 100%">
                    <thead>
                    <tr>
                        <th abbr="" axis="col3" class="">
                            <div class="" style="padding-left:15px ">文件名</div>
                        </th>
                        <th abbr="" axis="col3" class="w200">
                            <div class="tc">文件大小</div>
                        </th>
                        <th abbr="" axis="col6" class="w100">
                            <div class="tc">更新时间</div>
                        </th>
                        <th axis="col1" class="w120">
                            <div class="tc">操作</div>
                        </th>
                    </tr>
                    </thead>
                </table>
            </div>
        </div>
        <div class="bDiv" style="height: auto;">
            <div id="flexigrid" cellpadding="0" cellspacing="0" border="0">
                <table style="width: 100%">
                    <tbody>
                    {empty name="list"}
                        <tr>
                            <td class="no-data" align="center" axis="col0" colspan="50">
                                <div class="no_row">
                                    <div class="no_pic"><img src="__SKIN__/images/null-data.png"></div>
                                </div>
                            </td>
                        </tr>
                    {else/}
                        {foreach name="list" item="vo" key="k" }
                        <tr>
                            <td style="width: 100%">
                                <div style="text-align: left; padding-left: 15px;">
                                  {if condition="in_array($vo['filetype'], array('dir','dir2'))"}
                                  <a href="{:url('Filemanager/index', array('activepath'=>replace_path($vo.filepath)))}"><img src="__SKIN__/images/{$vo.icon}" border="0" width="16" height="16" align="absmiddle" class="flt_u">&nbsp;{$vo.filename}</a>
                                  {else /}
                                    {notempty name="$vo.icon"}
                                    <img class="flt_u" src="__SKIN__/images/{$vo.icon}" border="0" width="16" height="16" align="absmiddle" {if condition="!empty($vo.filepath) AND 'image' == $vo.filemine"}onmouseover="layer_tips=layer.tips('<img src=__ROOT_DIR__{$vo.filepath}?v={php}echo time();{/php} class=\'layer_tips_img\'>',this,{tips: [3, '#fff'],skin:'layer-yourskin-mt0'});"{/if} onmouseout="layer.close(layer_tips);">
                                    {/notempty}
                                    &nbsp;
                                    {eq name="$vo['filemine']" value='image'}
                                        <a href="__ROOT_DIR__{$vo['filepath']}?v={php}echo time();{/php}" target="_blank">{$vo.filename}</a>
                                    {else /}
                                        <a href="{:url('Filemanager/edit',array('filename'=>$vo['filename'], 'activepath'=>replace_path(dirname($vo['filepath']))))}">{$vo.filename}</a>
                                    {/eq}
                                  {/if}
                                  {$vo.intro}
                                </div>
                            </td>
                            <td class="">
                                <div class="w200 tc">
                                  {$vo.filesize|default=''}
                                </div>
                            </td>
                            <td class="">
                                <div class="w100 tc">
                                  {notempty name="$vo.filetime"}
                                  {$vo.filetime|date='Y-m-d',###}
                                  {/notempty}
                                </div>
                            </td>
                            <td class="operation">
                                <div class="w120 tc">
                                  {eq name="$vo['filemine']" value='image'}
                                    {if condition="in_array($vo['filetype'], $replaceImgOpArr)"}
                                        {eq name="$Think.const.CONTROLLER_NAME.'@replace_img'|is_check_access" value="1"}
                                        <a href="{:url('Filemanager/replace_img',array('filename'=>$vo['filename'], 'activepath'=>replace_path(dirname($vo['filepath']))))}" class="btn blue">替换</a>
                                        {/eq}
                                    {/if}
                                  {else /}
                                    {if condition="in_array($vo['filetype'], $editOpArr)"}
                                        {eq name="$Think.const.CONTROLLER_NAME.'@edit'|is_check_access" value="1"}
                                        <a href="{:url('Filemanager/edit',array('filename'=>$vo['filename'], 'activepath'=>replace_path(dirname($vo['filepath']))))}" class="btn blue">编辑</a>
                                        {/eq}
                                    {/if}
                                  {/eq}
                                </div>
                            </td>
                        </tr>
                        {/foreach}
                    {/empty}
                    </tbody>
                </table>
            </div>
            <div class="iDiv" style="display: none;"></div>
        </div>
        <div class="tDiv">
            <div class="tDiv2">
                {eq name="'Filemanager@newfile'|is_check_access" value="1"}
                <div class="fbutton">
                    <a href="{:url('Filemanager/newfile', array('activepath'=>replace_path($activepath)))}" class="layui-btn layui-btn-primary">
                            <span class="red">新建文件</span>
                    </a>
                </div>
                {/eq}
				<div class="fbuttonr" style=" margin-right: 15px; ">
					<div class="total">
					   <h5>共有{$list|count}条数据</h5>
					</div>
				</div>
            </div>
            <div style="clear:both"></div>
        </div>
        <!--分页位置-->
    </div>
</div>
<form class="none" id="post_del" method="POST" action="">
  <input type="hidden" name="filename" value="">
  <input type="hidden" name="activepath" value="">
</form>
<script type="text/javascript">
    $(document).ready(function(){

        // 表格行点击选中切换
        $('#flexigrid > table>tbody >tr').click(function(){
            $(this).toggleClass('trSelected');
        });

        // 点击刷新数据
        $('.fa-refresh').click(function(){
            location.href = location.href;
        });
    });

    function delfilename(obj, formid) {
        layer.confirm('此操作不可恢复，确认删除？', {
                shade: layer_shade,
                area: ['480px', '190px'],
                move: false,
                title: '提示',
                btnAlign:'r',
                closeBtn: 3,
                btn: ['确定', '取消'] ,//按钮
                success: function () {
                      $(".layui-layer-content").css('text-align', 'left');
                  }
            }, function(){
                // 确定
                var form = $('#post_del');
                var filename = $(obj).attr('data-filename');
                var activepath = $(obj).attr('data-activepath');
                var url = $(obj).attr('data-url');
                $(form).find('input[name=filename]').val(filename);
                $(form).find('input[name=activepath]').val(activepath);
                $(form).attr('action', url);
                layer_loading('正在处理');
                $('#post_del').submit();
            }, function(index){
                layer.close(index);
                return false;// 取消
            }
        );
    }
</script>

{include file="public/footer" /}