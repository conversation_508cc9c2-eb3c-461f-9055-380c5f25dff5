{include file="public/layout" /}

<body class="bodystyle">
<div id="toolTipLayer" style="position: absolute; z-index: 9999; display: none; visibility: visible; left: 95px; top: 573px;"></div>
<div id="append_parent"></div>
<div id="ajaxwaitid"></div>
<div class="page min-hg-c-i">
    <form class="form-horizontal" id="post_form" action="{:url('Field/channel_edit')}" method="post">
        <!-- 常规选项 -->
        <div class="ncap-form-default tab_div_1">
            {if condition="!empty($type) && 'all' == $type"}
            <dl class="row">
                <dt class="tit">
                    <label><em>*</em>所属模型</label>
                </dt>
                <dd class="opt">
                    {$channeltype_list[$info.channel_id]['title']}
                </dd>
            </dl>
            {/if}
            <dl class="row">
                <dt class="tit">
                    <label for="title"><em>*</em>字段名称</label>
                </dt>
                <dd class="opt">
                    <input type="text" value="{$info.title|default=''}" name="title" id="title" class="input-txt">
                    <p class="notic"></p>
                </dd>
            </dl>
            <dl class="row">
                <dt class="tit">
                    <label for="name"><em>*</em>字段标识</label>
                </dt>
                <dd class="opt">
                    <input type="text" value="{$info.name|default=''}" name="name" id="name" placeholder="支持字母、数字和下划线，不能和别的标识值相同" class="input-txt" onkeyup="this.value=this.value.replace(/[^0-9a-zA-Z_]/g,'');" onbeforepaste="clipboardData.setData('text',clipboardData.getData('text').replace(/[^0-9a-zA-Z_]/g,''));">
                    <p class="notic">保持唯一性</p>
                </dd>
            </dl>
            <dl class="row">
                <dt class="tit">
                    <label for="dtype"><em>*</em>数据类型</label>
                </dt>
                <dd class="opt">
                    {volist name='$fieldtype_list' id='vo'}
                        <div style="width: 150px; float: left;">
                            <label><input type="radio" name="dtype" value="{$vo.name}" data-ifoption="{$vo.ifoption|default=0}" {eq name="$info.dtype" value="$vo.name"} checked="checked" {/eq} data-text="{$vo.title}">{$vo.title}</label>&nbsp;
                        </div>
                        {if condition="$i % 4 == 0"}<br/>{/if}
                    {/volist}
                    <input type="hidden" name="old_dtype" value="{$info.dtype}">
                    <span class="err"></span>
                    <p class="notic"></p>
                </dd>
            </dl>

            <div id='region_div' style="display: none;">
                <dl class="row">
                    <dt class="tit">
                        <label for="region"><em>*</em>区域选择</label>
                    </dt>
                    <dd class="opt">
                        <select id="province" onchange="GetRegionData(this,'province');">
                            <option value="-1">请选择</option>
                            {volist name='$Province' id='P_V'}
                                <option {if condition="$P_V.id == $region.parent_id"} selected {/if} value="{$P_V.id}">{$P_V.name}</option>
                            {/volist}
                        </select>

                        <span id='CityId'>
                            <select id="city" onchange="GetRegionData(this,'city');">
                                <option value="{$region.parent_id}">请选择</option>
                                {notempty name="$City"}
                                {volist name='$City' id='C_V'}
                                    <option {if condition="$C_V.id == $region.region_id"} selected {/if} value="{$C_V.id}">{$C_V.name}</option>
                                {/volist}
                                {/notempty}
                            </select>    
                        </span>

                        <span class="err"></span>
                        <p class="notic"></p>
                    </dd>
                    <input type="hidden" id="GetRegionDataUrl" value="{:url('Field/ajax_get_region_data')}">
                    <input type="hidden" name="region_data[region_id]" id="RegionId" value="{$region.region_id}">
                </dl>

                <dl class="row">
                    <dt class="tit">
                        <label><em>*</em>默认值</label>
                    </dt>
                    <dd class="opt">
                        <textarea rows="5" cols="60" name="region_data[region_names]" id="region_names" readonly='readonly' placeholder="这里会自动区域选择之后的下级区域列表" style="height:110px; background-color: #f5f5f5;">{$region.region_names}</textarea>
                        <span class="err"></span>
                        <p class="notic">这里会自动区域选择之后的下级区域列表</p>
                    </dd>
                    <input type="hidden" name="region_data[region_ids]" id='region_ids' value='{$region.region_ids}' style="width: 100%;">
                </dl>
                <dl class="row">
                    <dt class="tit">
                        <label>三级区域联动</label>
                    </dt>
                    <dd class="opt">
                        <div class="onoff">
                            <label for="SetType_1" class="cb-enable {eq name='$info.set_type' value='1'}selected{/eq}">是</label>
                            <input id="SetType_1" name="set_type" value="1" type="radio" onclick="change_set_type(this);">

                            <label for="SetType_0" class="cb-disable {empty name='$info.set_type'}selected{/empty}">否</label>
                            <input id="SetType_0" name="set_type" value="0" type="radio" onclick="change_set_type(this);">
                        </div>
                        &nbsp;
                        <span class="err"></span>
                        <p class="notic">是否开启三级联动</p>
                    </dd>
                </dl>
            </div>

            <dl class="row" id="dl_dfvalue">
                <dt class="tit">
                    <label id="label_dfvalue">默认值</label>
                </dt>
                <dd class="opt">
                    <textarea rows="5" cols="60" id="dfvalue" name="dfvalue" placeholder="如果定义字段类型为下拉框、单选项、多选项时，此处填写被选择的项目(用“,”分开，如“男,女,人妖”)。" style="height:60px;">{$info.dfvalue|default=''}</textarea>
                    <textarea rows="5" cols="60" name="old_dfvalue" style="display: none;">{$info.dfvalue|default=''}</textarea>
                    <span class="err"></span>
                    <p class="notic">1、如果定义字段类型为下拉框、单选项、多选项时，默认值为必填项，不支持反斜杠 \ ，可以用斜杠 / 代替。<br/>2、特殊符号会被过滤掉，比如：&、=、?等<br/>3、定义字段类型为多选项时，默认值最多为64项，超出则截取前64项</p>
                </dd>
            </dl>
            <dl class="row" id="dl_dfvalue_unit">
                <dt class="tit">
                    <label for="dfvalue_unit">数值单位</label>
                </dt>
                <dd class="opt">
                    <input type="text" value="{$info.dfvalue_unit|default=''}" name="dfvalue_unit" id="dfvalue_unit" placeholder="比如：元、个、件等等" class="input-txt">
                    <p class="notic"></p>
                </dd>
            </dl>
            <dl class="row" id='IsRelease' {eq name='$userConfig.users_open_release' value='0'} style="display: none;" {/eq}>
                <dt class="tit">
                    <label>支持投稿</label>
                </dt>
                <dd class="opt">
                    <label class="curpoin"><input id="IsRelease_1" name="is_release" value="1" type="radio" {if condition="$info.is_release == 1"}checked="checked"{/if}>是</label>
                    &nbsp;
                    <label class="curpoin"><input id="IsRelease_0" name="is_release" value="0" type="radio" {if condition="!isset($info.is_release) || empty($info.is_release)"}checked="checked"{/if}>否</label>
                    &nbsp;
                    <span class="err"></span>
                    <p class="notic">是否应用于会员投稿中</p>
                </dd>
            </dl>
            <dl class="row" id='IsScreening' style="display: none;">
                <dt class="tit">
                    <label>支持筛选</label>
                </dt>
                <dd class="opt">
                    <label class="curpoin"><input id="IsScreening_1" name="is_screening" value="1" type="radio" {if condition="$info.is_screening == 1"}checked="checked"{/if}>是</label>
                    &nbsp;
                    <label class="curpoin"><input id="IsScreening_0" name="is_screening" value="0" type="radio" {if condition="!isset($info.is_screening) || empty($info.is_screening)"}checked="checked"{/if}>否</label>
                    <input type="hidden" name="IsScreening_status" value="0">
                    &nbsp;
                    <span class="err"></span>
                    <p class="notic">是否应用于列表的条件筛选中</p>
                    <a id="call_tags_help" href="JavaScript:void(0);" onclick="click_to_eyou_1575506523('https://www.eyoucms.com/plus/view.php?aid=7881&origin_eycms=1','筛选标签调用')" class="{if condition='!isset($info.is_screening) || empty($info.is_screening)'}none{/if}">查看标签调用</a>
                </dd>
            </dl>
            <dl class="row">
                <dt class="tit">
                    <label>提示文字</label>
                </dt>
                <dd class="opt">          
                    <textarea rows="5" cols="60" id="remark" name="remark" placeholder="问号提示文字" style="height:60px;">{$info.remark|default=''}</textarea>
                    <span class="err"></span>
                    <p class="notic">问号提示文字</p>
                </dd>
            </dl>
            <dl class="row">
                <dt class="tit">
                    <label for="title" id="select_title">指定栏目</label>
                </dt>
                <dd class="opt">
                    <select name="typeids[]" id="typeid" style="width: 300px;" size="15" multiple="true">
                        <option value="0" {if condition="in_array(0, $typeids) OR empty($typeids)"}selected="true"{/if}>
                            —指定所有栏目—
                        </option>
                        {$select_html}
                    </select>
                    <span class="err"></span>
                    <p class="red">(按 Ctrl 可以进行多选)</p>
                </dd>
            </dl>
<!--             <dl class="row">
                <dt class="tit">
                    <label for="sort_order">排序</label>
                </dt>
                <dd class="opt">
                    <input type="text" value="{$info.sort_order|default=100}" name="sort_order" id="sort_order" class="input-txt">
                    <p class="notic">越小越靠前</p>
                </dd>
            </dl> -->
        </div>
        <!-- 常规选项 -->
        <div class="ncap-form-default">
            <div class="bot">
                <input type="hidden" name="channel_id" id="channel_id" value="{$info.channel_id|default=''}">
                <input type="hidden" name="id" id="id" value="{$info.id|default=''}">
                <input type="hidden" name="old_name" id="old_name" value="{$info.name|default=''}">
                <a href="JavaScript:void(0);" onclick="check_submit();" class="ncap-btn-big ncap-btn-green" id="submitBtn">确认提交</a>
            </div>
        </div> 
    </form>
</div>
<script type="text/javascript">
    function change_set_type(obj) {
        var val = $(obj).val();
        if (1 == val) {
            // $("#IsScreening_1").prop('checked', false);
            // $("#IsScreening_0").prop('checked', true);
            // $("#IsScreening").find('label').eq(1).removeClass('selected');
            // $("#IsScreening").find('label').eq(2).addClass('selected');
            // $("#IsScreening").hide();
            $("#call_tags_help").hide();
        } else {
            // $("#IsScreening").show();
        }
    }

    $(function(){
        // 判断是否隐藏第二级地区选择栏
        var parent_array = {$parent_array};
        if (parent_array) {
            var region_id = $('#RegionId').val();
            if (0 <= $.inArray(region_id, parent_array)) {
                $('#CityId').hide();
            }    
        }
        
        var screening_value = $('input:radio[name="is_screening"]:checked').val();
        $('#IsScreening_'+screening_value).click();

        dtype_change($('input[name=dtype]:checked'));
        $('input[name=dtype]').click(function(){
            /*多选项不能切换为指定的字段类型*/
            var dtype = $(this).val();
            var old_dtype = $('input[name=old_dtype]').val();
            if (('checkbox' == old_dtype && 0 <= $.inArray(dtype, ['radio','select'])) || 'media' == old_dtype) {
                $("input[name=dtype][value="+old_dtype+"]").attr("checked",'checked');
                var old_dtype_text = $("input[name=dtype][value="+old_dtype+"]").data('text');
                var dtype_text = $("input[name=dtype][value="+dtype+"]").data('text');
                showErrorMsg(old_dtype_text+"不能更改为"+dtype_text+"！");
                $(this).focus();
                return false;
            }
            /*end*/
            dtype_change(this);
        });

        function dtype_change(obj) {
            var dtype = $(obj).val();
            var ifoption = $(obj).data('ifoption');
            if (0 <= $.inArray(dtype, ['datetime','switch','img','imgs','file','media'])) {
                $('#dl_dfvalue').hide();
                ClearAreaData();
            } else if ('region' == dtype) {
                $('#region_div').show();
                $('#dl_dfvalue').hide();
            } else {
                $('#dl_dfvalue').show();
                ClearAreaData();
            }
            if (1 == ifoption) {
                $('#label_dfvalue').html('<em>*</em>默认值');
            } else {
                $('#label_dfvalue').html('默认值');
            }
            if (0 <= $.inArray(dtype, ['text','multitext'])) {
                $('#dfvalue').attr('placeholder', '前端将默认显示输入的默认值');
            } else if (0 <= $.inArray(dtype, ['checkbox','radio','select'])) {
                $('#dfvalue').attr('placeholder', '此处填写被选择的项目(用“,”分开，如“男,女”)。');
            } else {
                $('#dfvalue').attr('placeholder', '前端将默认显示输入的默认值');
            }
            if (0 <= $.inArray(dtype, ['region','checkbox','radio','select'])) {
                $('#IsScreening').show();
                $('input[name=IsScreening_status]').val(1);
            } else {
                $('#IsScreening').hide();
                $('input[name=IsScreening_status]').val(0);
                $('#select_title').html('指定栏目');
            }
            if (0 <= $.inArray(dtype, ['text','int','float','decimal'])) {
                $('#dl_dfvalue_unit').show();
            } else {
                $('#dl_dfvalue_unit').hide();
            }
            //如果开启了区域三级联动 隐藏用于筛选
            var set_type = "{$info.set_type|default=0}";
            if (1 == set_type && 'region' == dtype ) {
                // $("#IsScreening").hide();
            }
        }
    });

    // 当切换其他类型时清空所有关于区域选择的数据
    function ClearAreaData(){
        $('#region_div').hide();
        $('#RegionId').val('');
        $('#region_names').empty();
    }

    // 获取联动地址
    function GetRegionData(t,type){
        var parent_id = $(t).val();
        if(!parent_id){
            return false;
        }
        
        var url = $('#GetRegionDataUrl').val();
        $.ajax({
            url: url,
            data: {parent_id:parent_id,_ajax:1},
            type:'post',
            dataType:'json',
            success:function(res){
                // 判断是否隐藏第二级地区选择栏
                if (0 <= $.inArray(parent_id, res.parent_array)) {
                    $('#CityId').hide();
                }else{
                    $('#CityId').show();
                }
                // 加载城市名称数据到textarea
                $('#region_names').empty().html(res.region_names);
                // 加载城市ID数据到input
                $('#region_ids').val(res.region_ids);
                // 加载ID到input
                $('#RegionId').val(parent_id);
                // 输出下一级栏目选项
                if ('province' == type) {
                    res = '<option value='+parent_id+'>请选择</option>'+ res.region_html;
                    $('#city').empty().html(res);
                }
            },
            error : function(e) {
                layer.closeAll();
                layer.alert(e.responseText, {icon: 5});
            }
        });
    }

    $('#IsScreening_1').click(function(){
        $('#select_title').html('<em>*</em>指定栏目');
        $('#typeid').find('option:first').attr('disabled', true).css('display', 'none');
        $('#call_tags_help').show();
    });

    $('#IsScreening_0').click(function(){
        $('#select_title').html('指定栏目');
        $('#typeid').find('option:first').attr('disabled', false).css('display', '');
        $('#call_tags_help').hide();
    });

    var parentObj = parent.layer.getFrameIndex(window.name); //先得到当前iframe层的索引
    function check_submit(){
        if($('input[name="title"]').val() == ''){
            showErrorMsg('字段标题不能为空！');
            $('input[name=title]').focus();
            return false;
        }
        var name = $('input[name="name"]').val();
        var ret1 = /^[_]+$/;
        var ret2 = /^[\w]+$/;
        var ret3 = /^[0-9]+$/;
        if (ret1.test(name) || !ret2.test(name)) {
            showErrorMsg('字段名称格式不正确！');
            $('input[name=name]').focus();
            return false;
        } else if (ret3.test(name)) {
            showErrorMsg('字段名称不能纯数字！');
            $('input[name=name]').focus();
            return false;
        }
        if($('input[name=dtype]:checked').val() == ''){
            showErrorMsg('请选择字段类型！');
            $('input[name=dtype]').focus();
            return false;
        } else if ('region' == $('input[name=dtype]:checked').val()){
            if (-1 == $('#RegionId').val()) {
                showErrorMsg('请选择区域范围！');
                $('#province').focus();
                return false;
            }
        } else {
            var ifoption = $('input[name=dtype]:checked').data('ifoption');
            if (1 == ifoption) {
                if ($.trim($('#dfvalue').val()) == '') {
                    showErrorMsg('默认值不能为空！');
                    $('#dfvalue').focus();
                    return false;
                }

                var tag = '|';
                if($('#dfvalue').val().indexOf(tag) != -1){
               　　  showErrorMsg('默认值不能输入 | 符号！');
                    $('#dfvalue').focus();
                    return false;
            　　}
            }
            
            if (0 <= $.inArray($('input[name=dtype]:checked').val(), ['radio','checkbox','select'])) {
                var dfvalue = $.trim($('#dfvalue').val());
                if(dfvalue.indexOf('\\') != -1){
               　　  showErrorMsg('默认值不支持反斜杠 \\ ，可用斜杠 / 代替');
                    $('#dfvalue').focus();
                    return false;
            　　}
                data = dfvalue.split(',');
                for(var i = 0;i < data.length ;i++) {
                    for(var j = i+1;j < data.length;j++) {
                        if ($.trim(data[i]) == $.trim(data [j])){
                            showErrorMsg('默认值不能含有相同的值！');
                            $('#dfvalue').focus();
                            return false;
                        }
                    }
                }
                if (0 <= $.inArray($('input[name=dtype]:checked').val(), ['checkbox'])) {
                    if (64 < data.length) {
                        showErrorMsg('默认值最多填写64项！');
                        $('#dfvalue').focus();
                        return false;
                    }
                }
            }
        }
        if (0 >= parseInt($('#typeid').find('option:selected').length)) {
            showErrorMsg('请选择指定栏目！');
            $('#typeid').focus();
            return false;
        }

        // 字段支持筛选时，指定栏目必须指定一个或多个栏目
        if (1 == parseInt($('input:radio[name="is_screening"]:checked').val())) {
            if (0 == parseInt($('#typeid').find('option:selected').val())) {
                if (1 >= $('#typeid').val().length) {
                    showErrorMsg('字段支持筛选时，栏目必须指定一个或多个！');
                    $('#typeid').focus();
                    return false;
                }
            }
        }

        layer_loading('正在处理');
        $.ajax({
            type : 'post',
            url : "{:url('Field/channel_edit', ['_ajax'=>1])}",
            data : $('#post_form').serialize(),
            dataType : 'json',
            success : function(res){
                layer.closeAll();
                if(res.code == 1){
                    var _parent = parent;
                    _parent.layer.close(parentObj);
                    _parent.layer.msg(res.msg, {shade: layer_shade, time: 1000}, function(){
                        _parent.window.location.reload();
                    });
                }else{
                    showErrorMsg(res.msg);
                }
            },
            error: function(e){
                layer.closeAll();
                layer.alert(e.responseText, {icon: 5, title:false});
            }
        });
    }
</script>

{include file="public/footer" /}