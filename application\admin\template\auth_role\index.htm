{include file="public/layout" /}
<body class="bodystyle" style="cursor: default; -moz-user-select: inherit;">
<div id="append_parent"></div>
<div id="ajaxwaitid"></div>
<div class="page min-hg-c-10">
    {include file="admin/admin_bar" /}
    <div class="flexigrid">
        <div class="mDiv pt0">
            <div class="ftitle">
                {eq name="'AuthRole@add'|is_check_access" value="1"}
                <div class="fbutton">
                    <a href="javascript:void(0);" data-href="{:url('AuthRole/add')}" onclick="openFullframe(this, '新增角色', '100%', '100%');">
                        <div class="add">
                            <span><i class="layui-icon layui-icon-addition"></i>新增角色</span>
                        </div>
                    </a>
                </div>
                {/eq}
            </div>
            <form class="navbar-form form-inline" action="{:url('AuthRole/index')}" method="get" onsubmit="layer_loading('正在处理');">
                {$searchform.hidden|default=''}
                <div class="sDiv">
                    <div class="sDiv2">
                        <input type="text" size="30" name="keywords" value="{$Request.param.keywords}" class="qsbox" placeholder="搜索相关数据...">
                        <input type="submit" class="btn" value="搜索">
						<i class="iconfont e-sousuo"></i>
                    </div>
                </div>
            </form>
        </div>
        <div class="hDiv">
            <div class="hDivBox">
                <table cellspacing="0" cellpadding="0" style="width: 100%">
                    <thead>
                    <tr>
                        <th class="sign w40" axis="col0">
                            <div class="tc"><input type="checkbox" class="checkAll"></div>
                        </th>
                        <th abbr="article_show" axis="col5" class="w40">
                            <div class="tc">ID</div>
                        </th>
                        <th abbr="ac_id" axis="col4">
                            <div class="text-l10">用户组</div>
                        </th>
                        <th abbr="article_time" axis="col6" class="w100">
                            <div class="tc">更新时间</div>
                        </th>
                        <th axis="col1" class="w120">
                            <div class="tc">操作</div>
                        </th>
                    </tr>
                    </thead>
                </table>
            </div>
        </div>
        <div class="bDiv" style="height: auto;">
            <div id="flexigrid" cellpadding="0" cellspacing="0" border="0">
                <table style="width: 100%">
                    <tbody>
                    {empty name="list"}
                        <tr>
                            <td class="no-data" align="center" axis="col0" colspan="50">
                                <div class="no_row">
                                    <div class="no_pic"><img src="__SKIN__/images/null-data.png"></div>
                                </div>
                            </td>
                        </tr>
                    {else/}
                        {foreach name="list" item="vo" key="k" }
                        <tr>
                            <td class="sign">
                                <div class="w40 tc">
                                    {empty name="$vo.built_in"}
                                    <input type="checkbox" name="ids[]" value="{$vo.id}">
									{else /}
									<input disabled type="checkbox" name="ids[]" value="{$vo.id}">
                                    {/empty}
                                </div>
                            </td>
                            <td class="sort">
                                <div class="w40 tc">
                                    {$vo.id}
                                </div>
                            </td>
                            <td style="width: 100%">
                                <div class="text-l10">
                                    {eq name="'AuthRole@edit'|is_check_access" value="1"}
                                    <a href="javascript:void(0);" data-href="{:url('AuthRole/edit',array('id'=>$vo['id']))}" onclick="openFullframe(this, '编辑角色', '100%', '100%');">{$vo.name}</a>
                                    {else /}
                                    {$vo.name}
                                    {/eq}
                                </div>
                            </td>
                            <td class="">
                                <div class="w100 tc">
                                    {$vo.update_time|date='Y-m-d',###}
                                </div>
                            </td>
                            <td class="operation">
                                <div class="w120 tc">
                                    {eq name="'AuthRole@edit'|is_check_access" value="1"}
                                    <a href="javascript:void(0);" data-href="{:url('AuthRole/edit',array('id'=>$vo['id']))}" class="btn blue" onclick="openFullframe(this, '编辑角色', '100%', '100%');">编辑</a>
                                    {/eq}

                                    {eq name="'AuthRole@del'|is_check_access" value="1"}
                                    {empty name="$vo.built_in"}
									<i></i>
                                    <a class="btn red"  href="javascript:void(0)" data-url="{:url('AuthRole/del')}" data-id="{$vo.id}" onClick="delfun(this);">删除</a>
									{else /}
                                    <i></i>
									<a class="btn grey"  href="javascript:void(0)" data-id="{$vo.id}">删除</a>
                                    {/empty}
                                    {/eq}
                                </div>
                            </td>
                        </tr>
                        {/foreach}
                    {/empty}
                    </tbody>
                </table>
            </div>
            <div class="iDiv" style="display: none;"></div>
        </div>
        <div class="tDiv">
            <div class="tDiv2">
                <div class="fbutton checkboxall">
                    <input type="checkbox" class="checkAll">
                </div>
                {eq name="'AuthRole@del'|is_check_access" value="1"}
                <div class="fbutton">
                    <a onclick="batch_del(this, 'ids');" data-url="{:url('AuthRole/del')}" class="layui-btn layui-btn-primary">
                            <span>批量删除</span>
                    </a>
                </div>
                {/eq}
                {include file="public/page" /}
            </div>
            <div style="clear:both"></div>
        </div>
    </div>
</div>
<script type="text/javascript">
    $(function(){
        $('input[name*=ids]').click(function(){
            if ($('input[name*=ids]').length == $('input[name*=ids]:checked').length) {
                $('.checkAll').prop('checked','checked');
            } else {
                $('.checkAll').prop('checked', false);
            }
        });
        $('input[type=checkbox].checkAll').click(function(){
            $('input[type=checkbox]:not(:disabled)').prop('checked',this.checked);
        });
    });
    
    $(document).ready(function(){
        // 表格行点击选中切换
        $('#flexigrid > table>tbody >tr').click(function(){
            $(this).toggleClass('trSelected');
        });

        // 点击刷新数据
        $('.fa-refresh').click(function(){
            location.href = location.href;
        });

    });
</script>

{include file="public/footer" /}