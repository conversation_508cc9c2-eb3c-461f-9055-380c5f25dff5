
    <div class="fixed-bar">
        <div class="item-title">
            {in name="$Think.const.ACTION_NAME" value='channel_index'}
            <a class="back_xin" href="{:url('Channeltype/index')}" title="返回"><i class="iconfont e-fanhui"></i></a></a>
            {else /}
            <a class="back_xin" href="javascript:history.back();" title="返回"><i class="iconfont e-fanhui"></i></a>
            {/in}
            
            <ul class="tab-base nc-row">
                <li><a href="{:url('Channeltype/edit', array('id'=>$channel_id))}" class="tab"><span>编辑模型</span></a></li>
                <li><a href="{:url('Field/channel_index', array('channel_id'=>$channel_id))}" class="tab current"><span>内容字段</span></a></li>
            </ul>
        </div>
    </div>