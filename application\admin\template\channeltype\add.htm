{include file="public/layout" /}
<body class="bodystyle">
<div id="toolTipLayer" style="position: absolute; z-index: 9999; display: none; visibility: visible; left: 95px; top: 573px;"></div>
<div id="append_parent"></div>
<div id="ajaxwaitid"></div>
<div class="page min-hg-c-10">
    <form class="form-horizontal" id="post_form" action="{:url('Channeltype/add')}" method="post">
        <div class="ncap-form-default">
            <dl class="row">
                <dt class="tit">
                    <label for="title"><em>*</em>模型名称</label>
                </dt>
                <dd class="opt">
                    <input type="text" name="title" value="" id="title" class="input-txt">
                    <span class="err"></span>
                    <p class="notic"></p>
                </dd>
            </dl>
            <dl class="row">
                <dt class="tit">
                    <label for="nid"><em>*</em>模型标识</label>
                </dt>
                <dd class="opt">
                    <input type="text" name="nid" value="" id="nid" class="input-txt" onkeyup="this.value=this.value.replace(/[^a-z0-9]/g,'');" onbeforepaste="clipboardData.setData('text',clipboardData.getData('text').replace(/[^a-z0-9]/g,''));">
                    <span class="err"></span>
                    <p class="">与文档的模板相关连，建议由小写字母、数字组成，因为部份Unix系统无法识别中文文件。<br/>列表模板是：lists_模型标识.htm<br/>文档模板是：view_模型标识.htm</p>
                </dd>
            </dl>
            <dl class="row">
                <dt class="tit">
                    <label>文档标题重复</label>
                </dt>
                <dd class="opt">
                    <label class="curpoin"><input id="is_repeat_title1" name="is_repeat_title" value="1" type="radio" checked="checked">允许</label>
                    &nbsp;
                    <label class="curpoin"><input id="is_repeat_title0" name="is_repeat_title" value="0" type="radio">不允许</label>
                    <p class="notic">新增/编辑文档时，是否允许标题的重复</p>
                </dd>
            </dl>
            <div class="bot">
                <a href="JavaScript:void(0);" onclick="checkForm();" class="ncap-btn-big ncap-btn-green" id="submitBtn">确认提交</a>
            </div>
        </div>
    </form>
</div>
<script type="text/javascript">
    var parentObj = parent.layer.getFrameIndex(window.name); //先得到当前iframe层的索引
    // 判断输入框是否为空
    function checkForm(){
        if($.trim($('input[name=title]').val()) == ''){
            showErrorMsg('模型名称不能为空！');
            $('input[name=title]').focus();
            return false;
        }
        var nid = $.trim($('input[name=nid]').val());
        if(nid == ''){
            showErrorMsg('模型标识不能为空！');
            $('input[name=nid]').focus();
            return false;
        } else {
            var reg = /^([a-z]+)([a-z0-9]*)$/i;
            if(!reg.test(nid)){
                showErrorMsg('模型标识必须以小写字母开头！');
                $('input[name=nid]').focus();
                // return false;
            }
        }

        layer_loading('正在处理');
        $.ajax({
            type : 'post',
            url : "{:url('Channeltype/add', ['_ajax'=>1])}",
            data : $('#post_form').serialize(),
            dataType : 'json',
            success : function(res){
                layer.closeAll();
                if(res.code == 1){
                    var _parent = parent;
                    _parent.layer.close(parentObj);
                    _parent.layer.msg(res.msg, {shade: layer_shade, time: 1000}, function(){
                        _parent.window.location.reload();
                    });
                }else{
                    showErrorMsg(res.msg);
                }
            },
            error: function(e){
                layer.closeAll();
                layer.alert(e.responseText, {icon: 5, title:false});
            }
        });
    }
</script>
{include file="public/footer" /}