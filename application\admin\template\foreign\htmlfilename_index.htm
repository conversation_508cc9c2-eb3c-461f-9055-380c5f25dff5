{include file="public/layout" /}
<body class="bodystyle" style="cursor: default; -moz-user-select: inherit;">
<div id="append_parent"></div>
<div id="ajaxwaitid"></div>
<div class="page min-hg-c-10">
    {include file="foreign/bar" /}
    <form class="form-horizontal" id="post_form" method="post">
        <div class="ncap-form-default">
            <dl class="row">
                <dt class="tit">
                    <label>更新文档URL</label>
                </dt>
                <dd class="opt">
                    <label class="curpoin"><input type="radio" name="foreign_htmlfilename_mode" value="0" {if condition="empty($foreignData.foreign_htmlfilename_mode)"} checked="checked" {/if} onclick="select2Format(this);">外贸文档名</label>&nbsp;&nbsp;
                    <label class="curpoin"><input type="radio" name="foreign_htmlfilename_mode" value="1" {if condition="!empty($foreignData.foreign_htmlfilename_mode)"} checked="checked" {/if} onclick="select2Format(this);">ID文档名</label>&nbsp;&nbsp;
                    <span class="err"></span>
                    <p class="notic2 {if condition="!empty($foreignData.foreign_htmlfilename_mode)"} none {/if}" id="notic_foreign_htmlfilename_mode_0">以文档标题的中文拼音或英文作为结尾文件名，示例：{$Request.domain}__ROOT_DIR__/news/wenzhangbiaoti.html</p>
                    <p class="notic2 {if condition="empty($foreignData.foreign_htmlfilename_mode)"} none {/if}" id="notic_foreign_htmlfilename_mode_1">以文档ID作为结尾文件名，示例：{$Request.domain}__ROOT_DIR__/news/123.html</p>
                </dd>
            </dl>
            <div class="bot">
                <a class="ncap-btn-big ncap-btn-green" type="button" id="submitBtn" onclick="handle_archives(this);">开始更新</a>
            </div>
        </div>
    </form>
</div>
<script type="text/javascript">
    function select2Format(obj)
    {
        $('#notic_foreign_htmlfilename_mode_0').hide();
        $('#notic_foreign_htmlfilename_mode_1').hide();
        if ($('input[name=foreign_htmlfilename_mode]:checked').val() == 0) {
            $('#notic_foreign_htmlfilename_mode_0').show();
        } else {
            $('#notic_foreign_htmlfilename_mode_1').show();
        }
    }

    /**
     * 批量更新URL
     */
    function handle_archives(obj)
    {
        var foreign_htmlfilename_mode = $('input[name=foreign_htmlfilename_mode]:checked').val();
        var confirm = layer.confirm('批量更新文档成外贸链接，是否更新？', {
            shade: layer_shade,
            area: ['480px', '190px'],
            move: false,
            title: '提示',
            btnAlign:'r',
            closeBtn: 3,
            btn: ['确认', '取消'], //按钮
            success: function () {
                $(".layui-layer-content").css('text-align', 'left');
            }
        }, function () {
            layer.close(confirm);
            var url = "{:url('Foreign/htmlfilename_handel')}";
            if (url.indexOf('?') > -1) {
                url += '&';
            } else {
                url += '?';
            }
            url += 'foreign_htmlfilename_mode='+foreign_htmlfilename_mode;
            var index = layer.open({type: 2,title: '开始更新',area: ['500px', '300px'],fix: false, maxmin: false,content: url});
        }, function (index) {
            layer.closeAll(index);
        });
    }
</script>
{include file="public/footer" /}