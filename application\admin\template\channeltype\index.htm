{include file="public/layout" /}

<body class="bodystyle" style="cursor: default; -moz-user-select: inherit;">
<div id="append_parent"></div>
<div id="ajaxwaitid"></div>
<div class="page min-hg-c-10">
    {include file="channeltype/bar" /}
    <div class="flexigrid">
        <div class="mDiv pt0">
            <div class="ftitle">
                {eq name="'Channeltype@add'|is_check_access" value="1"}
                <div class="fbutton">
                    <a href="javascript:void(0);" data-href="{:url('Channeltype/add')}" onclick="openFullframe(this, '新增模型', '80%', '80%');">
                        <div class="add">
                            <span><i class="layui-icon layui-icon-addition"></i>新增模型</span>
                        </div>
                    </a>
                </div>
                {/eq}
            </div>
        </div>
        <div class="hDiv">
            <div class="hDivBox">
                <table cellspacing="0" cellpadding="0" style="width: 100%">
                    <thead>
                    <tr>
                        <th abbr="article_title" axis="col3" class="w40">
                            <div class="tc">ID</div>
                        </th>
                        <th abbr="article_title" axis="col3" class="">
                            <div class="text-l10">模型名称</div>
                        </th>
                        <th abbr="article_time" axis="col6" class="w120">
                            <div class="tc">模型标识</div>
                        </th>
                        <th abbr="article_time" axis="col6" class="w80">
                            <div class="tc">字段数</div>
                        </th>
                        <th abbr="article_time" axis="col6" class="w50">
                            <div class="tc">启用</div>
                        </th>
                        <th axis="col1" id="th_handle" class="w150">
                            <div class="tc">操作</div>
                        </th>
                    </tr>
                    </thead>
                </table>
            </div>
        </div>
        <div class="bDiv" style="height: auto;">
            <div id="flexigrid" cellpadding="0" cellspacing="0" border="0">
                <table style="width: 100%;">
                    <tbody>
                    {empty name="list"}
                        <tr>
                            <td class="no-data" align="center" axis="col0" colspan="50">
                                <div class="no_row">
                                    <div class="no_pic"><img src="__SKIN__/images/null-data.png"></div>
                                </div>
                            </td>
                        </tr>
                    {else/}
                        {foreach name="list" item="vo" key="k" }
                        <tr>
                            <td class="sort">
                                <div class="tc w40">
                                    {$vo.id}
                                </div>
                            </td>
                            <td class="" style="width: 100%;">
                                <div class="tl text-l10">
                                    {eq name="'Channeltype@edit'|is_check_access" value="1"}
                                        {eq name="$vo.nid" value="ask"}
                                            <a href="javascript:void(0);" data-href="{:url('Ask/index')}" onclick="openFullframe(this, '问答模型', '100%', '100%');">{$vo.ntitle}</a>
                                        {else /}
                                            <a href="{:url('Channeltype/edit',array('id'=>$vo['id']))}" >{$vo.title}</a>
                                        {/eq}
                                    {else /}
                                        {$vo.ntitle}
                                    {/eq}
                                </div>
                            </td>
                            <td class="">
                                <div class="w120 tc">{$vo.nid}</div>
                            </td>
                            <td class="">
                                <div class="w80 tc">{$channelfieldRow[$vo['id']]['num']|default='0'}</div>
                            </td>
                            <td>
                                <div class="tc w50">
                                {eq name="'Channeltype@edit'|is_check_access" value="1"}
                                    {if condition="$vo['status'] eq 1"}
                                        <span class="yes" data-id="{$vo.id}" data-status="{eq name='$vo.status' value='1'}0{else /}1{/eq}" data-title="{$vo.title}" onClick="handleShow(this);"><i class="fa fa-check-circle"></i>是</span>
                                    {else /}
                                        <span class="no" data-id="{$vo.id}" data-status="{eq name='$vo.status' value='1'}0{else /}1{/eq}" data-title="{$vo.title}" onClick="handleShow(this);"><i class="fa fa-ban"></i>否</span>
                                    {/if}
                                {else /}
                                    {if condition="$vo['status'] eq 1"}是{else /}否{/if}
                                {/eq}
                                </div>
                            </td>
                            <td class="operation">
                                <div class="tc w150">
                                    {eq name="$vo.nid" value="ask"}
                                        <a href="javascript:void(0);" data-href="{:url('Ask/index')}" onclick="openFullframe(this, '问答模型', '100%', '100%');" class="btn blue">管理</a>
                                        <i></i>
                                        <a class="btn grey" href="javascript:void(0);">字段</a>
                                    {else /}
                                        {eq name="'Channeltype@edit'|is_check_access" value="1"}
                                            <a href="{:url('Channeltype/edit',array('id'=>$vo['id']))}" class="btn blue" >编辑</a>
                                        {/eq}

                                        {eq name="'Channeltype@edit'|is_check_access" value="1"}
                                            {eq name="$vo.nid" value="guestbook"}
                                            <i></i>
                                            <a href="{:url('Field/attribute_index')}" class="btn blue">字段</a>
                                            {else /}
                                            <i></i>
                                            <a href="{:url('Field/channel_index',array('channel_id'=>$vo['id']))}" class="btn blue">字段</a>
                                            {/eq}
                                        {/eq}
                                    {/eq}

                                    {empty name="$vo.ifsystem"}
                                        {eq name="'Channeltype@del'|is_check_access" value="1"}
                                        <i></i>
                                        <a class="btn red"  href="javascript:void(0);" data-url="{:url('Channeltype/del')}" data-id="{$vo.id}" data-deltype="pseudo" onClick="delfun(this);">删除</a>
                                        {/eq}
                                    {else /}
                                        <i></i>
                                        <a class="btn grey"  href="javascript:void(0);">删除</a>
                                    {/empty}
                                </div>
                            </td>
                            <td align="" class="" style="width: 100%;">
                                <div>&nbsp;</div>
                            </td>
                        </tr>
                        {/foreach}
                    {/empty}
                    </tbody>
                </table>
            </div>
            <div class="iDiv" style="display: none;"></div>
        </div>
        <div class="tDiv">
            <div class="tDiv2">
                {include file="public/page" /}
            </div>
            <div style="clear:both"></div>
        </div>
        <!--分页位置-->
       
    </div>
</div>
<script>
    $(document).ready(function(){
        // 表格行点击选中切换
        $('#flexigrid > table>tbody >tr').click(function(){
            $(this).toggleClass('trSelected');
        });

        // 点击刷新数据
        $('.fa-refresh').click(function(){
            location.href = location.href;
        });
    });

    function delfun(obj){
        var title = '<font color="#ff0000">重要提示！</font>';
        layer.confirm('确认删除？此操作将会删除与该模型所有相关的数据且不可恢复。', {
            shade: layer_shade,
            area: ['480px', '190px'],
            move: false,
            title: '提示',
            btnAlign:'r',
            closeBtn: 3,
            btn: ['确定', '取消'] ,//按钮
            success: function () {
                  $(".layui-layer-content").css('text-align', 'left');
              }
        }, function(){
            layer_loading('正在处理');
            // 确定
            $.ajax({
                type : 'post',
                url : $(obj).attr('data-url'),
                data : {del_id:$(obj).attr('data-id'), _ajax:1},
                dataType : 'json',
                success : function(res){
                    layer.closeAll();
                    if(res.code == 1){
                        layer.msg(res.msg, {icon: 1});
                        window.location.reload();
                    }else{
                        showErrorAlert(res.msg);
                    }
                },
                error:function(e){
                    showErrorAlert(e.responseText);
                }
            })
        }, function(index){
          layer.close(index);
        });
      return false;
    }

    function handleShow(obj){
        var title = $(obj).attr('data-title');
        var channel_id = $(obj).attr('data-id');
        var status = $(obj).attr('data-status');
        $.ajax({
            type : 'post',
            url : "{:url('Channeltype/ajax_show')}",
            data : {id:channel_id,status:status, _ajax:1},
            dataType : 'json',
            success : function(res){

                if (res.code == 1) {

                    if (0 == res.data.confirm) {
                        if (channel_id == 5){
                            if(status == 1){
                                parent.$("#sub-menu").find("#Member_media_index").show();
                            }else{
                                parent.$("#sub-menu").find("#Member_media_index").hide();
                            }
                        }
                        layer.msg(res.msg, {icon: 1, time:500}, function(){
                            window.location.reload();
                        });
                    } else {
                        var confirm = layer.confirm(res.msg, {
                            title: false,
                            btn: ['启用','取消'] //按钮
                        }, function(index){
                            layer.close(index);
                            // 确定
                            if (51 == channel_id) {
                                layer_loading('正在下载');
                                $.ajax({
                                    type : 'post',
                                    url : "{:url('Channeltype/ajax_syn_theme_ask', ['_ajax'=>1])}",
                                    data : {id:channel_id,status:$(obj).attr('data-status'),_ajax:1},
                                    dataType : 'json',
                                    success : function(res){
                                        layer.closeAll();
                                        if(res.code == 1){
                                            layer.msg(res.msg, {icon: 1, time: 500}, function(){
                                                window.location.reload();
                                            });
                                        }else{
                                            var icon = 5;
                                            if (res.data.icon) {icon = res.data.icon;}
                                            layer.alert(res.msg, {icon: icon, title:false, closeBtn:false}, function(){
                                                window.location.reload();
                                            });
                                        }
                                    },
                                    error:function(e){
                                        showErrorAlert(e.responseText);
                                    }
                                })
                            } else {
                                layer_loading('正在处理');
                                $.ajax({
                                    type : 'post',
                                    url : "{:url('Channeltype/ajax_check_tpl')}",
                                    data : {id:channel_id,status:$(obj).attr('data-status'),tpltype:res.data.tpltype, _ajax:1},
                                    dataType : 'json',
                                    success : function(res){
                                        layer.closeAll();
                                        if(res.code == 1){
                                            layer.msg(res.msg, {icon: 1, time: 500}, function(){
                                                window.location.reload();
                                            });
                                        }else{
                                            showErrorAlert(res.msg);
                                        }
                                    },
                                    error:function(e){
                                        showErrorAlert(e.responseText);
                                    }
                                })
                            }
                        });
                    }
                } else {
                    var icon = 5;
                    try {
                        if (res.data.icon) {
                            icon = res.data.icon;
                        }
                    }catch(err){}
                    showErrorAlert(res.msg, icon);
                }
            },
            error:function(e){
                showErrorAlert(e.responseText);
            }
        });
    }
</script>

{include file="public/footer" /}