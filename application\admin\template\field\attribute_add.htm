{include file="public/layout" /}
<body class="bodystyle">
<div id="toolTipLayer" style="position: absolute; z-index: 9999; display: none; visibility: visible; left: 95px; top: 573px;"></div>
<div id="append_parent"></div>
<div id="ajaxwaitid"></div>
<div class="page" style="min-width: auto;box-shadow:none;">
    <form class="form-horizontal" id="post_form" action="{:url('Field/attribute_add')}" method="post" onsubmit="return false;">
        <div class="ncap-form-default">
            <dl class="row">
                <dt class="tit">
                    <label for="typeid"><em>*</em>所属栏目</label>
                </dt>
                <dd class="opt"> 
                    {empty name="$typeid"}
                    <select name="typeid" id="typeid" onchange="showlist(this)">
                        <option value="0">请选择栏目…</option>
                        {$select_html}
                    </select>
                    {else /}
                    {$select_html}
                    <input type="hidden" name="typeid" id="typeid" value="{$typeid|default=''}">
                    {/empty}
                    <span class="err" id="err_typeid" style="color:#F00; display:none;"></span>
                    <p class="notic"></p>
                </dd>
            </dl>
            <dl class="row">
                <dt class="tit">
                    <label for="ac_name"><em>*</em>字段名称</label>
                </dt>
                <dd class="opt">
                    <input type="text" name="attr_name" value="" id="attr_name" class="input-txt">
                    <span class="err" id="err_attr_name" style="color:#F00; display:none;"></span>
                    <p class="notic"></p>
                </dd>
            </dl>
            <dl class="row">
                <dt class="tit">
                    <label for="attr_input_type"><em>*</em>字段类型</label>
                </dt>
                <dd class="opt">
                    {volist name='$attrInputTypeArr' id='vo'}
                        <div style="width: 150px; float: left;">
                            <label><input type="radio" name="attr_input_type" value="{$key}" {eq name='$i' value='1'} checked="checked" {/eq}>{$vo}</label>&nbsp;
                        </div>
                        {if condition="$i % 4 == 0"}<br/>{/if}
                    {/volist}
                    <span class="err" id="err_attr_input_type" style="color:#F00; display:none;"></span>
                    <p class="notic"></p>
                    <p class="none red" id="validate_type"><br/>前台表单输入框的格式验证标签调用，<a href="javascript:void(0);" onclick="click_to_eyou_1575506523('https://www.eyoucms.com/plus/view.php?aid=8973&origin_eycms=1','标签调用');">请点击这里查看教程</a></p>
                </dd>
            </dl>
            <dl class="row none attr_input_type2">
                <dt class="tit">
                    <label for="attr_values"><em>*</em>可选值列表</label>
                </dt>
                <dd class="opt">
                    <textarea rows="10" cols="30" name="attr_values" id="attr_values" class="input-txt" style="height:100px;" placeholder="一行代表一个可选值" onkeyup="this.value=this.value.replace(/[\|]/g,'');" onpaste="this.value=this.value.replace(/[\|]/g,'');"></textarea>
                    <span id="err_attr_values" class="err" style="color:#F00; display:none;"></span>
                    <p class="notic">一行代表一个可选值</p>
                </dd>
            </dl>
            <dl class="row none" id="show_list">
                <dt class="tit">
                    <label>后台列表显示</label>
                </dt>
                <dd class="opt">
                    <div class="onoff">
                        <label for="is_showlist1" id="is_showlist1_label" class="cb-enable" data-is_showlist="1" onclick="ajax_attribute_show(this);">显示</label>
                        <input id="is_showlist1" name="is_showlist" value="1" type="radio">

                        <label for="is_showlist0" id="is_showlist0_label" class="cb-disable selected" data-is_showlist="0" onclick="ajax_attribute_show(this);">隐藏</label>
                        <input id="is_showlist0" name="is_showlist" value="0" type="radio" checked="checked">
                    </div>
                    &nbsp;
                    <span class="err"></span>
                    <p class="notic">默认在列表显示前4个字段，全部字段可点击查看</p>
                </dd>
            </dl>
            <div id='region_div' style="display: none;">
                <dl class="row">
                    <dt class="tit">
                        <label for="region"><em>*</em>区域选择</label>
                    </dt>
                    <dd class="opt">
                        <select id="province" onchange="GetRegionData(this,'province');">
                            <option value="-1">请选择</option>
                            {volist name='$Province' id='P_V'}
                            <option value="{$P_V.id}">{$P_V.name}</option>
                            {/volist}
                        </select>

                        <span id='CityId'>
                            <select id="city" onchange="GetRegionData(this,'city');">
                                <option value="">请选择</option>
                            </select>
                            </span>
                        <span class="err"></span>
                        <p class="notic"></p>
                    </dd>
                    <input type="hidden" id="GetRegionDataUrl" value="{:url('Field/ajax_get_region_data')}">
                    <input type="hidden" name="region_data[region_id]" id="RegionId" value="-1">
                </dl>

                <dl class="row">
                    <dt class="tit">
                        <label><em>*</em>默认值</label>
                    </dt>
                    <dd class="opt">
                        <textarea rows="5" cols="60" name="region_data[region_names]" id="region_names" readonly='readonly' placeholder="这里会自动区域选择之后的下级区域列表" style="height:110px; background-color: #f5f5f5;">{$region.region_names}</textarea>
                        <span class="err"></span>
                        <p class="notic">这里会自动区域选择之后的下级区域列表</p>
                    </dd>
                    <input type="hidden" name="region_data[region_ids]" id='region_ids' value='{$region.region_ids}' style="width: 100%;">
                </dl>
            </div>

            <dl class="row" id="dl_dfvalue" style="display: none;">
                <dt class="tit">
                    <label id="label_dfvalue">默认值</label>
                </dt>
                <dd class="opt">
                    <textarea rows="5" cols="60" id="dfvalue" name="dfvalue" placeholder="如果定义字段类型为下拉框、单选项、多选项时，此处填写被选择的项目(用“,”分开，如“男,女,人妖”)。" style="height:60px;"></textarea>
                    <span class="err"></span>
                    <p class="notic">1、如果定义字段类型为下拉框、单选项、多选项时，此处填写被选择的项目(用“,”分开，如“男,女,人妖”)。<br/>2、特殊符号会被过滤掉，比如：&、=、?等</p>
                </dd>
            </dl>
            <dl class="row">
                <dt class="tit">
                    <label>前台必填</label>
                </dt>
                <dd class="opt">
                    <div class="onoff">
                        <label for="required1" class="cb-enable" >是</label>
                        <input id="required1" name="required" value="1" type="radio">

                        <label for="required0" class="cb-disable selected">否</label>
                        <input id="required0" name="required" value="0" type="radio" checked="checked">
                    </div>
                    &nbsp;
                    <span class="err"></span>
                    <p class="notic">用于控制前台用户提交是否必填验证</p>
                </dd>
            </dl>
            <dl class="row" id="realValidate" style="display: none;">
                <dt class="tit"> <label>真实验证</label> </dt>
                <dd class="opt">
                    <div class="onoff">
                        <label for="real_validate1" class="cb-enable" >是</label>
                        <input id="real_validate1" name="real_validate" value="1" type="radio">
                        <label for="real_validate0" class="cb-disable selected">否</label>
                        <input id="real_validate0" name="real_validate" value="0" type="radio" checked="checked">
                    </div>
                    &nbsp;
                    <span class="err"></span>
                    <p class="notic">用于控制前台用户提交的内容是否进行真实验证</p>
                    <p style="line-height: 1;" class="red {if condition="!in_array($field.attr_input_type, [6,7])"} none {/if}" id="real_validate_type"><br/>前台表单手机验证码调用标签，<a href="javascript:void(0);" onclick="click_to_eyou_1575506523('https://www.eyoucms.com/plus/view.php?aid=28608&origin_eycms=1','标签调用');">请点击这里查看教程</a></p>
                </dd>
            </dl>
            <div class="bot" style="padding-bottom: 0px;">
                <a href="JavaScript:void(0);" onclick="check_submit('post_form');"  class="ncap-btn-big ncap-btn-green" id="submitBtn">确认提交</a>
            </div>
        </div>
    </form>
</div>
<script type="text/javascript">
    var parentObj = parent.layer.getFrameIndex(window.name); //先得到当前iframe层的索引

    $(function(){
        if (0 == $('#RegionId').val() || -1 == $('#RegionId').val()) {
            $('#CityId').hide();
        }

        $('input[name=attr_input_type]').click(function(){
            var val = parseInt($(this).val());
            $('#validate_type').hide();
            $('#real_validate_type').hide();
            $('#realValidate').hide();
            if (-1 < $.inArray(val, [1,3,4])) {
                $('.attr_input_type2').show();
            } else {
                $('.attr_input_type2').hide();
                if (-1 < $.inArray(val, [6,7])) {
                    $('#validate_type').show();
                    $('#real_validate_type').show();
                    // 如果点击的是手机号码字段则执行
                    if (val == 6) $('#realValidate').show();
                }
            }
            if (9 == val) {
                $('#region_div').show();
                $('#dl_dfvalue').hide();
            } else {
                $('#dl_dfvalue').show();
                ClearAreaData();
            }
        });
    });

    // 当切换其他类型时清空所有关于区域选择的数据
    function ClearAreaData(){
        $('#region_div').hide();
        $('#RegionId').val('');
        $('#region_dfvalue').empty();
    }

    // 获取联动地址
    function GetRegionData(t,type){
        var parent_id = $(t).val();
        if(!parent_id){
            return false;
        }

        var url = $('#GetRegionDataUrl').val();
        $.ajax({
            url: url,
            data: {parent_id:parent_id,_ajax:1},
            type:'post',
            dataType:'json',
            success:function(res){
                // 判断是否隐藏第二级地区选择栏
                if (0 <= $.inArray(parent_id, res.parent_array)) {
                    $('#CityId').hide();
                }else{
                    $('#CityId').show();
                }
                // 加载城市名称数据到textarea
                $('#region_names').empty().html(res.region_names);
                // 加载城市ID数据到input
                $('#region_ids').val(res.region_ids);
                // 加载ID到input
                $('#RegionId').val(parent_id);
                // 输出下一级栏目选项
                if ('province' == type) {
                    res = '<option value='+parent_id+'>请选择</option>'+ res.region_html;
                    $('#city').empty().html(res);
                }
            },
            error : function(e) {
                layer.closeAll();
                layer.alert(e.responseText, {icon: 5});
            }
        });
    }
    /**
    * ajax 提交字段 到后台去验证然后回到前台提示错误
    * 验证通过后,再通过 form 自动提交
    */
    function check_submit(form_id)
    {
        if ($('#typeid').val() == 0) {
            showErrorMsg('请选择栏目…！');
            $('#typeid').focus();
            return false;
        }
        if($.trim($('input[name=attr_name]').val()) == ''){
            showErrorMsg('字段名称不能为空！');
            $('input[name=attr_name]').focus();
            return false;
        }
        var attr_input_type = parseInt($('input[name=attr_input_type]:checked').val());
        if (-1 < $.inArray(attr_input_type, [1,3,4]) && $.trim($('#attr_values').val()) == '') {
            showErrorMsg('可选值列表不能为空！');
            $('#attr_values').focus();
            return false;
        }

        layer_loading('正在处理');
        $.ajax({
            type : "POST",
            url  : "{:url('Field/attribute_add', ['_ajax'=>1])}",
            data : $('#'+form_id).serialize(),// 你的formid
            dataType: "JSON",
            error: function(e) {
                layer.closeAll();
                layer.alert(e.responseText, {icon: 5, title:false});
                return false;
            },
            success: function(res) {
                layer.closeAll();
                if(res.code == 1)
                {
                    if(!res.data.url){
                        layer.msg(res.msg, {shade: layer_shade, time: 1000}, function(){
                            window.location.reload();
                        });
                    }else{
                        var _parent = parent;
                        _parent.layer.close(parentObj);
                        _parent.layer.msg(res.msg, {shade: layer_shade, time: 1000}, function(){
                            _parent.gourl(res.data.url);
                        });
                    }
                    return true;
                } else {     
                    showErrorMsg(res.msg);                       
                    return false;
                }
            }
        });   
    }

    function showlist(obj) {
        if ($(obj).val() == 0) {
            $("#show_list").hide();
        } else {
            $("#show_list").show();
        }
    }

    function ajax_attribute_show(obj) {
        var is_showlist = $(obj).attr('data-is_showlist');
        var typeid = $('#typeid').val();
        if (typeid == 0) {
            showErrorMsg('请选择栏目…！');
            $('#typeid').focus();
            return false;
        }
        $.ajax({
            type : 'post',
            url : "{:url('Field/ajax_attribute_show')}",
            data : {typeid:typeid,is_showlist:is_showlist, _ajax:1},
            dataType : 'json',
            success : function(res){
                if (res.code != 1) {
                    $('#is_showlist1_label').removeClass('selected');
                    $('#is_showlist0_label').addClass('selected');//添加一个class
                    $("input:radio[name=is_showlist]").eq(0).attr("checked",false);
                    $("input:radio[name=is_showlist]").eq(1).attr("checked",true);
                    layer.alert(res.msg, {icon: 2, title:false});
                }
            },
            error:function(e){
                layer.alert(e.responseText, {icon: 2, title:false});
            }
        });
    }
</script>

{include file="public/footer" /}